import { BaseGraphQLError } from '@clinico/base-error';
import * as Sentry from '@sentry/node';
import { MiddlewareFn } from 'type-graphql';
import util from 'util';

import { logger } from '@packages/logger';

import { Context } from '~/utils/interfaces/apolloContext.interface';

import { version as ApiVersion } from '../../package.json';

export const GraphQLErrorInterceptor: MiddlewareFn<Context> = async (
    { context, args, info },
    next
) => {
    try {
        return await next();
    } catch (err: any) {
        const graphqlError = new BaseGraphQLError(err);
        Sentry.withScope((scope) => {
            const level: Sentry.SeverityLevel = !err.statusCode
                ? 'error'
                : err.statusCode >= 500
                ? 'error'
                : 'warning';
            scope.setLevel(level);

            const user = context.currentUser;
            if (user) {
                scope.setUser({
                    id: `${user.id}`,
                    email: user.email,
                    name: user.name,
                    code: user.code,
                });
            }

            scope.addEventProcessor((event) => {
                return Sentry.addRequestDataToEvent(event, context.ctx.request as any);
            });
            const formattedError = {
                extensions: graphqlError.extensions,
                locations: graphqlError.locations,
                message: graphqlError.message,
                path: graphqlError.path,
            };
            scope.setExtras({
                'API version': ApiVersion,
                'GraphQL error': util.inspect(formattedError, { depth: null }),
                'GraphQL path': info.path?.key,
            });

            scope.setTags({ status_code: err.statusCode ?? undefined });

            Sentry.captureException(err);
        });

        logger.error(err);
        throw new BaseGraphQLError(err);
    }
};
