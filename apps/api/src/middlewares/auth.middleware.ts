import { BaseError } from '@clinico/base-error';
import { preHandlerHookHandler } from 'fastify';
import { Forbidden, Unauthorized } from 'http-errors';
import { FORBIDDEN, UNAUTHORIZED } from 'http-status';
import { UseMiddleware } from 'type-graphql';
import { Container } from 'typedi';

import { EnumErrorMessage, EnumPermissionCode } from '~/constants';
import { AuthService } from '~/modules/auth/auth.service';

export const UserAuthInterceptor = (permission?: EnumPermissionCode | EnumPermissionCode[]) => {
    return UseMiddleware(async ({ context }, next) => {
        if (!context.currentUser) {
            throw new BaseError(EnumErrorMessage.ReloginRequired, UNAUTHORIZED);
        }

        if (permission) {
            const authService = Container.get<AuthService>('AuthService');
            const has = await authService.checkUserPermission({
                userId: context.currentUser.id,
                permissions: permission instanceof Array ? permission : [permission],
            });
            if (!has) {
                throw new BaseError(EnumErrorMessage.PermissionDenied, FORBIDDEN);
            }
        }

        return await next();
    });
};

export const RestUserAuthInterceptor = (permission?: EnumPermissionCode | EnumPermissionCode[]) => {
    const handler: preHandlerHookHandler = async (req, reply) => {
        const { currentUser } = req.ctx;

        if (!currentUser) {
            throw new Unauthorized(EnumErrorMessage.ReloginRequired);
        }

        if (permission) {
            const authService = Container.get<AuthService>('AuthService');
            const has = await authService.checkUserPermission({
                userId: currentUser.id,
                permissions: permission instanceof Array ? permission : [permission],
            });
            if (!has) {
                throw new Forbidden(EnumErrorMessage.PermissionDenied);
            }
        }
    };

    return handler;
};
