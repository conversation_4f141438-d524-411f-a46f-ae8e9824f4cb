import { EnumSalesTeamGroupCode } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { FilterQuery } from '@mikro-orm/core';
import { FastifyRequest } from 'fastify';
import _ from 'lodash';

import { EnumCustomHeader } from '~/constants';
import { DI } from '~/container';

/** Set mocked visible sales team groups after building auth service in FrontEnd app */
export const getMockedVisibleSalesTeamGroups = async (
    req: FastifyRequest
): Promise<SalesTeamGroup[]> => {
    const em = DI.em.fork();

    const rows = await em.find(SalesTeamGroup, {});
    return rows;
};

/** Set current sales team group after building auth service in FrontEnd app */
export const getCurrentSalesTeamGroup = async (
    req: FastifyRequest
): Promise<SalesTeamGroup | null> => {
    const em = DI.em.fork();

    const code = req.ctx.currentUser
        ? req.headers[EnumCustomHeader.SalesTeamGroupCode] ?? EnumSalesTeamGroupCode.CHN_EYE
        : null;
    if (_.isNil(code)) return null;

    const filter: FilterQuery<SalesTeamGroup> = {};

    if (code instanceof Array) {
        filter.code = { $in: code as EnumSalesTeamGroupCode[] };
    } else {
        filter.code = code as EnumSalesTeamGroupCode;
    }

    if (req.ctx.visibleSalesTeamGroups) {
        filter.id = { $in: req.ctx.visibleSalesTeamGroups.map((v) => v.id) };
    }

    const row = await em.findOne(SalesTeamGroup, filter);
    return row ?? null;
};
