import { FastifyRequest } from 'fastify';
import _ from 'lodash';
import Container from 'typedi';

import { UserPermissionService } from '~/modules/permission/userPermission/userPermission.service';
import { UserPermission } from '~/modules/permission/userPermission/userPermission.type';

/** Set current permission after building auth service in FrontEnd app */
export const getCurrentPermission = async (req: FastifyRequest): Promise<UserPermission | null> => {
    const userPermissionService = Container.get<UserPermissionService>('UserPermissionService');

    const user = req.ctx.currentUser;
    if (_.isNil(user)) return null;

    const row = await userPermissionService.findOneOrCreate({ userId: user.id });
    return row ?? null;
};
