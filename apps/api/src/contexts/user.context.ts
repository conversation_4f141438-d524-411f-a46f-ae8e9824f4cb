import { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import { FastifyRequest } from 'fastify';

import { DI } from '~/container';

type SuccessAuthentication = {
    success: true;
    user: PublicUser;
};

type FailureAuthentication = {
    success: false;
    message: string;
};

/** Set current user after building auth service in FrontEnd app */
export const getCurrentUser = async (
    req: FastifyRequest
): Promise<SuccessAuthentication | FailureAuthentication> => {
    const em = DI.em.fork();

    const token = req.headers.authorization;
    if (!token) {
        return {
            success: false,
            message: `[InvalidToken] Token from header not found`,
        };
    }

    const valid = DI.auth.verify(token);
    if (!valid.success) {
        // Ref: https://github.com/auth0/node-jsonwebtoken#errors--codes
        switch (valid.error.name) {
            case 'TokenExpiredError':
                return {
                    success: false,
                    message: `[TokenExpiredError] Token expired`,
                };
            case 'JsonWebTokenError':
                return {
                    success: false,
                    message: `[InvalidToken] The token is incorrect (token: ${token})`,
                };
            case 'NotBeforeError':
                return {
                    success: false,
                    message: `[InvalidToken] The token is inactive (token: ${token})`,
                };
            default:
                return {
                    success: false,
                    message: `[InvalidToken] The token causes unknown reason (token: ${token})`,
                };
        }
    }

    const user = await em.findOne(PublicUser, { id: valid.payload.id });
    if (!user) {
        return {
            success: false,
            message: `[InvalidToken] User for the token not found (token: ${token})`,
        };
    }

    return { success: true, user };
};
