import { BusinessMemo } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessMemo.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import {
    CreateBusinessMemoParams,
    DeleteBusinessMemoParams,
    UpdateBusinessMemoParams,
} from '@packages/erp-protobuf/generated/business/businessMemo_pb';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { BusinessMemoGrpcRepo } from './businessMemo.grpc.repo';
import { CreateInput, UpdateInput } from './businessMemo.input';
import { FilterInput } from './businessMemo.type';

@Service()
export class BusinessMemoService extends BaseService<BusinessMemo> {
    protected entity = BusinessMemo;
    private gRPCrepo = new BusinessMemoGrpcRepo();

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: BusinessMemo[] }> {
        const em = this.em.fork();

        const where: FilterQuery<BusinessMemo>[] = new FilterBuilder({
            em,
            ent: this.entity,
            filters: params,
        }).build();
        where.push({ deleted: false });

        // For headers
        if (options?.ctx?.currentSalesTeamGroup) {
            where.push({
                business: { salesTeamGroup: { id: options.ctx.currentSalesTeamGroup.id } },
            });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['business', 'createdUser', 'updatedUser'],
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    async createByGrpc(params: CreateInput, options?: { ctx?: Context }): Promise<BusinessMemo> {
        const inputs = new CreateBusinessMemoParams({
            regionId: options?.ctx?.currentSalesTeamGroup?.region?.id,
            businessId: params.businessId,
            title: params.title,
            content: params.content,
            createdUserId: options?.ctx?.currentUser?.id,
        });
        const row = await this.gRPCrepo.create(inputs);

        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(params: UpdateInput, options?: { ctx?: Context }): Promise<BusinessMemo> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateBusinessMemoParams({
            id: row.id,
            businessId: row.business.id,
            title: handler.toString({ input: params.title, default: row.title }),
            content: handler.toString({ input: params.content, default: row.content }),
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.update(inputs);
        const createdRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return createdRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteBusinessMemoParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }
}
