import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import type { BusinessMemo } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessMemo.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessService } from '../business/business.service';
import { TBusiness } from '../business/business.type';
import { CreateInput, UpdateInput } from './businessMemo.input';
import { BusinessMemoService } from './businessMemo.service';
import { PaginatedObjects, SearchArgs, TBusinessMemo } from './businessMemo.type';

@Service()
@Resolver(() => TBusinessMemo)
export class BusinessMemoResolver {
    @Inject()
    private service: BusinessMemoService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBusinessMemos' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; businessMemos: BusinessMemo[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, businessMemos: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBusinessMemo], { name: 'businessMemos' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<BusinessMemo[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBusinessMemo, { name: 'businessMemo' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<BusinessMemo> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBusinessMemo, { name: 'createBusinessMemo' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<BusinessMemo> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBusinessMemo, { name: 'updateBusinessMemo' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<BusinessMemo> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteBusinessMemo' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private businessService: BusinessService;
    @FieldResolver(() => TBusiness, { description: '商機' })
    async business(@Root() model: BusinessMemo): Promise<Business | undefined> {
        return await this.businessService.findOne({ id: model.businessId });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: BusinessMemo): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: BusinessMemo): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }
}
