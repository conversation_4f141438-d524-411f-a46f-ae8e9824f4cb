import type { BusinessMemo } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessMemo.model';
import { IBusinessMemo } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessMemo.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BusinessMemo', { implements: IBusinessMemo })
export class TBusinessMemo extends IBusinessMemo {}

@ObjectType('PaginatedBusinessMemos')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusinessMemo], { nullable: true })
    businessMemos: BusinessMemo[];
}

@InputType('BusinessMemoFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    businessId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
