import { Field, ID, InputType } from 'type-graphql';

@InputType('BusinessMemoInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    businessId?: number;

    @Field({ nullable: true })
    title?: string;

    @Field({ nullable: true })
    content?: string;

    deleted?: boolean;
}

@InputType('BusinessMemoCreateInput')
export class CreateInput extends CommonInput {}

@InputType('BusinessMemoUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
