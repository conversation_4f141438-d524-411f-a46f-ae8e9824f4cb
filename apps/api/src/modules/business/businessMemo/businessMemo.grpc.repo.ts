import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    BusinessMemoResult,
    CreateBusinessMemoParams,
    DeleteBusinessMemoParams,
    UpdateBusinessMemoParams,
} from '@packages/erp-protobuf/generated/business/businessMemo_pb';

@Service()
export class BusinessMemoGrpcRepo {
    serviceNodes: string[] = ['business', 'BusinessMemo'];

    async create(params: CreateBusinessMemoParams): Promise<BusinessMemoResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BusinessMemoResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.BusinessMemo.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateBusinessMemoParams): Promise<BusinessMemoResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BusinessMemoResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.BusinessMemo.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteBusinessMemoParams): Promise<BusinessMemoResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BusinessMemoResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.BusinessMemo.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
