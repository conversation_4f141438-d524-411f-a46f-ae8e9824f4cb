import { <PERSON><PERSON><PERSON>, <PERSON>tx, FieldResolver, Query, Resolver } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { EnumPermissionCode } from '~/constants';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessAnalysis } from './businessAnalysis.type';
import { BusinessAnalysisBusinessStatService } from './businessAnalysisBusinessStat.service';
import {
    BusinessAnalysisBusinessStat,
    SearchArgs as BusinessAnalysisBusinessStatSearchArgs,
} from './businessAnalysisBusinessStat.type';
import { BusinessAnalysisProductRankService } from './businessAnalysisProductRank.service';
import {
    BusinessAnalysisProductRank,
    SearchArgs as BusinessAnalysisProductRankSearchArgs,
} from './businessAnalysisProductRank.type';
import { BusinessSaleDealAmount } from './salesDealAmount.type';
import { SalesTeamUserService } from '~/modules/salesTeam/salesTeamUser/salesTeamUser.service';
import { EnumSalesTeamGroupCode } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';

@Service()
@Resolver(() => BusinessAnalysis)
export class BusinessAnalysisResolver {
    @UserAuthInterceptor(EnumPermissionCode['business.read'])
    @Query(() => BusinessAnalysis, { name: 'businessAnalysis' })
    async stat(@Ctx() ctx: Context): Promise<BusinessAnalysis> {
        return {};
    }

    @Inject()
    private businessAnalysisBusinessStatService: BusinessAnalysisBusinessStatService;
    @FieldResolver(() => BusinessAnalysisBusinessStat, { description: '商機統計' })
    async businessStat(
        @Args() { filters }: BusinessAnalysisBusinessStatSearchArgs,
        @Ctx() ctx: Context
    ): Promise<BusinessAnalysisBusinessStat> {
        const stat = await this.businessAnalysisBusinessStatService.stat(filters, { ctx });
        return stat;
    }

    @Inject()
    private businessAnalysisProductService: BusinessAnalysisProductRankService;
    @FieldResolver(() => [BusinessAnalysisProductRank], { description: '商機商品統計排名' })
    async productRank(
        @Args() { filters }: BusinessAnalysisProductRankSearchArgs,
        @Ctx() ctx: Context
    ): Promise<BusinessAnalysisProductRank[]> {
        const stat = await this.businessAnalysisProductService.stat(filters, { ctx });
        return stat;
    }


    @Inject()
    private salesTeamUserService: SalesTeamUserService;
    @FieldResolver(() => [BusinessSaleDealAmount], { description: '業務成交金額', nullable: true })
    async dealAmountSum(@Args() { filters }: BusinessAnalysisProductRankSearchArgs,
        @Ctx() ctx: Context): Promise<BusinessSaleDealAmount[]> {
        const salesTeamGroupCode = ctx?.currentSalesTeamGroup?.code

        if (salesTeamGroupCode === EnumSalesTeamGroupCode.TWN_EYE) {
            const result = await this.salesTeamUserService.salesDealAmount(filters);
            return result;
        }
        return []
    }
}
