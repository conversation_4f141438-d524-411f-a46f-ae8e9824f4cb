import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';

import { FilterInput as BusinessFilterInput } from '../business/business.type';

@ObjectType()
export class BusinessAnalysisProductRank {
    @Field(() => Int, { description: '流水號' })
    seq: number;

    @Field(() => Int, { description: '排名' })
    rank: number;

    @Field(() => ID, { description: '商機商品' })
    businessProductId: number;

    @Field(() => Int, { description: '數量' })
    qty: number;
}

@InputType('BusinessAnalysisProductRankFilterInput')
export class FilterInput extends BusinessFilterInput {}

@ArgsType()
export class SearchArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
