
import { ArgsType, Field, ID, InputType, Int, ObjectType, Float } from 'type-graphql';
import { TUser } from '~/modules/user/user/user.type';
import { TSalesTeam } from '~/modules/salesTeam/salesTeam/salesTeam.type';
import { SalesTeam } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeam.model';
import { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
@ObjectType()
export class BusinessSaleDealAmount {
    @Field(() => TSalesTeam, { description: '業務團隊' })
    salesTeam: SalesTeam

    @Field(() => TUser, { description: '業務人員' })
    user: PublicUser

    @Field(() => Float, { description: '成交金額' })
    dealAmount: number
}
