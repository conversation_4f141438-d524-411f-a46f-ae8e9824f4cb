import { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import { EntityData } from '@mikro-orm/core';
import BigNumber from 'bignumber.js';
import _ from 'lodash';
import { Inject, Service } from 'typedi';

import { array } from '@packages/utils';

import { DI } from '~/container';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessService } from '../business/business.service';
import { BusinessAnalysisProductRank, FilterInput } from './businessAnalysisProductRank.type';

@Service()
export class BusinessAnalysisProductRankService {
    private em = DI.em;

    @Inject()
    private businessService: BusinessService;

    async stat(
        params?: FilterInput,
        options?: { ctx?: Context }
    ): Promise<BusinessAnalysisProductRank[]> {
        const em = this.em.fork();

        const { rows } = await this.businessService.search(params, { ...options, fields: ['id'] });
        const sql = this.buildRawSQL(rows);
        const items = await em.execute(sql);

        const results: BusinessAnalysisProductRank[] = this.transform(items);
        return results;
    }

    private transform(rows: EntityData<Partial<any>>[]): BusinessAnalysisProductRank[] {
        type Result = Omit<BusinessAnalysisProductRank, 'seq'>;
        const results: Omit<BusinessAnalysisProductRank, 'seq'>[] = rows.map((item) => ({
            rank: Number(item.rank),
            businessProductId: item.id,
            qty: new BigNumber(item.qty).toNumber(),
        }));
        return array.alignRanking<Result>(results, 'qty');
    }

    private buildRawSQL(rows: Business[]) {
        const sql = `
        WITH
        w_products AS (
            SELECT RANK() OVER (ORDER BY SUM(bp.qty) DESC, p.id) AS "rank", p.id, SUM(bp.qty) AS qty
            FROM sales_rep_workstation.business_products p
            JOIN sales_rep_workstation.businesses_to_budget_products bp ON bp.budget_product_id = p.id
            JOIN sales_rep_workstation.businesses b ON bp.business_id = b.id
            WHERE b.id IN (${rows.length ? rows.map((r) => `'${r.id}'`).join(',') : `'0'`})
              AND p.deleted = FALSE
            GROUP BY p.id
        )
        SELECT w."rank", w.id, w.qty
        FROM w_products w
        ORDER BY w."rank"
        `;
        return sql;
    }
}
