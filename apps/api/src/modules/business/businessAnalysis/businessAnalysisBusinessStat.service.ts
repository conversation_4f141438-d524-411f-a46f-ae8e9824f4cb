import { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import { EntityData } from '@mikro-orm/core';
import BigNumber from 'bignumber.js';
import _ from 'lodash';
import { Inject, Service } from 'typedi';

import { toPercentOrNull } from '@packages/utils/number.util';

import { DI } from '~/container';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessService } from '../business/business.service';
import { EnumBusinessStatType } from './businessAnalysisBusinessStat.enum';
import {
    BusinessAnalysisBusinessStat,
    BusinessAnalysisBusinessStat_Detail,
    BusinessAnalysisBusinessStat_Overall,
} from './businessAnalysisBusinessStat.type';
import { FilterInput } from './businessAnalysisBusinessStat.type';
import { EnumSalesTeamGroupCode } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';

@Service()
export class BusinessAnalysisBusinessStatService {
    private em = DI.em;

    @Inject()
    private businessService: BusinessService;

    async stat(
        params?: FilterInput,
        options?: { ctx?: Context }
    ): Promise<BusinessAnalysisBusinessStat> {
        const em = this.em.fork();
        const salesTeamGroupCode = options?.ctx?.currentSalesTeamGroup?.code
        const { rows } = await this.businessService.search(params, { ...options, fields: ['id'] });
        const sql = this.buildRawSQL(rows, salesTeamGroupCode);
        const items = await em.execute(sql);

        const results: BusinessAnalysisBusinessStat = this.transform(items);
        return results;
    }

    private transform(rows: EntityData<Partial<any>>[]): BusinessAnalysisBusinessStat {
        const mapToItems = _.groupBy(rows, 'type');
        const results: BusinessAnalysisBusinessStat = {
            overall: { count: 0, amount: '0' },
            details: [],
        };

        // 組裝 overall 層資料
        const overall: BusinessAnalysisBusinessStat_Overall = { count: 0, amount: '0' };
        const item = mapToItems['Overall']?.[0];
        if (item) {
            overall.count = new BigNumber(item.count).toNumber();
            overall.amount = new BigNumber(item.amount).toString();
        }
        results.overall = overall;

        // 組裝 details 層資料
        const types: EnumBusinessStatType[] = [
            EnumBusinessStatType.Winning,
            EnumBusinessStatType.Losing,
            EnumBusinessStatType.Cancel,
            EnumBusinessStatType.InProgress,
            EnumBusinessStatType.HighOpportunity,
            EnumBusinessStatType.MediumHighOpportunity,
            EnumBusinessStatType.MediumHighOpportunity_99_75,
            EnumBusinessStatType.MediumHighOpportunity_75_99,
            EnumBusinessStatType.MediumHighOpportunity_99_50,
            EnumBusinessStatType.MediumOpportunity,
            EnumBusinessStatType.MediumOpportunity_99_25,
            EnumBusinessStatType.OtherOpportunity,
            EnumBusinessStatType.Others,
        ];
        for (const type of types) {
            const detail: BusinessAnalysisBusinessStat_Detail = {
                type: type,
                count: 0,
                countPercent: null,
                amount: '0',
                amountPercent: null,
            };
            const item = mapToItems[type]?.[0];
            if (item) {
                const currentCount = new BigNumber(item.count);
                detail.count = currentCount.toNumber();

                detail.countPercent = toPercentOrNull({
                    dividend: item.count,
                    divisor: results.overall.count,
                });

                const currentAmount = new BigNumber(item.amount);
                detail.amount = currentAmount.toString();

                detail.amountPercent = toPercentOrNull({
                    dividend: item.amount,
                    divisor: results.overall.amount,
                });
            }
            results.details.push(detail);
        }
        return results;
    }

    private buildRawSQL(rows: Business[], salesTeamGroupCode: EnumSalesTeamGroupCode | undefined) {

        // 基於 salesTeamGroupCode 定義特定查詢條件
        const opportunityConditions = salesTeamGroupCode === EnumSalesTeamGroupCode.TWN_EYE
            ? {
                other: `b.winning_opportunity_code NOT IN ('25%','50%','75%') OR b.buying_opportunity NOT IN ('99%')`,
                medium: `b.winning_opportunity_code = '25%' AND b.buying_opportunity = '99%'`,
                mediumHigh: `(b.winning_opportunity_code = '50%' AND b.buying_opportunity = '99%') OR (b.winning_opportunity_code = '75%' AND b.buying_opportunity = '99%')`
            }
            : {
                other: `b.winning_opportunity_code NOT IN ('75%','99%') OR b.buying_opportunity NOT IN ('75%','99%')`,
                medium: `b.winning_opportunity_code = '75%' AND b.buying_opportunity = '75%'`,
                mediumHigh: `(b.winning_opportunity_code = '99%' AND b.buying_opportunity = '75%') OR (b.winning_opportunity_code = '75%' AND b.buying_opportunity = '99%')`
            };

        // 定義額外的 UNION ALL 查詢
        const additionalUnions = salesTeamGroupCode === EnumSalesTeamGroupCode.TWN_EYE
            ? `SELECT * FROM w_medium_high_opportunity_99_75_stat
       UNION ALL
       SELECT * FROM w_medium_high_opportunity_99_50_stat
       UNION ALL
       SELECT * FROM w_medium_opportunity_stat
       UNION ALL
       SELECT * FROM w_medium_opportunity_99_25_stat
       UNION ALL`
            : `SELECT * FROM w_medium_high_opportunity_99_75_stat
       UNION ALL
       SELECT * FROM w_medium_high_opportunity_75_99_stat
       UNION ALL
       SELECT * FROM w_medium_opportunity_stat
       UNION ALL`;

        const sql = `
        WITH
        w_businesses AS (
            SELECT b.id
                  ,b.budget_amount
                  ,b.deal_amount
                  ,bs."type" AS status_type
                  ,bs.buying_opportunity
                  ,bo1.code AS winning_opportunity_code
            FROM sales_rep_workstation.businesses                  b
            LEFT JOIN sales_rep_workstation.business_statuses      bs  ON bs.id = b.status_id
            LEFT JOIN sales_rep_workstation.business_opportunities bo1 ON bo1.id = b.winning_opportunity_id
            WHERE b.id IN (${rows.length ? rows.map((r) => `'${r.id}'`).join(',') : `'0'`})
        ),
        w_overall_stat AS (
            SELECT 'Overall' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
        ),
        w_winning_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'ClosedInWinning'
        ),
        w_winning_stat AS (
            SELECT 'Winning' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_winning_list)
        ),
        w_losing_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'ClosedInLosing'
        ),
        w_losing_stat AS (
            SELECT 'Losing' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_losing_list)
        ),
        w_cancel_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'ClosedInCancel'
        ),
        w_cancel_stat AS (
            SELECT 'Cancel' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_cancel_list)
        ),
        w_in_progress_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
        ),
        w_in_progress_stat AS (
            SELECT 'InProgress' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_in_progress_list)
        ),
        w_high_opportunity_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND b.winning_opportunity_code = '99%'
              AND b.buying_opportunity = '99%'
        ),
        w_high_opportunity_stat AS (
            SELECT 'HighOpportunity' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_high_opportunity_list)
        ),
        w_medium_high_opportunity_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND (${opportunityConditions.mediumHigh})
        ),
        w_medium_high_opportunity_stat AS (
            SELECT 'MediumHighOpportunity' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_medium_high_opportunity_list)
        ),
        w_medium_high_opportunity_99_75_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND (b.winning_opportunity_code = '75%' AND b.buying_opportunity = '99%')
        ),
        w_medium_high_opportunity_99_75_stat AS (
            SELECT 'MediumHighOpportunity_99_75' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_medium_high_opportunity_99_75_list)
        ),
        w_medium_high_opportunity_99_50_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND (b.winning_opportunity_code = '50%' AND b.buying_opportunity = '99%')
        ),
        w_medium_high_opportunity_99_50_stat AS (
            SELECT 'MediumHighOpportunity_99_50' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_medium_high_opportunity_99_50_list)
        ),
        w_medium_high_opportunity_75_99_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND (b.winning_opportunity_code = '99%' AND b.buying_opportunity = '75%')
        ),
        w_medium_high_opportunity_75_99_stat AS (
            SELECT 'MediumHighOpportunity_75_99' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_medium_high_opportunity_75_99_list)
        ),

        w_medium_opportunity_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND (${opportunityConditions.medium})
        ),
        w_medium_opportunity_stat AS (
            SELECT 'MediumOpportunity' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_medium_opportunity_list)
        ),
       w_medium_opportunity_99_25_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND (b.winning_opportunity_code = '25%' AND b.buying_opportunity = '99%')
        ),
        w_medium_opportunity_99_25_stat AS (
            SELECT 'MediumOpportunity_99_25' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_medium_opportunity_99_25_list)
        ),
        w_other_opportunity_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND (${opportunityConditions.other})
        ),
        w_other_opportunity_stat AS (
            SELECT 'OtherOpportunity' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_other_opportunity_list)
        ),
        w_others_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.id NOT IN (SELECT id FROM w_winning_list)
              AND b.id NOT IN (SELECT id FROM w_losing_list)
              AND b.id NOT IN (SELECT id FROM w_cancel_list)
              AND b.id NOT IN (SELECT id FROM w_in_progress_list)
              AND b.id NOT IN (SELECT id FROM w_high_opportunity_list)
              AND b.id NOT IN (SELECT id FROM w_medium_high_opportunity_list)
              AND b.id NOT IN (SELECT id FROM w_medium_high_opportunity_99_75_list)
              AND b.id NOT IN (SELECT id FROM w_medium_high_opportunity_75_99_list)
              AND b.id NOT IN (SELECT id FROM w_medium_high_opportunity_99_50_list)
              AND b.id NOT IN (SELECT id FROM w_medium_opportunity_list)
              AND b.id NOT IN (SELECT id from w_medium_opportunity_99_25_list)
              AND b.id NOT IN (SELECT id FROM w_other_opportunity_list)
        ),
        w_others_stat AS (
            SELECT 'Others' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_others_list)
        )
        SELECT * FROM w_overall_stat
        UNION ALL
        SELECT * FROM w_winning_stat
        UNION ALL
        SELECT * FROM w_losing_stat
        UNION ALL
        SELECT * FROM w_cancel_stat
        UNION ALL
        SELECT * FROM w_in_progress_stat
        UNION ALL
        SELECT * FROM w_high_opportunity_stat
        UNION ALL
        SELECT * FROM w_medium_high_opportunity_stat
        UNION ALL
        SELECT * FROM w_medium_opportunity_stat
        UNION ALL
        /* 其他 UNION ALL... */
       ${additionalUnions}
        SELECT * FROM w_other_opportunity_stat
        UNION ALL
        SELECT * FROM w_others_stat
        `;
        return sql;
    }
}
