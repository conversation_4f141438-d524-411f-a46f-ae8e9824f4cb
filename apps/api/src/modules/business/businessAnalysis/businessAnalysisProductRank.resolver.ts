import type { BusinessProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProduct.model';
import { FieldResolver, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { BusinessProductService } from '../businessProduct/businessProduct.service';
import { TBusinessProduct } from '../businessProduct/businessProduct.type';
import { BusinessAnalysisProductRank } from './businessAnalysisProductRank.type';

@Service()
@Resolver(() => BusinessAnalysisProductRank)
export class BusinessAnalysisProductRankResolver {
    @Inject()
    private businessProductService: BusinessProductService;
    @FieldResolver(() => TBusinessProduct)
    async businessProduct(
        @Root() model: BusinessAnalysisProductRank
    ): Promise<BusinessProduct | undefined> {
        return await this.businessProductService.findOne({ id: model.businessProductId });
    }
}
