import { registerEnumType } from 'type-graphql';

export enum EnumBusinessStatType {
    /** 贏單 */
    Winning = 'Winning',
    /** 丟單 */
    Losing = 'Losing',
    /** 取消 */
    Cancel = 'Cancel',
    /** 跟進中 */
    InProgress = 'InProgress',
    /** 跟進中（成交機會：高_99%成單_99%贏單） */
    HighOpportunity = 'HighOpportunity',
    /** 跟進中（成交機會：中高_75%/99%成單_99/75%贏單_99/50%贏單） */
    MediumHighOpportunity = 'MediumHighOpportunity',
    /** 跟進中（成交機會：中高_99%成單_75%贏單） */
    'MediumHighOpportunity_99_75' = 'MediumHighOpportunity_99_75',
    /** 跟進中（成交機會：中高_75%成單_99%贏單） */
    'MediumHighOpportunity_75_99' = 'MediumHighOpportunity_75_99',
    /** 跟進中（成交機會：中高_99%成單_50%贏單） for 台灣達輝*/
    'MediumHighOpportunity_99_50' = 'MediumHighOpportunity_99_50',
    /** 跟進中（成交機會：中_75%成單_75%贏單） */
    MediumOpportunity = 'MediumOpportunity',
    /** 跟進中（成交機會：中_99%成單_25%贏單）for 台灣達輝 */
    'MediumOpportunity_99_25' = 'MediumOpportunity_99_25',
    /** 跟進中（成交機會：其他） */
    OtherOpportunity = 'OtherOpportunity',
    /** 其他（髒資料） */
    Others = 'Others',
}
registerEnumType(EnumBusinessStatType, {
    name: 'EnumBusinessStatType',
    description: '商機統計類型',
    valuesConfig: {
        Winning: { description: '贏單' },
        Losing: { description: '丟單' },
        Cancel: { description: '取消' },
        InProgress: { description: '跟進中' },
        HighOpportunity: { description: '跟進中（成交機會：高_99%成單_99%贏單）' },
        MediumHighOpportunity: { description: '跟進中（成交機會：中高_75%/99%成單_99/75%贏單）' },
        MediumHighOpportunity_99_75: { description: '跟進中（成交機會：中高_99%成單_75%贏單）' },
        MediumHighOpportunity_75_99: { description: '跟進中（成交機會：中高_75%成單_99%贏單）' },
        MediumHighOpportunity_99_50: { description: '跟進中（成交機會：中高_99%成單_50%贏單）' },
        MediumOpportunity: { description: '跟進中（成交機會：中_75%成單_75%贏單）' },
        MediumOpportunity_99_25: { description: '跟進中（成交機會：中_99%成單_25%贏單）' },
        OtherOpportunity: { description: '跟進中（成交機會：其他）' },
        Others: { description: '其他（髒資料）' },
    },
});
