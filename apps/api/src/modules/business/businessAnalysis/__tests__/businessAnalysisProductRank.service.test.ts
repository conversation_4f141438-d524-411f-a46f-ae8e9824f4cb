import { BusinessAnalysisProductRankService } from '../businessAnalysisProductRank.service';

describe('buildRawSQL', () => {
    const buildRawSQL = BusinessAnalysisProductRankService.prototype['buildRawSQL'];

    test('should build correct the raw SQL when given empty data', () => {
        const inputs: any[] = [];
        const result = buildRawSQL(inputs);

        const expected = `WHERE b.id IN ('0')`;
        expect(result).toContain(expected);
    });

    test('should build correct the raw SQL when given single data', () => {
        const inputs: any[] = [{ id: 1 }];
        const result = buildRawSQL(inputs);

        const expected = `WHERE b.id IN ('1')`;
        expect(result).toContain(expected);
    });

    test('should build correct the raw SQL when given multiple data', () => {
        const inputs: any[] = [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }];
        const result = buildRawSQL(inputs);

        const expected = `WHERE b.id IN ('1','2','3','4')`;
        expect(result).toContain(expected);
    });
});

describe('transform', () => {
    const transform = BusinessAnalysisProductRankService.prototype['transform'];

    test('should build the correct data when given empty data', () => {
        const inputs: any[] = [];
        const result = transform(inputs);

        const expected = [];
        expect(result).toEqual(expected);
    });

    test('should build the correct data when given multiple data', () => {
        const inputs: any[] = [
            { rank: '1', id: '150', qty: 651 },
            { rank: '2', id: '224', qty: 169 },
            { rank: '3', id: '202', qty: 140 },
            { rank: '4', id: '226', qty: 137 },
            { rank: '5', id: '225', qty: 98 },
            { rank: '6', id: '137', qty: 9 },
            { rank: '7', id: '156', qty: 9 },
            { rank: '8', id: '149', qty: 8 },
            { rank: '9', id: '203', qty: 8 },
            { rank: '10', id: '171', qty: 6 },
            { rank: '11', id: '160', qty: 5 },
            { rank: '12', id: '195', qty: 4 },
            { rank: '13', id: '208', qty: 4 },
            { rank: '14', id: '188', qty: 3 },
            { rank: '15', id: '196', qty: 3 },
            { rank: '16', id: '206', qty: 3 },
            { rank: '17', id: '166', qty: 2 },
            { rank: '18', id: '191', qty: 2 },
            { rank: '19', id: '193', qty: 2 },
            { rank: '20', id: '207', qty: 2 },
            { rank: '21', id: '187', qty: 1 },
            { rank: '22', id: '190', qty: 1 },
            { rank: '23', id: '192', qty: 1 },
            { rank: '24', id: '200', qty: 1 },
            { rank: '25', id: '219', qty: 1 },
        ];
        const result = transform(inputs);

        const expected = [
            { seq: 1, rank: 1, businessProductId: '150', qty: 651 },
            { seq: 2, rank: 2, businessProductId: '224', qty: 169 },
            { seq: 3, rank: 3, businessProductId: '202', qty: 140 },
            { seq: 4, rank: 4, businessProductId: '226', qty: 137 },
            { seq: 5, rank: 5, businessProductId: '225', qty: 98 },
            { seq: 6, rank: 6, businessProductId: '137', qty: 9 },
            { seq: 7, rank: 6, businessProductId: '156', qty: 9 },
            { seq: 8, rank: 8, businessProductId: '149', qty: 8 },
            { seq: 9, rank: 8, businessProductId: '203', qty: 8 },
            { seq: 10, rank: 10, businessProductId: '171', qty: 6 },
            { seq: 11, rank: 11, businessProductId: '160', qty: 5 },
            { seq: 12, rank: 12, businessProductId: '195', qty: 4 },
            { seq: 13, rank: 12, businessProductId: '208', qty: 4 },
            { seq: 14, rank: 14, businessProductId: '188', qty: 3 },
            { seq: 15, rank: 14, businessProductId: '196', qty: 3 },
            { seq: 16, rank: 14, businessProductId: '206', qty: 3 },
            { seq: 17, rank: 17, businessProductId: '166', qty: 2 },
            { seq: 18, rank: 17, businessProductId: '191', qty: 2 },
            { seq: 19, rank: 17, businessProductId: '193', qty: 2 },
            { seq: 20, rank: 17, businessProductId: '207', qty: 2 },
            { seq: 21, rank: 21, businessProductId: '187', qty: 1 },
            { seq: 22, rank: 21, businessProductId: '190', qty: 1 },
            { seq: 23, rank: 21, businessProductId: '192', qty: 1 },
            { seq: 24, rank: 21, businessProductId: '200', qty: 1 },
            { seq: 25, rank: 21, businessProductId: '219', qty: 1 },
        ];
        expect(result).toEqual(expected);
    });
});
