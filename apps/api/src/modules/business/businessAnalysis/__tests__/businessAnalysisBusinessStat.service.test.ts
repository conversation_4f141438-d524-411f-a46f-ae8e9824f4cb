import { EnumSalesTeamGroupCode } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { BusinessAnalysisBusinessStatService } from '../businessAnalysisBusinessStat.service';

describe('buildRawSQL', () => {
    const buildRawSQL = BusinessAnalysisBusinessStatService.prototype['buildRawSQL'];

    test('should build correct the raw SQL when given empty data', () => {
        const inputs: any[] = [];
        const result = buildRawSQL(inputs, EnumSalesTeamGroupCode.CHN_EYE);

        expect(result).toContain(`WHERE b.id IN ('0')`);
    });

    test('should build correct the raw SQL when given single data', () => {
        const inputs: any[] = [{ id: 1 }];
        const result = buildRawSQL(inputs, EnumSalesTeamGroupCode.TWN_EYE);

        expect(result).toContain(`WHERE b.id IN ('1')`);
    });

    test('should build correct the raw SQL when given multiple data', () => {
        const inputs: any[] = [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }];
        const result = buildRawSQL(inputs, EnumSalesTeamGroupCode.TWN_EYE);

        expect(result).toContain(`WHERE b.id IN ('1','2','3','4')`);
    });
});

describe('transform', () => {
    const transform = BusinessAnalysisBusinessStatService.prototype['transform'];

    test('should build the correct data when given empty data', () => {
        const inputs: any[] = [];
        const result = transform(inputs);

        const expected = {
            details: [
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'Winning',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'Losing',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'Cancel',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'InProgress',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'HighOpportunity',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'MediumHighOpportunity',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'MediumHighOpportunity_99_75',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'MediumHighOpportunity_75_99',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'MediumHighOpportunity_99_50',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'MediumOpportunity',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'MediumOpportunity_99_25',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'OtherOpportunity',
                },
                {
                    amount: '0',
                    amountPercent: null,
                    count: 0,
                    countPercent: null,
                    type: 'Others',
                },
            ],
            overall: {
                amount: '0',
                count: 0,
            },
        };
        expect(result).toEqual(expected);
    });

    test('should build the correct data when given multiple data', () => {
        const inputs: any[] = [
            { type: 'Overall', count: '4482', amount: '1542592.567800' },
            { type: 'Winning', count: '122', amount: '91000.000000' },
            { type: 'Losing', count: '195', amount: '0' },
            { type: 'Cancel', count: '1', amount: '0' },
            { type: 'InProgress', count: '4160', amount: '1451592.567800' },
            { type: 'HighOpportunity', count: '2', amount: '0' },
            { type: 'MediumHighOpportunity', count: '35', amount: '0' },
            { type: 'MediumHighOpportunity_99_75', count: '35', amount: '0' },
            { type: 'MediumHighOpportunity_75_99', count: '0', amount: '0' },
            { type: 'MediumHighOpportunity_99_50', count: '0', amount: '0' },
            { type: 'MediumOpportunity', count: '106', amount: '2.000000' },
            { type: 'MediumOpportunity_99_25', count: '106', amount: '2.000000' },
            { type: 'OtherOpportunity', count: '3567', amount: '1450133.000000' },
            { type: 'Others', count: '4', amount: '0.000000' },
        ];
        const result = transform(inputs);

        const expected = {
            overall: { count: 4482, amount: '1542592.5678' },
            details: [
                {
                    type: 'Winning',
                    count: 122,
                    countPercent: '0.0272',
                    amount: '91000',
                    amountPercent: '0.0590',
                },
                {
                    type: 'Losing',
                    count: 195,
                    countPercent: '0.0435',
                    amount: '0',
                    amountPercent: '0.0000',
                },
                {
                    type: 'Cancel',
                    count: 1,
                    countPercent: '0.0002',
                    amount: '0',
                    amountPercent: '0.0000',
                },
                {
                    type: 'InProgress',
                    count: 4160,
                    countPercent: '0.9282',
                    amount: '1451592.5678',
                    amountPercent: '0.9410',
                },
                {
                    type: 'HighOpportunity',
                    count: 2,
                    countPercent: '0.0004',
                    amount: '0',
                    amountPercent: '0.0000',
                },
                {
                    type: 'MediumHighOpportunity',
                    count: 35,
                    countPercent: '0.0078',
                    amount: '0',
                    amountPercent: '0.0000',
                },
                {
                    type: 'MediumHighOpportunity_99_75',
                    count: 35,
                    countPercent: '0.0078',
                    amount: '0',
                    amountPercent: '0.0000',
                },
                {
                    type: 'MediumHighOpportunity_75_99',
                    count: 0,
                    countPercent: '0.0000',
                    amount: '0',
                    amountPercent: '0.0000',
                },
                {
                    type: 'MediumHighOpportunity_99_50',
                    count: 0,
                    countPercent: '0.0000',
                    amount: '0',
                    amountPercent: '0.0000',
                },
                {
                    type: 'MediumOpportunity',
                    count: 106,
                    countPercent: '0.0237',
                    amount: '2',
                    amountPercent: '0.0000',
                },
                {
                    type: 'MediumOpportunity_99_25',
                    count: 106,
                    countPercent: '0.0237',
                    amount: '2',
                    amountPercent: '0.0000',
                },
                {
                    type: 'OtherOpportunity',
                    count: 3567,
                    countPercent: '0.7959',
                    amount: '1450133',
                    amountPercent: '0.9401',
                },
                {
                    type: 'Others',
                    count: 4,
                    countPercent: '0.0009',
                    amount: '0',
                    amountPercent: '0.0000',
                },
            ],
        };
        expect(result).toEqual(expected);
    });
});
