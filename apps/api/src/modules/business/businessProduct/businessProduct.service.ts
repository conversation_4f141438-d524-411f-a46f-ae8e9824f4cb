import { BusinessProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProduct.model';
import { BusinessProductType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProductType.model';
import { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { EntityManager, FilterQuery } from '@mikro-orm/core';
import _ from 'lodash';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { EntityUpdater } from '~/utils/providers/entityUpdater.provider';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CommonInput, CreateInput, UpdateInput } from './businessProduct.input';
import { FilterInput } from './businessProduct.type';

@Service()
export class BusinessProductService extends BaseService<BusinessProduct> {
    protected entity = BusinessProduct;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: BusinessProduct[] }> {
        const em = this.em.fork();

        const where: FilterQuery<BusinessProduct>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();

        // Default
        this.appendFilterByDefault(where, params);
        // Filter input
        this.appendFilterByParams(where, params);

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['salesTeamGroup', 'type'],
                orderBy: [{ type: { viewOrder: 'ASC' } }, { viewOrder: 'ASC' }, { id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    private appendFilterByDefault(where: FilterQuery<BusinessProduct>[], params?: FilterInput) {
        where.push({ deleted: false });

        // For use cases
        if (!(params?.id || params?.ids || !_.isUndefined(params?.isActive))) {
            // http://asking.clinico.com.tw/issues/69958
            // 預設：業務系統不可查詢到「停用」的商機商品
            where.push({ isActive: true });
        }
    }

    private appendFilterByParams(where: FilterQuery<BusinessProduct>[], params?: FilterInput) {
        if (params?.competitorId) {
            where.push({
                competitorsBusinessProducts: { competitor: { id: params.competitorId } },
            });
        }
        if (params?.salesTeamId) {
            where.push({ salesTeamsBusinessProducts: { salesTeam: { id: params.salesTeamId } } });
        }
        if (params?.salesTeamIds) {
            where.push({
                salesTeamsBusinessProducts: { salesTeam: { id: { $in: params.salesTeamIds } } },
            });
        }
        if (params?.marketActivityId) {
            where.push({
                marketActivityBusinessProducts: { marketActivity: { id: params.marketActivityId } },
            });
        }

        if (params?.productLineIds && params.productLineIds.length) {
            where.push({
                businessProductsToProductLines: {
                    productLine: {
                        id: { $in: params.productLineIds },
                        deleted: false,
                    },
                },
            });
        }
    }

    async create(params: CreateInput, options?: { ctx?: Context }): Promise<BusinessProduct> {
        const em = this.em.fork();

        const row = new this.entity();
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const createdRow = await this.findOneOrError({ id: row.id });
        return createdRow;
    }

    async update(params: UpdateInput, options?: { ctx?: Context }): Promise<BusinessProduct> {
        const em = this.em.fork();

        const row = await this.findOneOrError({ id: params.id });
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const updatedRow = await this.findOneOrError({ id: row.id });
        return updatedRow;
    }

    private async mutate(
        params: { em: EntityManager; ent: BusinessProduct; input: CommonInput },
        options?: { ctx?: Context }
    ): Promise<BusinessProduct> {
        const { em, ent, input } = params;

        const updater = new EntityUpdater<BusinessProduct>({ ent, em });
        updater.updateSimpleColumns(input);
        updater.updateToOne({ key: 'type', ref: BusinessProductType, id: input.typeId });
        updater.updateToOne({
            key: 'salesTeamGroup',
            ref: SalesTeamGroup,
            id: input.salesTeamGroupId ?? options?.ctx?.currentSalesTeamGroup?.id,
        });

        return ent;
    }
}
