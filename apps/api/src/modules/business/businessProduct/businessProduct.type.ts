import type { BusinessProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProduct.model';
import { IBusinessProduct } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessProduct.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BusinessProduct', { implements: IBusinessProduct })
export class TBusinessProduct extends IBusinessProduct {}

@ObjectType('PaginatedBusinessProducts')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusinessProduct], { nullable: true })
    businessProducts: BusinessProduct[];
}

@InputType('BusinessProductFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field({ nullable: true, description: '名稱' })
    name?: string;

    @Field(() => ID, { nullable: true, description: '競爭商品' })
    competitorId?: number;

    @Field(() => ID, { nullable: true, description: '業務團隊' })
    salesTeamId?: number;

    @Field(() => [ID], { nullable: true, description: '業務團隊（複選）' })
    salesTeamIds?: number[];

    @Field({
        nullable: true,
        defaultValue: true,
        description: '是否啟用（查詢條件設置為 Null 時，同時取得「啟用」及「禁用」的資料）',
    })
    isActive?: boolean;

    @Field(() => ID, { nullable: true })
    marketActivityId?: number;

    @Field(() => [ID], { nullable: true, description: '产品线' })
    productLineIds?: number[];
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
