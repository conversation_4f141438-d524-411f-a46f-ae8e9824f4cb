import { Field, ID, InputType } from 'type-graphql';

@InputType('BusinessProductInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field(() => ID, { nullable: true })
    typeId?: number;

    deleted?: boolean;
}

@InputType('BusinessProductCreateInput')
export class CreateInput extends CommonInput {}

@InputType('BusinessProductUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
