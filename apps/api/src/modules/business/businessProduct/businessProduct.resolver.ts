import type { InventoryMaterial } from '@clinico/mikro-orm-persistence/models/inventory/material.model';
import type { BusinessProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProduct.model';
import type { BusinessProductType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProductType.model';
import { ProductLine } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/productLine.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { TMaterial } from '~/modules/material/material/material.type';
import { ProductLineService } from '~/modules/productLine/productLine.service';
import { TProductLine } from '~/modules/productLine/productLine.type';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessProductTypeService } from '../businessProductType/businessProductType.service';
import { TBusinessProductType } from '../businessProductType/businessProductType.type';
import { CreateInput, UpdateInput } from './businessProduct.input';
import { BusinessProductService } from './businessProduct.service';
import { PaginatedObjects, SearchArgs, TBusinessProduct } from './businessProduct.type';

@Service()
@Resolver(() => TBusinessProduct)
export class BusinessProductResolver {
    @Inject()
    private service: BusinessProductService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBusinessProducts' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; businessProducts: BusinessProduct[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, businessProducts: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBusinessProduct], { name: 'businessProducts' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<BusinessProduct[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBusinessProduct, { name: 'businessProduct' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<BusinessProduct> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBusinessProduct, { name: 'createBusinessProduct' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<BusinessProduct> {
        const row = await this.service.create(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBusinessProduct, { name: 'updateBusinessProduct' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<BusinessProduct> {
        const row = await this.service.update(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteBusinessProduct' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        await this.service.update({ id, deleted: true });
        return true;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: BusinessProduct): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }

    @Inject()
    private businessProductTypeService: BusinessProductTypeService;
    @FieldResolver(() => TBusinessProductType, { description: '商機商品類型', nullable: true })
    async type(@Root() model: BusinessProduct): Promise<BusinessProductType | undefined> {
        return await this.businessProductTypeService.findOne({ id: model.type?.id });
    }

    @FieldResolver(() => [TMaterial], { description: '料號' })
    async materials(@Root() model: BusinessProduct): Promise<InventoryMaterial[]> {
        if (!model.businessProductsMaterials.isInitialized()) {
            await model.businessProductsMaterials.init({
                populate: ['material'],
                where: { material: { deleted: false } },
            });
        }
        return model.businessProductsMaterials.getItems().map((v) => v.material);
    }

    @FieldResolver(() => [TProductLine], { nullable: true, description: '产品线' })
    async productLine(@Root() model: BusinessProduct): Promise<ProductLine[]> {
        if (!model.businessProductsToProductLines.isInitialized()) {
            await model.businessProductsToProductLines.init({
                populate: ['productLine'],
                where: { productLine: { deleted: false } },
            });
        }
        return model.businessProductsToProductLines.getItems().map((v) => v.productLine);
    }
}
