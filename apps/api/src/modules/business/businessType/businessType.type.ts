import type { BusinessType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessType.model';
import { IBusinessType } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessType.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BusinessType', { implements: IBusinessType })
export class TBusinessType extends IBusinessType {}

@ObjectType('PaginatedBusinessTypes')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusinessType], { nullable: true })
    businessTypes: BusinessType[];
}

@InputType('BusinessTypeFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
