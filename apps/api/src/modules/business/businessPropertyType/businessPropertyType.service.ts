import { BusinessPropertyType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessPropertyType.model';
import { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { Entity<PERSON>anager, FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { EntityUpdater } from '~/utils/providers/entityUpdater.provider';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CommonInput, CreateInput, UpdateInput } from './businessPropertyType.input';
import { FilterInput } from './businessPropertyType.type';

@Service()
export class BusinessPropertyTypeService extends BaseService<BusinessPropertyType> {
    protected entity = BusinessPropertyType;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: BusinessPropertyType[] }> {
        const em = this.em.fork();

        const where: FilterQuery<BusinessPropertyType>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['salesTeamGroup'],
                orderBy: [{ viewOrder: 'ASC' }, { id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    async create(params: CreateInput, options?: { ctx?: Context }): Promise<BusinessPropertyType> {
        const em = this.em.fork();

        const row = new this.entity();
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const createdRow = await this.findOneOrError({ id: row.id });
        return createdRow;
    }

    async update(params: UpdateInput, options?: { ctx?: Context }): Promise<BusinessPropertyType> {
        const em = this.em.fork();

        const row = await this.findOneOrError({ id: params.id });
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const updatedRow = await this.findOneOrError({ id: row.id });
        return updatedRow;
    }

    private async mutate(
        params: { em: EntityManager; ent: BusinessPropertyType; input: CommonInput },
        options?: { ctx?: Context }
    ): Promise<BusinessPropertyType> {
        const { em, ent, input } = params;

        const updater = new EntityUpdater<BusinessPropertyType>({ ent, em });
        updater.updateSimpleColumns(input);
        updater.updateToOne({
            key: 'salesTeamGroup',
            ref: SalesTeamGroup,
            id: input.salesTeamGroupId ?? options?.ctx?.currentSalesTeamGroup?.id,
        });

        return ent;
    }
}
