import type { BusinessProperty } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProperty.model';
import type { BusinessPropertyType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessPropertyType.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessPropertyService } from '../businessProperty/businessProperty.service';
import { TBusinessProperty } from '../businessProperty/businessProperty.type';
import { CreateInput, UpdateInput } from './businessPropertyType.input';
import { BusinessPropertyTypeService } from './businessPropertyType.service';
import { PaginatedObjects, SearchArgs, TBusinessPropertyType } from './businessPropertyType.type';

@Service()
@Resolver(() => TBusinessPropertyType)
export class BusinessPropertyTypeResolver {
    @Inject()
    private service: BusinessPropertyTypeService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBusinessPropertyTypes' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; businessPropertyTypes: BusinessPropertyType[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, businessPropertyTypes: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBusinessPropertyType], { name: 'businessPropertyTypes' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<BusinessPropertyType[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBusinessPropertyType, { name: 'businessPropertyType' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<BusinessPropertyType> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBusinessPropertyType, { name: 'createBusinessPropertyType' })
    async create(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<BusinessPropertyType> {
        const row = await this.service.create(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBusinessPropertyType, { name: 'updateBusinessPropertyType' })
    async update(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<BusinessPropertyType> {
        const row = await this.service.update(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteBusinessPropertyType' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        await this.service.update({ id, deleted: true });
        return true;
    }

    @Inject()
    private businessPropertyService: BusinessPropertyService;
    @FieldResolver(() => [TBusinessProperty], { description: '商機屬性' })
    async properties(@Root() model: BusinessPropertyType): Promise<BusinessProperty[]> {
        return this.businessPropertyService.findByTypeId(model.id);
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: BusinessPropertyType): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }
}
