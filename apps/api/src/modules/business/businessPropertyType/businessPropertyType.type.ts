import type { BusinessPropertyType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessPropertyType.model';
import { IBusinessPropertyType } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessPropertyType.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BusinessPropertyType', { implements: IBusinessPropertyType })
export class TBusinessPropertyType extends IBusinessPropertyType {}

@ObjectType('PaginatedBusinessPropertyTypes')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusinessPropertyType], { nullable: true })
    businessPropertyTypes: BusinessPropertyType[];
}

@InputType('BusinessPropertyTypeFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
