import { registerEnumType } from 'type-graphql';

export enum EnumBusinessTransactionOpportunity {
    /** 高（99%成單_99%贏單） */
    High = 'High',
    /** 中高（75%/99%成單_99/75%贏單_50/99%成單） */
    MediumHigh = 'MediumHigh',
    /** 中高（99%成單_75%贏單） */
    MediumHigh_99_75 = 'MediumHigh_99_75',
    /** 中高（75%成單_99%贏單） */
    MediumHigh_75_99 = 'MediumHigh_75_99',
    /** 中高（99%成單_50%贏單） for TW達輝 */
    MediumHigh_99_50 = 'MediumHigh_99_50',
    /** 中（75%成單_75%贏單） */
    Medium = 'Medium',
    /** 中（99%成單_25%贏單）  for TW達輝 */
    Medium_99_25 = 'Medium_99_25',
    /** 其他 */
    Others = 'Others',
}
registerEnumType(EnumBusinessTransactionOpportunity, {
    name: 'EnumBusinessTransactionOpportunity',
    description: '成交機會',
    valuesConfig: {
        High: { description: '高（99%成單_99%贏單）' },
        MediumHigh: { description: '中高（75%/99%成單_99/75%贏單）' },
        MediumHigh_99_75: { description: '中高（99%成單_75%贏單）' },
        MediumHigh_75_99: { description: '中高（75%成單_99%贏單）' },
        MediumHigh_99_50: { description: '中高（99%成單_50%贏單）' },
        Medium: { description: '中（75%成單_75%贏單）' },
        Medium_99_25: { description: '中（99%成單_25%贏單）' },
        Others: { description: '其他' },
    },
});

export enum EnumBusinessOpportunityCode {
    '0%' = '0%',
    '25%' = '25%',
    '50%' = '50%',
    '75%' = '75%',
    '99%' = '99%',
}
registerEnumType(EnumBusinessOpportunityCode, {
    name: 'EnumBusinessOpportunityCode',
    description: '商機機會編號',
    valuesConfig: {
        '0%': { description: '0%' },
        '25%': { description: '25%' },
        '50%': { description: '50%' },
        '75%': { description: '75%' },
        '99%': { description: '99%' },
    },
});
