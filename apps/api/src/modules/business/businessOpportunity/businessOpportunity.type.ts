import type { BusinessOpportunity } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessOpportunity.model';
import { IBusinessOpportunity } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessOpportunity.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BusinessOpportunity', { implements: IBusinessOpportunity })
export class TBusinessOpportunity extends IBusinessOpportunity {}

@ObjectType('PaginatedBusinessOpportunities')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusinessOpportunity], { nullable: true })
    businessOpportunities: BusinessOpportunity[];
}

@InputType('BusinessOpportunityFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field({ nullable: true, description: '是否啟用於前端畫面顯示' })
    enabled?: boolean;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
