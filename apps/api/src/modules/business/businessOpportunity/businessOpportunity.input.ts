import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('BusinessOpportunityInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    code?: string;

    @Field(() => Int, { nullable: true })
    viewOrder?: number;

    @Field({ nullable: true })
    enabled?: boolean;

    deleted?: boolean;
}

@InputType('BusinessOpportunityCreateInput')
export class CreateInput extends CommonInput {}

@InputType('BusinessOpportunityUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
