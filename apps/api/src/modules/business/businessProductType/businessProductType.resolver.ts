import type { BusinessProductType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProductType.model';
import { Arg, Args, Ctx, ID, Info, Mutation, Query, Resolver } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessProductTypeService } from './businessProductType.service';
import { PaginatedObjects, SearchArgs, TBusinessProductType } from './businessProductType.type';

@Service()
@Resolver(() => TBusinessProductType)
export class BusinessProductTypeResolver {
    @Inject()
    private service: BusinessProductTypeService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBusinessProductTypes' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; businessProductTypes: BusinessProductType[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, businessProductTypes: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBusinessProductType], { name: 'businessProductTypes' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<BusinessProductType[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBusinessProductType, { name: 'businessProductType' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<BusinessProductType> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }
}
