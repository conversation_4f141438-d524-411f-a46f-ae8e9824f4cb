import { BusinessProductType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProductType.model';
import { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { EntityManager, FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { EntityUpdater } from '~/utils/providers/entityUpdater.provider';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CommonInput, CreateInput, UpdateInput } from './businessProductType.input';
import { FilterInput } from './businessProductType.type';

@Service()
export class BusinessProductTypeService extends BaseService<BusinessProductType> {
    protected entity = BusinessProductType;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: BusinessProductType[] }> {
        const em = this.em.fork();

        const where: FilterQuery<BusinessProductType>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['salesTeamGroup'],
                orderBy: [{ viewOrder: 'ASC' }, { id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    async create(params: CreateInput, options?: { ctx?: Context }): Promise<BusinessProductType> {
        const em = this.em.fork();

        const row = new this.entity();
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const createdRow = await this.findOneOrError({ id: row.id });
        return createdRow;
    }

    async update(params: UpdateInput, options?: { ctx?: Context }): Promise<BusinessProductType> {
        const em = this.em.fork();

        const row = await this.findOneOrError({ id: params.id });
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const updatedRow = await this.findOneOrError({ id: row.id });
        return updatedRow;
    }

    private async mutate(
        params: { em: EntityManager; ent: BusinessProductType; input: CommonInput },
        options?: { ctx?: Context }
    ): Promise<BusinessProductType> {
        const { em, ent, input } = params;

        const updater = new EntityUpdater<BusinessProductType>({ ent, em });
        updater.updateSimpleColumns(input);
        updater.updateToOne({
            key: 'salesTeamGroup',
            ref: SalesTeamGroup,
            id: input.salesTeamGroupId ?? options?.ctx?.currentSalesTeamGroup?.id,
        });

        return ent;
    }
}
