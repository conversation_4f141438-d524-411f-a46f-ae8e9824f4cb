import type { BusinessProductType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProductType.model';
import { IBusinessProductType } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessProductType.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BusinessProductType', { implements: IBusinessProductType })
export class TBusinessProductType extends IBusinessProductType {}

@ObjectType('PaginatedBusinessProductTypes')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusinessProductType], { nullable: true })
    businessProductTypes: BusinessProductType[];
}

@InputType('BusinessProductTypeFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
