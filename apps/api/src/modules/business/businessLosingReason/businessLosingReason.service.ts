import { BusinessLosingReason } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessLosingReason.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './businessLosingReason.type';

@Service()
export class BusinessLosingReasonService extends BaseService<BusinessLosingReason> {
    protected entity = BusinessLosingReason;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: BusinessLosingReason[] }> {
        const em = this.em.fork();

        const where: FilterQuery<BusinessLosingReason>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        if (params?.businessId) {
            where.push({ businessesLosingReasons: { business: { id: params.businessId } } });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['salesTeamGroup'],
                orderBy: [{ viewOrder: 'ASC' }, { id: 'DESC' }],
            }
        );

        return { rows, count };
    }
}
