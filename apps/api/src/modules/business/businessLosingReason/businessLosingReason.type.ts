import type { BusinessLosingReason } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessLosingReason.model';
import { IBusinessLosingReason } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessLosingReason.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BusinessLosingReason', { implements: IBusinessLosingReason })
export class TBusinessLosingReason extends IBusinessLosingReason {}

@ObjectType('PaginatedBusinessLosingReasons')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusinessLosingReason], { nullable: true })
    businessLosingReasons: BusinessLosingReason[];
}

@InputType('BusinessLosingReasonFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    businessId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
