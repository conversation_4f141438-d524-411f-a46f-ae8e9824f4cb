import type { BusinessLosingReason } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessLosingReason.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessLosingReasonService } from './businessLosingReason.service';
import { PaginatedObjects, SearchArgs, TBusinessLosingReason } from './businessLosingReason.type';

@Service()
@Resolver(() => TBusinessLosingReason)
export class BusinessLosingReasonResolver {
    @Inject()
    private service: BusinessLosingReasonService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBusinessLosingReasons' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; businessLosingReasons: BusinessLosingReason[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, businessLosingReasons: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBusinessLosingReason], { name: 'businessLosingReasons' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<BusinessLosingReason[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBusinessLosingReason, { name: 'businessLosingReason' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<BusinessLosingReason> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: BusinessLosingReason): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }
}
