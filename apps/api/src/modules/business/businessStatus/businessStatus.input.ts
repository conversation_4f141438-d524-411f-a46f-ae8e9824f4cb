import { EnumBusinessStatusType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessStatus.model';
import { EnumBusinessStatusType as IEnumBusinessStatusType } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessStatus.model';
import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('BusinessStatusInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field({ nullable: true })
    name?: string;

    @Field(() => IEnumBusinessStatusType, { nullable: true })
    type?: EnumBusinessStatusType;

    @Field(() => Int, { nullable: true })
    viewOrder?: number;

    deleted?: boolean;
}

@InputType('BusinessStatusCreateInput')
export class CreateInput extends CommonInput {}

@InputType('BusinessStatusUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
