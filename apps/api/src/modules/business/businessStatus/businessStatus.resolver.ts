import type { BusinessStatus } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessStatus.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { CreateInput, UpdateInput } from './businessStatus.input';
import { BusinessStatusService } from './businessStatus.service';
import { PaginatedObjects, SearchArgs, TBusinessStatus } from './businessStatus.type';

@Service()
@Resolver(() => TBusinessStatus)
export class BusinessStatusResolver {
    @Inject()
    private service: BusinessStatusService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBusinessStatuses' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; businessStatuses: BusinessStatus[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, businessStatuses: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBusinessStatus], { name: 'businessStatuses' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<BusinessStatus[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBusinessStatus, { name: 'businessStatus' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<BusinessStatus> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBusinessStatus, { name: 'createBusinessStatus' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<BusinessStatus> {
        const row = await this.service.create(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBusinessStatus, { name: 'updateBusinessStatus' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<BusinessStatus> {
        const row = await this.service.update(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteBusinessStatus' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        await this.service.update({ id, deleted: true });
        return true;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: BusinessStatus): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }
}
