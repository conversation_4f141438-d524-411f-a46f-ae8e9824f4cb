import type { BusinessStatus } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessStatus.model';
import { IBusinessStatus } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessStatus.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BusinessStatus', { implements: IBusinessStatus })
export class TBusinessStatus extends IBusinessStatus {}

@ObjectType('PaginatedBusinessStatuses')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusinessStatus], { nullable: true })
    businessStatuses: BusinessStatus[];
}

@InputType('BusinessStatusFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
