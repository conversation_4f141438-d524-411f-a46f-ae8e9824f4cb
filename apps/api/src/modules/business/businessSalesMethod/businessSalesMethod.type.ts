import type { BusinessSalesMethod } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessSalesMethod.model';
import { IBusinessSalesMethod } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessSalesMethod.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BusinessSalesMethod', { implements: IBusinessSalesMethod })
export class TBusinessSalesMethod extends IBusinessSalesMethod {}

@ObjectType('PaginatedBusinessSalesMethods')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusinessSalesMethod], { nullable: true })
    businessSalesMethods: BusinessSalesMethod[];
}

@InputType('BusinessSalesMethodFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
