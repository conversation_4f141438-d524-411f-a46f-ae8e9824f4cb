import { BusinessesToBudgetProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessesToBudgetProduct.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { FilterInput } from '~/modules/business/businessToBudgetProduct/businessToBudgetProduct.input';
import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

@Service()
export class BusinessesToBudgetProductService extends BaseService<BusinessesToBudgetProduct> {
    protected entity = BusinessesToBudgetProduct;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: BusinessesToBudgetProduct[] }> {
        const em = this.em.fork();

        const where: FilterQuery<BusinessesToBudgetProduct> = new FilterBuilder({
            em,
            ent: this.entity,
            filters: params,
        }).build();

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }
}
