import { Field, ID, InputType, Int } from 'type-graphql';

import { BaseFilterInput } from '~/utils/types/base.type';

@InputType('BusinessBudgetProductFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    businessId?: number;
}

@InputType('BusinessBudgetProductInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    budgetProductId?: number;

    @Field(() => Int, { nullable: true, defaultValue: 1 })
    qty?: number;
}
