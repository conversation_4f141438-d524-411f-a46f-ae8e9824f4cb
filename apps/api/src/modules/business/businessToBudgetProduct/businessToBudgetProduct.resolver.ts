import type { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import type { BusinessProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProduct.model';
import type { BusinessesToBudgetProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessesToBudgetProduct.model';
import { FieldResolver, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { BusinessService } from '../business/business.service';
import { TBusiness } from '../business/business.type';
import { BusinessProductService } from '../businessProduct/businessProduct.service';
import { TBusinessProduct } from '../businessProduct/businessProduct.type';
import { TBusinessesToBudgetProduct } from './businessToBudgetProduct.type';

@Service()
@Resolver(() => TBusinessesToBudgetProduct)
export class BusinessesToBudgetProductResolver {
    @Inject()
    private businessService: BusinessService;
    @FieldResolver(() => TBusiness, { description: '商機' })
    async business(@Root() model: BusinessesToBudgetProduct): Promise<Business | undefined> {
        return await this.businessService.findOne({ id: model.businessId });
    }

    @Inject()
    private businessProductService: BusinessProductService;
    @FieldResolver(() => TBusinessProduct, { description: '預算商品' })
    async budgetProduct(
        @Root() model: BusinessesToBudgetProduct
    ): Promise<BusinessProduct | undefined> {
        return await this.businessProductService.findOne({ id: model.budgetProductId });
    }
}
