import { Field, ID, InputType, Int } from 'type-graphql';

import { BaseFilterInput } from '~/utils/types/base.type';

@InputType('BusinessDealProductFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    businessId?: number;
}

@InputType('BusinessDealProductInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    dealProductId?: number;

    @Field(() => Int, { nullable: true, defaultValue: 1 })
    qty?: number;
}
