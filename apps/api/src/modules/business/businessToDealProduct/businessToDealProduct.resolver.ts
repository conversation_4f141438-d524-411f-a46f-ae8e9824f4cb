import type { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import type { BusinessProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProduct.model';
import type { BusinessesToDealProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessesToDealProduct.model';
import { FieldResolver, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { BusinessService } from '../business/business.service';
import { TBusiness } from '../business/business.type';
import { BusinessProductService } from '../businessProduct/businessProduct.service';
import { TBusinessProduct } from '../businessProduct/businessProduct.type';
import { TBusinessesToDealProduct } from './businessToDealProduct.type';

@Service()
@Resolver(() => TBusinessesToDealProduct)
export class BusinessesToDealProductResolver {
    @Inject()
    private businessService: BusinessService;
    @FieldResolver(() => TBusiness, { description: '商機' })
    async business(@Root() model: BusinessesToDealProduct): Promise<Business | undefined> {
        return await this.businessService.findOne({ id: model.businessId });
    }

    @Inject()
    private businessProductService: BusinessProductService;
    @FieldResolver(() => TBusinessProduct, { description: '成交商品' })
    async dealProduct(
        @Root() model: BusinessesToDealProduct
    ): Promise<BusinessProduct | undefined> {
        return await this.businessProductService.findOne({ id: model.dealProductId });
    }
}
