import type { BusinessProperty } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProperty.model';
import type { BusinessPropertyType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessPropertyType.model';
import { IBusinessProperty } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessProperty.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

import { TBusinessPropertyType } from '../businessPropertyType/businessPropertyType.type';

@ObjectType('BusinessProperty', { implements: IBusinessProperty })
export class TBusinessProperty extends IBusinessProperty {}

@ObjectType('PaginatedBusinessProperties')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusinessProperty], { nullable: true })
    businessProperties: BusinessProperty[];
}
@ObjectType('FormattedBusinessProperties')
export class FormattedProperty {
    @Field(() => TBusinessPropertyType, { nullable: true })
    propertyType: BusinessPropertyType;

    @Field(() => [TBusinessProperty], { nullable: true })
    properties: BusinessProperty[];
}

@InputType('BusinessPropertyFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    businessId?: number;

    @Field(() => [ID], { nullable: true })
    typeIds?: number[];
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
