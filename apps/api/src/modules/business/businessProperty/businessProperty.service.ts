import { BusinessProperty } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProperty.model';
import { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { EntityManager, FilterQuery } from '@mikro-orm/core';
import DataLoader from 'dataloader';
import _ from 'lodash';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { EntityUpdater } from '~/utils/providers/entityUpdater.provider';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CommonInput, CreateInput, UpdateInput } from './businessProperty.input';
import { FilterInput } from './businessProperty.type';

@Service()
export class BusinessPropertyService extends BaseService<BusinessProperty> {
    protected entity = BusinessProperty;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: BusinessProperty[] }> {
        const em = this.em.fork();

        const where: FilterQuery<BusinessProperty>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        if (params?.businessId) {
            where.push({ businessesProperties: { business: { id: params.businessId } } });
        }
        if (params?.typeIds) {
            where.push({ type: { id: { $in: params.typeIds } } });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['salesTeamGroup', 'type'],
                orderBy: [{ type: { viewOrder: 'ASC' } }, { viewOrder: 'ASC' }, { id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    private _findByTypeId = new DataLoader(
        async (typeIds: readonly BusinessProperty['type']['id'][]) => {
            const { rows } = await this.search({
                typeIds: typeIds as BusinessProperty['type']['id'][],
            });

            const grouped = _.groupBy(rows, 'type.id');
            return typeIds.map((id) => grouped[id] ?? []);
        },
        { cache: false, batchScheduleFn: (fn) => setTimeout(fn, 100) }
    );
    async findByTypeId(typeId: BusinessProperty['type']['id']): Promise<BusinessProperty[]> {
        if (_.isNil(typeId)) return [];

        const rows = this._findByTypeId.load(typeId);
        return rows;
    }

    async create(params: CreateInput, options?: { ctx?: Context }): Promise<BusinessProperty> {
        const em = this.em.fork();

        const row = new this.entity();
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const createdRow = await this.findOneOrError({ id: row.id });
        return createdRow;
    }

    async update(params: UpdateInput, options?: { ctx?: Context }): Promise<BusinessProperty> {
        const em = this.em.fork();

        const row = await this.findOneOrError({ id: params.id });
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const updatedRow = await this.findOneOrError({ id: row.id });
        return updatedRow;
    }

    private async mutate(
        params: { em: EntityManager; ent: BusinessProperty; input: CommonInput },
        options?: { ctx?: Context }
    ): Promise<BusinessProperty> {
        const { em, ent, input } = params;

        const updater = new EntityUpdater<BusinessProperty>({ ent, em });
        updater.updateSimpleColumns(input);
        updater.updateToOne({
            key: 'salesTeamGroup',
            ref: SalesTeamGroup,
            id: input.salesTeamGroupId ?? options?.ctx?.currentSalesTeamGroup?.id,
        });

        return ent;
    }
}
