import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('BusinessPropertyInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field(() => ID, { nullable: true })
    typeId?: number;

    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    code?: string;

    @Field(() => Int, { nullable: true })
    viewOrder?: number;

    deleted?: boolean;
}

@InputType('BusinessPropertyCreateInput')
export class CreateInput extends CommonInput {}

@InputType('BusinessPropertyUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
