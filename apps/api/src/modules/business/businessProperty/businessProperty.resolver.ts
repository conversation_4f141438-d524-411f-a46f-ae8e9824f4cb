import type { BusinessProperty } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProperty.model';
import type { BusinessPropertyType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessPropertyType.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessPropertyTypeService } from '../businessPropertyType/businessPropertyType.service';
import { TBusinessPropertyType } from '../businessPropertyType/businessPropertyType.type';
import { CreateInput, UpdateInput } from './businessProperty.input';
import { BusinessPropertyService } from './businessProperty.service';
import { PaginatedObjects, SearchArgs, TBusinessProperty } from './businessProperty.type';

@Service()
@Resolver(() => TBusinessProperty)
export class businessPropertyResolver {
    @Inject()
    private service: BusinessPropertyService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBusinessProperties' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; businessProperties: BusinessProperty[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, businessProperties: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBusinessProperty], { name: 'businessProperties' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<BusinessProperty[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBusinessProperty, { name: 'businessProperty' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<BusinessProperty> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBusinessProperty, { name: 'createBusinessProperty' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<BusinessProperty> {
        const row = await this.service.create(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBusinessProperty, { name: 'updateBusinessProperty' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<BusinessProperty> {
        const row = await this.service.update(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteBusinessProperty' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        await this.service.update({ id, deleted: true });
        return true;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: BusinessProperty): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }

    @Inject()
    private businessPropertyTypeService: BusinessPropertyTypeService;
    @FieldResolver(() => TBusinessPropertyType, { description: '商機屬性類型' })
    async type(@Root() model: BusinessProperty): Promise<BusinessPropertyType | undefined> {
        return await this.businessPropertyTypeService.findOne({ id: model.type.id });
    }
}
