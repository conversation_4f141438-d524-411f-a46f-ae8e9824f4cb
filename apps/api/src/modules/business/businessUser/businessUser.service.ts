import { BusinessesUser } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessesUser.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './businessUser.input';

@Service()
export class BusinessUserService extends BaseService<BusinessesUser> {
    protected entity = BusinessesUser;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: BusinessesUser[] }> {
        const em = this.em.fork();

        const where: FilterQuery<BusinessesUser>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();

        if (params?.businessId) {
            where.push({ business: { id: params.businessId } });
        }
        if (params?.salesTeamId) {
            where.push({ salesTeam: { id: params.salesTeamId } });
        }
        if (params?.userId) {
            where.push({ user: { id: params.userId } });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['business', 'salesTeam', 'user'],
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }
}
