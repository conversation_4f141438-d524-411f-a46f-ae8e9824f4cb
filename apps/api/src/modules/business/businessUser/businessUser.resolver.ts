import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import type { BusinessesUser } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessesUser.model';
import type { SalesTeam } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeam.model';
import { FieldResolver, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { SalesTeamService } from '~/modules/salesTeam/salesTeam/salesTeam.service';
import { TSalesTeam } from '~/modules/salesTeam/salesTeam/salesTeam.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';

import { BusinessService } from '../business/business.service';
import { TBusiness } from '../business/business.type';
import { TBusinessesUser } from './businessUser.type';

@Service()
@Resolver(() => TBusinessesUser)
export class BusinessesUserResolver {
    @Inject()
    private businessService: BusinessService;
    @FieldResolver(() => TBusiness, { description: '商機' })
    async customer(@Root() model: BusinessesUser): Promise<Business | undefined> {
        return this.businessService.findOne({ id: model.business.id });
    }

    @Inject()
    private salesTeamService: SalesTeamService;
    @FieldResolver(() => TSalesTeam, { description: '業務團隊', nullable: true })
    async salesTeam(@Root() model: BusinessesUser): Promise<SalesTeam | undefined> {
        return await this.salesTeamService.findOne({ id: model.salesTeam?.id });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '負責（協助）業務' })
    async user(@Root() model: BusinessesUser): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.user.id });
    }
}
