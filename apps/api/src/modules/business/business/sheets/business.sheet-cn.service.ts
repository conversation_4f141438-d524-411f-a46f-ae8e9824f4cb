import { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import { SalesTeam } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeam.model';
import BigNumber from 'bignumber.js';
import _ from 'lodash';
import { DateTime } from 'luxon';
import { Service } from 'typedi';

@Service()
export class BusinessSheetCnService {
    render(params: { business: Business; dictToSalesTeam: Record<string, SalesTeam[]> }): any {
        const { business, dictToSalesTeam } = params;

        const salesTeams: SalesTeam[] = business.salesTeamUnit?.salesTeam?.id
            ? dictToSalesTeam[business.salesTeamUnit?.salesTeam.id]
            : [];
        const data = {
            商机編號: business.code || '-',
            内容: business.content || '-',
            商机类别: business.type?.name || '-',
            赢单机会: business.winningOpportunity?.name || '-',
            成单机会: (() => {
                const item: string[] = [];
                if (business.status?.buyingOpportunity) {
                    item.push(business.status.buyingOpportunity);
                }
                if (business.status?.name) {
                    item.push(business.status.name);
                }
                return item.length ? item.join(' - ') : '-';
            })(),
            欲购买商品: (() => {
                const items = business.businessesToBudgetProducts.getItems().map((item) => {
                    const val: string[] = [];
                    if (item.budgetProduct.brand) {
                        val.push(`[${item.budgetProduct.brand}]`);
                    }
                    if (item.budgetProduct.name) {
                        val.push(item.budgetProduct.name);
                    }
                    if (!_.isNil(item.qty)) {
                        val.push(`（数量：${item.qty}）`);
                    }
                    return val.join('');
                });
                return items.length ? items.join('\n') : '-';
            })(),
            预计结束日期: business.expectedClosedDate
                ? DateTime.fromJSDate(business.expectedClosedDate).toFormat('yyyy-MM-dd')
                : '-',
            年: business.expectedClosedDate
                ? DateTime.fromJSDate(business.expectedClosedDate).toFormat('yyyy')
                : '-',
            月: business.expectedClosedDate
                ? DateTime.fromJSDate(business.expectedClosedDate).toFormat('M')
                : '-',
            报价金额: (() => {
                if (business.budgetAmount) {
                    return new BigNumber(business.budgetAmount).toFormat(2);
                }
                return '-';
            })(),
            预算范围: (() => {
                const items = business.businessesProperties
                    .getItems()
                    .filter((item) => {
                        const { code } = item.businessProperty.type;
                        return code === 'Customer_Budget_Range';
                    })
                    .map((item) => item.businessProperty.name);
                return items.length ? items.join('\n') : '-';
            })(),
            竞争对手: (() => {
                const items = business.businessesCompetitors
                    .getItems()
                    .map((item) => item.competitor.name);
                return items.length ? items.join('\n') : '-';
            })(),
            需求科室: (() => {
                const items = business.businessesProperties
                    .getItems()
                    .filter((item) => {
                        const { code } = item.businessProperty.type;
                        return code === 'Section_In_Demand';
                    })
                    .map((item) => item.businessProperty.name);
                return items.length ? items.join('\n') : '-';
            })(),
            客户编号: business.customer?.code || '-',
            客户名称: business.customer?.name || '-',
            客户地址: business.customer?.address || '-',
            客戶联络人: (() => {
                const items = business.businessesPrimaryContactPeople.getItems().map((item) => {
                    const val: string[] = [];
                    if (item.contactPerson.name) {
                        val.push(item.contactPerson.name);
                    }
                    if (item.contactPerson.jobTitle) {
                        val.push(`（${item.contactPerson.jobTitle}）`);
                    }
                    return val.join('');
                });
                return items.length ? items.join('\n') : '-';
            })(),
            订单编号: business.orderCode || '-',
            成交商品: (() => {
                const items = business.businessesToDealProducts.getItems().map((item) => {
                    const val: string[] = [];
                    if (item.dealProduct.brand) {
                        val.push(`[${item.dealProduct.brand}]`);
                    }
                    if (item.dealProduct.name) {
                        val.push(item.dealProduct.name);
                    }
                    return val.join('');
                });
                return items.length ? items.join('\n') : '-';
            })(),
            结束日期: business.closedDate
                ? DateTime.fromJSDate(business.closedDate).toFormat('yyyy-MM-dd')
                : '-',
            成交价格: (() => {
                if (business.dealAmount) {
                    return new BigNumber(business.dealAmount).toFormat(2);
                }
                return '-';
            })(),
            负责业务: business.salesTeamUnit?.name || '-',
            事业部: salesTeams?.[0]?.name || '-',
            大区: salesTeams?.[1]?.name || '-',
            办事处: salesTeams?.[2]?.name || '-',
            跨部门协助业务: (() => {
                const items = business.businessesUsers.getItems().map((item) => {
                    const val: string[] = [];
                    if (item.user.name) {
                        val.push(item.user.name);
                    }
                    if (item.user.code) {
                        val.push(`（${item.user.code}）`);
                    }
                    return val.join('');
                });
                return items.length ? items.join('\n') : '-';
            })(),
            建立人员: (() => {
                const item: string[] = [];
                if (business.createdUser?.name) {
                    item.push(business.createdUser.name);
                }
                if (business.createdUser?.code) {
                    item.push(`（${business.createdUser.code}）`);
                }
                return item.length ? item.join('') : '-';
            })(),
            建立时间: (() => {
                if (business.updatedAt) {
                    const val = DateTime.fromJSDate(business.createdAt);
                    return val.toFormat('yyyy-MM-dd HH:mm:ss');
                }
                return '-';
            })(),
            编辑人员: (() => {
                const item: string[] = [];
                if (business.updatedUser?.name) {
                    item.push(business.updatedUser.name);
                }
                if (business.updatedUser?.code) {
                    item.push(`（${business.updatedUser.code}）`);
                }
                return item.length ? item.join('') : '-';
            })(),
            编辑时间: (() => {
                if (business.updatedAt) {
                    const val = DateTime.fromJSDate(business.updatedAt);
                    return val.toFormat('yyyy-MM-dd HH:mm:ss');
                }
                return '-';
            })(),
            报价单编号: business.eyeQuotationOrderCode || '-',
        };
        return data;
    }

    bulkRender(params: { businesses: Business[]; salesTeams: SalesTeam[] }): any[] {
        const { businesses, salesTeams } = params;

        const dictToSalesTeam = this.buildSalesTeamDict({ salesTeams });
        return businesses.map((business) => this.render({ business, dictToSalesTeam }));
    }

    private buildSalesTeamDict(params: { salesTeams: SalesTeam[] }): Record<string, SalesTeam[]> {
        const { salesTeams } = params;

        const dict: Record<string, SalesTeam[]> = {};
        const grouped = _.groupBy(salesTeams, 'id');
        for (const salesTeam of salesTeams) {
            const list: SalesTeam[] = [];

            let current = salesTeam;
            while (true) {
                list.unshift(current);
                if (!current.parent) break;
                const next = grouped[current.parent.id];
                if (!next) break;
                current = next[0];
            }
            dict[salesTeam.id] = list;
        }
        return dict;
    }
}
