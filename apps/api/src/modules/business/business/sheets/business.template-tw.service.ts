import { Utils } from '@clinico/clinico-node-framework';
import { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import { SalesTeam } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeam.model';
import path from 'node:path';
import Container, { Service } from 'typedi';

import { configs } from '~/app.config';

import { BusinessSheetTwService } from './business.sheet-tw.service';

@Service()
export class BusinessTemplateTwService {
    public template = new Utils.ExcelTemplater();

    private filepath = path.join(configs.dir.template, 'excel', 'business', 'businesses_tw.xlsx');
    async load() {
        await this.template.load(this.filepath);
        return this;
    }

    async render(params: {
        businesses: Business[];
        salesTeams: SalesTeam[];
        count: number | null;
        limit: number;
    }) {
        const startRowIndex = 2;
        this.template.sheet('商機清單');

        const sheetService = Container.get(BusinessSheetTwService);
        this.template.fillRows({
            startRowIndex: startRowIndex,
            rows: sheetService.bulkRender({
                businesses: params.businesses,
                salesTeams: params.salesTeams,
            }),
        });

        // Add warning message to footer if count exceeds limit
        if (params.count && params.count > params.limit) {
            const message = '#### 已達到匯出筆數上限，請嘗試縮小查詢範圍後重試。 ####';
            this.template.fillRows({
                startRowIndex: startRowIndex + params.limit,
                rows: [{ 商機編號: message }],
                rowStyle: { horizontalAlignment: 'left' },
            });
        }

        return this;
    }
}
