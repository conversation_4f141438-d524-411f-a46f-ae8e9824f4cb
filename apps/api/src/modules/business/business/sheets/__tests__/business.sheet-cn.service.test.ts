import { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import { SalesTeam } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeam.model';
import { DateTime } from 'luxon';

import { BusinessSheetCnService } from '../business.sheet-cn.service';

describe('BusinessSheetCnService', () => {
    let businessSheetService: BusinessSheetCnService;
    beforeEach(() => {
        businessSheetService = new BusinessSheetCnService();
    });

    describe('render', () => {
        test('should render business data correctly', () => {
            const business: any = {
                code: '123',
                content: 'Business Content',
                type: { name: 'Type Name' },
                winningOpportunity: { name: 'Winning Opportunity' },
                status: { buyingOpportunity: 'Buying Opportunity', name: 'Status Name' },
                businessesToBudgetProducts: {
                    getItems: () => [
                        {
                            budgetProduct: { brand: 'Product Brand', name: 'Product Name' },
                            qty: 10,
                        },
                    ],
                },
                expectedClosedDate: new Date('2023-06-01'),
                budgetAmount: 1000,
                businessesProperties: {
                    getItems: () => [
                        {
                            businessProperty: {
                                type: { code: 'Customer_Budget_Range' },
                                name: 'Customer Budget Range',
                            },
                        },
                        {
                            businessProperty: {
                                type: { code: 'Section_In_Demand' },
                                name: 'Section In Demand',
                            },
                        },
                    ],
                },
                businessesCompetitors: {
                    getItems: () => [{ competitor: { name: 'Competitor Name' } }],
                },
                businessesPrimaryContactPeople: {
                    getItems: () => [
                        {
                            contactPerson: {
                                name: 'Contact Person Name',
                                jobTitle: 'Contact Person Job Title',
                            },
                        },
                    ],
                },
                orderCode: 'Order Code',
                businessesToDealProducts: {
                    getItems: () => [
                        {
                            dealProduct: {
                                brand: 'Deal Product Brand',
                                name: 'Deal Product Name',
                            },
                        },
                    ],
                },
                closedDate: new Date('2023-06-02'),
                dealAmount: 2000,
                salesTeamUnit: {
                    name: 'salesTeamUnit name',
                    salesTeam: { id: 3 },
                },
                primaryUser: {
                    name: 'Primary User Name',
                    code: 'Primary User Code',
                    salesTeamsUsers: [{ salesTeam: { id: 3 } }],
                },
                businessesUsers: {
                    getItems: () => [
                        {
                            user: {
                                name: 'User Name',
                                code: 'User Code',
                            },
                        },
                    ],
                },
                createdUser: {
                    name: 'Created User Name',
                    code: 'Created User Code',
                },
                createdAt: new Date('2023-05-01T00:00:00.000Z'),
                updatedUser: {
                    name: 'Updated User Name',
                    code: 'Updated User Code',
                },
                updatedAt: new Date('2023-05-02T00:00:00.000Z'),
                eyeQuotationOrderCode: 'EyeQuotationOrderCode Code',
            };
            const dictToSalesTeam: any = {
                1: [{ name: 'SalesTeam 1' }],
                2: [{ name: 'SalesTeam 1' }, { name: 'SalesTeam 2' }],
                3: [{ name: 'SalesTeam 1' }, { name: 'SalesTeam 2' }, { name: 'SalesTeam 3' }],
            };
            const result = businessSheetService.render({ business, dictToSalesTeam });

            const expected = {
                商机編號: '123',
                内容: 'Business Content',
                商机类别: 'Type Name',
                赢单机会: 'Winning Opportunity',
                成单机会: 'Buying Opportunity - Status Name',
                欲购买商品: '[Product Brand]Product Name（数量：10）',
                预计结束日期: '2023-06-01',
                年: '2023',
                月: '6',
                报价金额: '1,000.00',
                预算范围: 'Customer Budget Range',
                竞争对手: 'Competitor Name',
                需求科室: 'Section In Demand',
                客户编号: '-',
                客户名称: '-',
                客户地址: '-',
                客戶联络人: 'Contact Person Name（Contact Person Job Title）',
                订单编号: 'Order Code',
                成交商品: '[Deal Product Brand]Deal Product Name',
                结束日期: '2023-06-02',
                成交价格: '2,000.00',
                负责业务: 'salesTeamUnit name',
                事业部: 'SalesTeam 1',
                大区: 'SalesTeam 2',
                办事处: 'SalesTeam 3',
                跨部门协助业务: 'User Name（User Code）',
                建立人员: 'Created User Name（Created User Code）',
                建立时间: DateTime.fromJSDate(business.createdAt).toFormat('yyyy-MM-dd HH:mm:ss'),
                编辑人员: 'Updated User Name（Updated User Code）',
                编辑时间: DateTime.fromJSDate(business.updatedAt).toFormat('yyyy-MM-dd HH:mm:ss'),
                报价单编号: 'EyeQuotationOrderCode Code',
            };

            expect(result).toEqual(expected);
        });

        test('should render default values when data is missing', () => {
            const business = new Business();
            const dictToSalesTeam: any = {};
            const result = businessSheetService.render({ business, dictToSalesTeam });

            const expected = {
                商机編號: '-',
                内容: '-',
                商机类别: '-',
                赢单机会: '-',
                成单机会: '-',
                欲购买商品: '-',
                预计结束日期: '-',
                年: '-',
                月: '-',
                报价金额: '-',
                预算范围: '-',
                竞争对手: '-',
                需求科室: '-',
                客户编号: '-',
                客户名称: '-',
                客户地址: '-',
                客戶联络人: '-',
                订单编号: '-',
                成交商品: '-',
                结束日期: '-',
                成交价格: '-',
                负责业务: '-',
                事业部: '-',
                大区: '-',
                办事处: '-',
                跨部门协助业务: '-',
                建立人员: '-',
                建立时间: '-',
                编辑人员: '-',
                编辑时间: '-',
                报价单编号: '-',
            };
            expect(result).toEqual(expected);
        });
    });

    describe('bulkRender', () => {
        test('should return correct values', () => {
            const businesses: any[] = [];
            businesses.push({
                code: 'BU000001',
                content: 'Business 1',
                salesTeamUnit: {
                    id: 1,
                    name: 'salesTeamUnit name 1',
                    salesTeam: { id: 3 },
                },
                primaryUser: {
                    id: 1,
                    name: 'User 1',
                    salesTeamsUsers: [{ salesTeam: { id: 3 } }],
                },
                businessesToBudgetProducts: { getItems: () => [] },
                businessesProperties: { getItems: () => [] },
                businessesCompetitors: { getItems: () => [] },
                businessesPrimaryContactPeople: { getItems: () => [] },
                businessesToDealProducts: { getItems: () => [] },
                businessesUsers: { getItems: () => [] },
            });
            businesses.push({
                code: 'BU000002',
                content: 'Business 2',
                salesTeamUnit: {
                    id: 2,
                    name: 'salesTeamUnit name 2',
                    salesTeam: { id: 2 },
                },
                primaryUser: {
                    id: 2,
                    name: 'User 2',
                    salesTeamsUsers: [{ salesTeam: { id: 2 } }],
                },
                businessesToBudgetProducts: { getItems: () => [] },
                businessesProperties: { getItems: () => [] },
                businessesCompetitors: { getItems: () => [] },
                businessesPrimaryContactPeople: { getItems: () => [] },
                businessesToDealProducts: { getItems: () => [] },
                businessesUsers: { getItems: () => [] },
            });

            const salesTeams: any[] = [
                { id: 1, name: 'SalesTeam 1' },
                { id: 2, name: 'SalesTeam 2', parent: { id: 1 } },
                { id: 3, name: 'SalesTeam 3', parent: { id: 2 } },
            ];

            const result = businessSheetService.bulkRender({ businesses, salesTeams });

            const expected = [
                {
                    商机編號: 'BU000001',
                    内容: 'Business 1',
                    商机类别: '-',
                    赢单机会: '-',
                    成单机会: '-',
                    欲购买商品: '-',
                    预计结束日期: '-',
                    年: '-',
                    月: '-',
                    报价金额: '-',
                    预算范围: '-',
                    竞争对手: '-',
                    需求科室: '-',
                    客户编号: '-',
                    客户名称: '-',
                    客户地址: '-',
                    客戶联络人: '-',
                    订单编号: '-',
                    成交商品: '-',
                    结束日期: '-',
                    成交价格: '-',
                    负责业务: 'salesTeamUnit name 1',
                    事业部: 'SalesTeam 1',
                    大区: 'SalesTeam 2',
                    办事处: 'SalesTeam 3',
                    跨部门协助业务: '-',
                    建立人员: '-',
                    建立时间: '-',
                    编辑人员: '-',
                    编辑时间: '-',
                    报价单编号: '-',
                },
                {
                    商机編號: 'BU000002',
                    内容: 'Business 2',
                    商机类别: '-',
                    赢单机会: '-',
                    成单机会: '-',
                    欲购买商品: '-',
                    预计结束日期: '-',
                    年: '-',
                    月: '-',
                    报价金额: '-',
                    预算范围: '-',
                    竞争对手: '-',
                    需求科室: '-',
                    客户编号: '-',
                    客户名称: '-',
                    客户地址: '-',
                    客戶联络人: '-',
                    订单编号: '-',
                    成交商品: '-',
                    结束日期: '-',
                    成交价格: '-',
                    负责业务: 'salesTeamUnit name 2',
                    事业部: 'SalesTeam 1',
                    大区: 'SalesTeam 2',
                    办事处: '-',
                    跨部门协助业务: '-',
                    建立人员: '-',
                    建立时间: '-',
                    编辑人员: '-',
                    编辑时间: '-',
                    报价单编号: '-',
                },
            ];
            expect(result).toEqual(expected);
        });

        test('should return an empty array when no data is provided', () => {
            const businesses: Business[] = [];
            const salesTeams: SalesTeam[] = [];
            const result = businessSheetService.bulkRender({ businesses, salesTeams });

            const expected = [];
            expect(result).toEqual(expected);
        });
    });

    describe('buildSalesTeamDict', () => {
        const service = BusinessSheetCnService.prototype['buildSalesTeamDict'];
        const salesTeams: any[] = [];
        salesTeams.push({ id: 1, name: 'SalesTeam 1' });
        salesTeams.push({ id: 2, name: 'SalesTeam 2', parent: { id: 1 } });
        salesTeams.push({ id: 3, name: 'SalesTeam 3', parent: { id: 2 } });
        const result = service({ salesTeams });

        const expected = {
            1: [{ id: 1, name: 'SalesTeam 1' }],
            2: [
                { id: 1, name: 'SalesTeam 1' },
                { id: 2, name: 'SalesTeam 2', parent: { id: 1 } },
            ],
            3: [
                { id: 1, name: 'SalesTeam 1' },
                { id: 2, name: 'SalesTeam 2', parent: { id: 1 } },
                { id: 3, name: 'SalesTeam 3', parent: { id: 2 } },
            ],
        };
        expect(result).toEqual(expected);
    });
});
