import { Utils } from '@clinico/clinico-node-framework';
import { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import { SalesTeam } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeam.model';
import path from 'node:path';
import Container, { Service } from 'typedi';

import { configs } from '~/app.config';

import { BusinessSheetCnService } from './business.sheet-cn.service';

@Service()
export class BusinessTemplateCnService {
    public template = new Utils.ExcelTemplater();

    private filepath = path.join(configs.dir.template, 'excel', 'business', 'businesses_cn.xlsx');
    async load() {
        await this.template.load(this.filepath);
        return this;
    }

    async render(params: {
        businesses: Business[];
        salesTeams: SalesTeam[];
        count: number | null;
        limit: number;
    }) {
        const startRowIndex = 2;
        this.template.sheet('商机清单');

        const sheetService = Container.get(BusinessSheetCnService);
        this.template.fillRows({
            startRowIndex: startRowIndex,
            rows: sheetService.bulkRender({
                businesses: params.businesses,
                salesTeams: params.salesTeams,
            }),
        });

        // Add warning message to footer if count exceeds limit
        if (params.count && params.count > params.limit) {
            const message = '#### 已达到汇出笔数上限，请尝试缩小查询范围后重试。 ####';
            this.template.fillRows({
                startRowIndex: startRowIndex + params.limit,
                rows: [{ 商机編號: message }],
                rowStyle: { horizontalAlignment: 'left' },
            });
        }

        return this;
    }
}
