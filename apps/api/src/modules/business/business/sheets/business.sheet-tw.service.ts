import { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import { SalesTeam } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeam.model';
import BigNumber from 'bignumber.js';
import _ from 'lodash';
import { DateTime } from 'luxon';
import { Service } from 'typedi';

@Service()
export class BusinessSheetTwService {
    render(params: { business: Business; dictToSalesTeam: Record<string, SalesTeam[]> }): any {
        const { business, dictToSalesTeam } = params;

        const salesTeams: SalesTeam[] = business.salesTeamUnit?.salesTeam?.id
            ? dictToSalesTeam[business.salesTeamUnit?.salesTeam.id]
            : [];
        const data = {
            商機編號: business.code || '-',
            内容: business.content || '-',
            商機類別: business.type?.name || '-',
            贏單機會: business.winningOpportunity?.name || '-',
            成單機會: (() => {
                const item: string[] = [];
                if (business.status?.buyingOpportunity) {
                    item.push(business.status.buyingOpportunity);
                }
                if (business.status?.name) {
                    item.push(business.status.name);
                }
                return item.length ? item.join(' - ') : '-';
            })(),
            欲購買商品: (() => {
                const items = business.businessesToBudgetProducts.getItems().map((item) => {
                    const val: string[] = [];
                    if (item.budgetProduct.brand) {
                        val.push(`[${item.budgetProduct.brand}]`);
                    }
                    if (item.budgetProduct.name) {
                        val.push(item.budgetProduct.name);
                    }
                    if (!_.isNil(item.qty)) {
                        val.push(`（數量：${item.qty}）`);
                    }
                    return val.join('');
                });
                return items.length ? items.join('\n') : '-';
            })(),
            預計結束日期: business.expectedClosedDate
                ? DateTime.fromJSDate(business.expectedClosedDate).toFormat('yyyy-MM-dd')
                : '-',
            年: business.expectedClosedDate
                ? DateTime.fromJSDate(business.expectedClosedDate).toFormat('yyyy')
                : '-',
            月: business.expectedClosedDate
                ? DateTime.fromJSDate(business.expectedClosedDate).toFormat('M')
                : '-',
            報價金額: (() => {
                if (business.budgetAmount) {
                    return new BigNumber(business.budgetAmount).toFormat(2);
                }
                return '-';
            })(),
            預算範圍: (() => {
                const items = business.businessesProperties
                    .getItems()
                    .filter((item) => {
                        const { code } = item.businessProperty.type;
                        return code === 'Customer_Budget_Range';
                    })
                    .map((item) => item.businessProperty.name);
                return items.length ? items.join('\n') : '-';
            })(),
            競爭對手: (() => {
                const items = business.businessesCompetitors
                    .getItems()
                    .map((item) => item.competitor.name);
                return items.length ? items.join('\n') : '-';
            })(),
            需求科室: (() => {
                const items = business.businessesProperties
                    .getItems()
                    .filter((item) => {
                        const { code } = item.businessProperty.type;
                        return code === 'Section_In_Demand';
                    })
                    .map((item) => item.businessProperty.name);
                return items.length ? items.join('\n') : '-';
            })(),
            客戶編號: business.customer?.code || '-',
            客戶名稱: business.customer?.name || '-',
            客戶地址: business.customer?.address || '-',
            客戶聯絡人: (() => {
                const items = business.businessesPrimaryContactPeople.getItems().map((item) => {
                    const val: string[] = [];
                    if (item.contactPerson.name) {
                        val.push(item.contactPerson.name);
                    }
                    if (item.contactPerson.jobTitle) {
                        val.push(`（${item.contactPerson.jobTitle}）`);
                    }
                    return val.join('');
                });
                return items.length ? items.join('\n') : '-';
            })(),
            訂單編號: business.orderCode || '-',
            成交商品: (() => {
                const items = business.businessesToDealProducts.getItems().map((item) => {
                    const val: string[] = [];
                    if (item.dealProduct.brand) {
                        val.push(`[${item.dealProduct.brand}]`);
                    }
                    if (item.dealProduct.name) {
                        val.push(item.dealProduct.name);
                    }
                    return val.join('');
                });
                return items.length ? items.join('\n') : '-';
            })(),
            結束日期: business.closedDate
                ? DateTime.fromJSDate(business.closedDate).toFormat('yyyy-MM-dd')
                : '-',
            成交價格: (() => {
                if (business.dealAmount) {
                    return new BigNumber(business.dealAmount).toFormat(2);
                }
                return '-';
            })(),
            負責業務: business.salesTeamUnit?.name || '-',
            事業部: salesTeams?.[0]?.name || '-',
            大區: salesTeams?.[1]?.name || '-',
            辦事處: salesTeams?.[2]?.name || '-',
            跨部門協助業務: (() => {
                const items = business.businessesUsers.getItems().map((item) => {
                    const val: string[] = [];
                    if (item.user.name) {
                        val.push(item.user.name);
                    }
                    if (item.user.code) {
                        val.push(`（${item.user.code}）`);
                    }
                    return val.join('');
                });
                return items.length ? items.join('\n') : '-';
            })(),
            建立人員: (() => {
                const item: string[] = [];
                if (business.createdUser?.name) {
                    item.push(business.createdUser.name);
                }
                if (business.createdUser?.code) {
                    item.push(`（${business.createdUser.code}）`);
                }
                return item.length ? item.join('') : '-';
            })(),
            建立時間: (() => {
                if (business.updatedAt) {
                    const val = DateTime.fromJSDate(business.createdAt);
                    return val.toFormat('yyyy-MM-dd HH:mm:ss');
                }
                return '-';
            })(),
            編輯人員: (() => {
                const item: string[] = [];
                if (business.updatedUser?.name) {
                    item.push(business.updatedUser.name);
                }
                if (business.updatedUser?.code) {
                    item.push(`（${business.updatedUser.code}）`);
                }
                return item.length ? item.join('') : '-';
            })(),
            編輯時間: (() => {
                if (business.updatedAt) {
                    const val = DateTime.fromJSDate(business.updatedAt);
                    return val.toFormat('yyyy-MM-dd HH:mm:ss');
                }
                return '-';
            })(),
            報價單編號: business.eyeQuotationOrderCode || '-',
        };
        return data;
    }

    bulkRender(params: { businesses: Business[]; salesTeams: SalesTeam[] }): any[] {
        const { businesses, salesTeams } = params;

        const dictToSalesTeam = this.buildSalesTeamDict({ salesTeams });
        return businesses.map((business) => this.render({ business, dictToSalesTeam }));
    }

    private buildSalesTeamDict(params: { salesTeams: SalesTeam[] }): Record<string, SalesTeam[]> {
        const { salesTeams } = params;

        const dict: Record<string, SalesTeam[]> = {};
        const grouped = _.groupBy(salesTeams, 'id');
        for (const salesTeam of salesTeams) {
            const list: SalesTeam[] = [];

            let current = salesTeam;
            while (true) {
                list.unshift(current);
                if (!current.parent) break;
                const next = grouped[current.parent.id];
                if (!next) break;
                current = next[0];
            }
            dict[salesTeam.id] = list;
        }
        return dict;
    }
}
