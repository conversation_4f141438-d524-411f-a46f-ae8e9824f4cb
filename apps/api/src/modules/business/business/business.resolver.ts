import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import type { BusinessLosingReason } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessLosingReason.model';
import type { BusinessMemo } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessMemo.model';
import type { BusinessOpportunity } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessOpportunity.model';
import type { BusinessProperty } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProperty.model';
import type { BusinessSalesMethod } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessSalesMethod.model';
import type { BusinessStatus } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessStatus.model';
import type { BusinessType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessType.model';
import type { BusinessesToBudgetProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessesToBudgetProduct.model';
import type { BusinessesToDealProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessesToDealProduct.model';
import type { BusinessesUser } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessesUser.model';
import type { Competitor } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/competitor.model';
import type { ContactPerson } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPerson.model';
import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import type { SalesTeam } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeam.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import type { Visit } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/visit.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { EnumPermissionCode } from '~/constants';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { TCompetitor } from '~/modules/competitor/competitor.type';
import { TContactPerson } from '~/modules/contactPerson/contactPerson/contactPerson.type';
import { CustomerService } from '~/modules/customer/customer/customer.service';
import { TCustomer } from '~/modules/customer/customer/customer.type';
import { SalesTeamService } from '~/modules/salesTeam/salesTeam/salesTeam.service';
import { TSalesTeam } from '~/modules/salesTeam/salesTeam/salesTeam.type';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import { TVisit } from '~/modules/visit/visit/visit.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessLosingReasonService } from '../businessLosingReason/businessLosingReason.service';
import { TBusinessLosingReason } from '../businessLosingReason/businessLosingReason.type';
import { TBusinessMemo } from '../businessMemo/businessMemo.type';
import { BusinessOpportunityService } from '../businessOpportunity/businessOpportunity.service';
import { TBusinessOpportunity } from '../businessOpportunity/businessOpportunity.type';
import { FormattedProperty, TBusinessProperty } from '../businessProperty/businessProperty.type';
import { BusinessPropertyTypeService } from '../businessPropertyType/businessPropertyType.service';
import { BusinessSalesMethodService } from '../businessSalesMethod/businessSalesMethod.service';
import { TBusinessSalesMethod } from '../businessSalesMethod/businessSalesMethod.type';
import { BusinessStatusService } from '../businessStatus/businessStatus.service';
import { TBusinessStatus } from '../businessStatus/businessStatus.type';
import { TBusinessesToBudgetProduct } from '../businessToBudgetProduct/businessToBudgetProduct.type';
import { TBusinessesToDealProduct } from '../businessToDealProduct/businessToDealProduct.type';
import { BusinessTypeService } from '../businessType/businessType.service';
import { TBusinessType } from '../businessType/businessType.type';
import { TBusinessesUser } from '../businessUser/businessUser.type';
import { CreateInput, UpdateInput } from './business.input';
import { BusinessService } from './business.service';
import { PaginatedObjects, SearchArgs, TBusiness } from './business.type';
import { SalesTeamUnitService } from '~/modules/salesTeam/salesTeamUnit/salesTeamUnit.service';
import { SalesTeamUnit } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamUnit.model';
import { TSalesTeamUnit } from '~/modules/salesTeam/salesTeamUnit/salesTeamUnit.type';

@Service()
@Resolver(() => TBusiness)
export class BusinessResolver {
    @Inject()
    private service: BusinessService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBusinesses' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination, sorts }: SearchArgs
    ): Promise<{ count: number; businesses: Business[] }> {
        const options = { pagination, sorts, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);

        // Workaround for PaginatedBusinessResolver
        (ctx as any).args = { filters, pagination, sorts };

        return { count: count ?? 0, businesses: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBusiness], { name: 'businesses' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination, sorts }: SearchArgs
    ): Promise<Business[]> {
        const options = { pagination, sorts, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBusiness, { name: 'business' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<Business> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['business.create'])
    @Mutation(() => TBusiness, { name: 'createBusiness' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<Business> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['business.update'])
    @Mutation(() => TBusiness, { name: 'updateBusiness' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<Business> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['business.delete'])
    @Mutation(() => Boolean, { name: 'deleteBusiness' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: Business): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroupId });
    }

    @Inject()
    private businessTypeService: BusinessTypeService;
    @FieldResolver(() => TBusinessType, { description: '商機類型', nullable: true })
    async type(@Root() model: Business): Promise<BusinessType | undefined> {
        return await this.businessTypeService.findOne({ id: model.typeId });
    }

    @Inject()
    private businessOpportunityService: BusinessOpportunityService;
    @FieldResolver(() => TBusinessOpportunity, { description: '贏單機會', nullable: true })
    async winningOpportunity(@Root() model: Business): Promise<BusinessOpportunity | undefined> {
        return await this.businessOpportunityService.findOne({ id: model.winningOpportunityId });
    }
    @FieldResolver(() => TBusinessOpportunity, { description: '購買機會', nullable: true })
    async buyingOpportunity(@Root() model: Business): Promise<BusinessOpportunity | undefined> {
        return await this.businessOpportunityService.findOne({ id: model.buyingOpportunityId });
    }

    @Inject()
    private customerService: CustomerService;
    @FieldResolver(() => TCustomer, { description: '客戶', nullable: true })
    async customer(@Root() model: Business): Promise<Customer | undefined> {
        return await this.customerService.findOne({ id: model.customerId });
    }
    @FieldResolver(() => TCustomer, { description: '經銷商', nullable: true })
    async dealer(@Root() model: Business): Promise<Customer | undefined> {
        return await this.customerService.findOne({ id: model.dealerId });
    }

    @Inject()
    private businessStatusService: BusinessStatusService;
    @FieldResolver(() => TBusinessStatus, { description: '商機狀態', nullable: true })
    async status(@Root() model: Business): Promise<BusinessStatus | undefined> {
        return await this.businessStatusService.findOne({ id: model.statusId });
    }

    @Inject()
    private businessSalesMethodService: BusinessSalesMethodService;
    @FieldResolver(() => TBusinessSalesMethod, { description: '商機銷售方式', nullable: true })
    async salesMethod(@Root() model: Business): Promise<BusinessSalesMethod | undefined> {
        return await this.businessSalesMethodService.findOne({ id: model.salesMethodId });
    }

    @Inject()
    private salesTeamService: SalesTeamService;
    @FieldResolver(() => TSalesTeam, { description: '業務團隊', nullable: true })
    async salesTeam(@Root() model: Business): Promise<SalesTeam | undefined> {
        return await this.salesTeamService.findOne({ id: model.salesTeamId });
    }

    @Inject()
    private salesTeamUnitService: SalesTeamUnitService;
    @FieldResolver(() => TSalesTeamUnit, { description: '业务团队位置', nullable: true })
    async salesTeamUnit(@Root() model: Business): Promise<SalesTeamUnit | undefined> {
        return await this.salesTeamUnitService.findOne({ id: model.salesTeamUnitId });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '主要負責人員', nullable: true })
    async primaryUser(@Root() model: Business): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.primaryUserId });
    }

    @FieldResolver(() => [TBusinessesUser], { description: '負責（支援）業務' })
    async users(@Root() model: Business): Promise<BusinessesUser[]> {
        if (!model.businessesUsers.isInitialized()) {
            await model.businessesUsers.init({ 
                populate: ['user'],
                where: { user: { deleted: false } }, 
            });
        }
        return model.businessesUsers.getItems();
    }

    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: Business): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: Business): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }

    @FieldResolver(() => [TVisit], { description: '拜訪' })
    async visits(@Root() model: Business): Promise<Visit[]> {
        if (!model.visits.isInitialized()) {
            await model.visits.init();
        }
        return model.visits.getItems();
    }

    @FieldResolver(() => [TContactPerson], { description: '主要負責聯絡人' })
    async primaryContactPeople(@Root() model: Business): Promise<ContactPerson[]> {
        if (!model.businessesPrimaryContactPeople.isInitialized()) {
            await model.businessesPrimaryContactPeople.init({ populate: ['contactPerson'] });
        }
        return model.businessesPrimaryContactPeople.getItems().map((v) => v.contactPerson);
    }

    @FieldResolver(() => [TCompetitor], { description: '競爭對手' })
    async competitors(@Root() model: Business): Promise<Competitor[]> {
        if (!model.businessesCompetitors.isInitialized()) {
            await model.businessesCompetitors.init({ populate: ['competitor'] });
        }
        return model.businessesCompetitors.getItems().map((v) => v.competitor);
    }

    @Inject()
    private businessPropertyTypeService: BusinessPropertyTypeService;
    @FieldResolver(() => [FormattedProperty], { description: '調整格式後的屬性' })
    async formattedProperties(
        @Ctx() ctx: Context,
        @Root() model: Business
    ): Promise<FormattedProperty[]> {
        const { rows: types } = await this.businessPropertyTypeService.search({}, { ctx });

        if (!model.businessesProperties.isInitialized()) {
            await model.businessesProperties.init({ populate: ['businessProperty.type'] });
        }
        const items = model.businessesProperties.getItems().map((v) => v.businessProperty);

        const map = new Map<number, BusinessProperty[]>();
        items.forEach((i) => {
            const prev = map.get(i.type.id);
            map.set(i.type.id, [...(prev ?? []), i]);
        });
        return types.map((t) => ({ propertyType: t, properties: map.get(t.id) ?? [] }));
    }

    @FieldResolver(() => [TBusinessProperty], { description: '屬性' })
    async properties(@Root() model: Business): Promise<BusinessProperty[]> {
        if (!model.businessesProperties.isInitialized()) {
            await model.businessesProperties.init({ populate: ['businessProperty.type'] });
        }
        const items = model.businessesProperties.getItems().map((v) => v.businessProperty);
        return items;
    }

    @FieldResolver(() => [TBusinessMemo], { description: '備註' })
    async memos(@Root() model: Business): Promise<BusinessMemo[]> {
        if (!model.businessMemos.isInitialized()) {
            await model.businessMemos.init();
        }
        return model.businessMemos.getItems();
    }

    @FieldResolver(() => [TBusinessesToBudgetProduct], { description: '預算商品' })
    async mapBudgetProducts(@Root() model: Business): Promise<BusinessesToBudgetProduct[]> {
        if (!model.businessesToBudgetProducts.isInitialized()) {
            await model.businessesToBudgetProducts.init({
                populate: ['budgetProduct'],
                where: { budgetProduct: { deleted: false } },
            });
        }
        return model.businessesToBudgetProducts.getItems();
    }

    @FieldResolver(() => [TBusinessesToDealProduct], { description: '成交商品' })
    async mapDealProducts(@Root() model: Business): Promise<BusinessesToDealProduct[]> {
        if (!model.businessesToDealProducts.isInitialized()) {
            await model.businessesToDealProducts.init({
                populate: ['dealProduct'],
                where: { dealProduct: { deleted: false } },
            });
        }
        return model.businessesToDealProducts.getItems();
    }

    @Inject()
    private businessLosingReasonService: BusinessLosingReasonService;
    @FieldResolver(() => [TBusinessLosingReason], { description: '丟單原因' })
    async losingReasons(@Root() model: Business): Promise<BusinessLosingReason[]> {
        const { rows } = await this.businessLosingReasonService.search({ businessId: model.id });
        return rows;
    }
}
