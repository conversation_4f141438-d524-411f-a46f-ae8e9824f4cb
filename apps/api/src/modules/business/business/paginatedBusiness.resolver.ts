import { Ctx, FieldResolver, Float, Info, Resolver } from 'type-graphql';
import { Inject, Service } from 'typedi';

import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessService } from './business.service';
import { SearchOptions } from './business.service.type';
import { PaginatedObjects } from './business.type';

@Service()
@Resolver(() => PaginatedObjects)
export class PaginatedBusinessResolver {
    @Inject()
    private businessService: BusinessService;

    @FieldResolver(() => Float, { description: '客戶預算金額。總計' })
    async budgetAmountSum(@Ctx() ctx: Context, @Info() info): Promise<number> {
        // Workaround from BusinessResolver
        const { args } = ctx as any;
        const { filters, sorts } = args;

        const options: SearchOptions = { sorts, ctx };
        const result = await this.businessService.budgetAmountSum(filters, options);
        return result;
    }
}
