import { QueryOrder } from '@mikro-orm/core';

import { EnumSortDirection } from '../../../../utils/types/base.enum';
import { EnumBusinessSortName } from '../business.enum';
import { BusinessService } from '../business.service';

describe('buildPopulating', () => {
    const buildPopulating = BusinessService.prototype['buildPopulating'];

    test('should build the correct array when no params are passed', () => {
        const result = buildPopulating({ populating: undefined });

        const expected = [
            'salesTeamGroup',
            'type',
            'winningOpportunity',
            'buyingOpportunity',
            'customer',
            'status',
            'salesTeam',
            'primaryUser',
            'createdUser',
            'updatedUser',
            'salesTeamUnit',
        ];
        expect(result).toEqual(expected);
    });

    test('should build the correct array when "Full" is passed', () => {
        const result = buildPopulating({ populating: 'Full' });

        const expected = [
            'salesTeamGroup',
            'type',
            'winningOpportunity',
            'buyingOpportunity',
            'customer',
            'status',
            'salesTeam',
            'primaryUser',
            'createdUser',
            'updatedUser',
            'salesTeamUnit',
            'businessesCompetitors.competitor',
            'businessesToBudgetProducts.budgetProduct',
            'businessesToDealProducts.dealProduct',
            'businessesPrimaryContactPeople.contactPerson',
            'businessesProperties.businessProperty.type',
            'businessesUsers.user',
            'primaryUser.salesTeamsUsers.salesTeam',
            'salesTeamUnit.salesTeam',
            'salesTeamUnit.user',
        ];
        expect(result).toEqual(expected);
    });

    test('should build the correct array when "Default" is passed', () => {
        const result = buildPopulating({ populating: 'Default' });

        const expected = [
            'salesTeamGroup',
            'type',
            'winningOpportunity',
            'buyingOpportunity',
            'customer',
            'status',
            'salesTeam',
            'primaryUser',
            'createdUser',
            'updatedUser',
            'salesTeamUnit',
        ];
        expect(result).toEqual(expected);
    });
});

describe('buildSorting', () => {
    const buildSorting = BusinessService.prototype['buildSorting'];

    test('should build the correct array when no sorting params are given', () => {
        const params = { sorting: undefined };
        const result = buildSorting(params);

        const expected = [{ expectedClosedDate: QueryOrder.ASC }, { id: QueryOrder.DESC }];
        expect(result).toEqual(expected);
    });

    test('should build the correct array when sorting params are given', () => {
        const Name = EnumBusinessSortName;
        const Direction = EnumSortDirection;
        const params = {
            sorting: [
                { name: Name.BudgetProductName, direction: Direction.ASC },
                { name: Name.CustomerName, direction: Direction.DESC },
                { name: Name.ExpectedClosedDate, direction: Direction.ASC },
                { name: Name.StatusBuyingOpportunity, direction: Direction.DESC },
                { name: Name.WinningOpportunityName, direction: Direction.ASC },
                { name: Name.Code, direction: Direction.DESC },
                { name: Name.PrimaryUserCode, direction: Direction.ASC },
                { name: Name.PrimaryUserName, direction: Direction.DESC },
            ],
        };
        const result = buildSorting(params);

        const expected = [
            { businessesToBudgetProducts: { budgetProduct: { name: QueryOrder.ASC } } },
            { customer: { name: QueryOrder.DESC } },
            { expectedClosedDate: QueryOrder.ASC },
            { status: { buyingOpportunity: QueryOrder.DESC } },
            { winningOpportunity: { name: QueryOrder.ASC } },
            { code: QueryOrder.DESC },
            { primaryUser: { code: QueryOrder.ASC } },
            { primaryUser: { name: QueryOrder.DESC } },
            { expectedClosedDate: QueryOrder.ASC },
            { id: QueryOrder.DESC },
        ];
        expect(result).toEqual(expected);
    });
});
