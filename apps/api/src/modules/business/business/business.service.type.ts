import type { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import type { AutoPath } from '@mikro-orm/core/typings';

import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { PaginationInput } from '~/utils/types/base.type';

import type { SortInput } from './business.type';

export interface CountOptions {
    ctx?: Context;
}

export interface SearchOptions {
    ctx?: Context;
    pagination?: PaginationInput;
    sorts?: SortInput[];
    count?: boolean;
    fields?: ['id'];
    populate?: 'Default' | 'Full';
}

export type PopulateType = AutoPath<
    Business,
    | '*'
    | 'businessesCompetitors.*'
    | 'businessesToBudgetProducts.*'
    | 'businessesPrimaryContactPeople.*'
    | 'businessesToDealProducts.*'
    | 'businessesProperties.businessProperty.*'
    | 'businessesUsers.*'
    | 'primaryUser.salesTeamsUsers.*'
    | 'salesTeamUnit.*'
>[];

export interface BuildPopulatingParams {
    populating?: 'Default' | 'Full';
}

export interface BuildSortingParams {
    sorting?: SortInput[];
}
