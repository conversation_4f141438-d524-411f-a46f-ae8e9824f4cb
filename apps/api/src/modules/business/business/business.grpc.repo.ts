import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import type {
    BusinessResult,
    CreateBusinessParams,
    DeleteBusinessParams,
    UpdateBusinessParams,
} from '@packages/erp-protobuf/generated/business/business_pb';

@Service()
export class BusinessGrpcRepo {
    serviceNodes: string[] = ['business', 'Business'];

    async create(params: CreateBusinessParams): Promise<BusinessResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BusinessResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Business.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateBusinessParams): Promise<BusinessResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BusinessResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Business.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteBusinessParams): Promise<BusinessResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BusinessResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Business.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
