import { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import { EnumVisitStatus } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/visit.model';
import { FilterQuery, QueryOrder, QueryOrderMap } from '@mikro-orm/core';
import BigNumber from 'bignumber.js';
import _ from 'lodash';
import { Inject, Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import {
    BudgetProduct,
    CreateBusinessParams,
    DealProduct,
    DeleteBusinessParams,
    UpdateBusinessParams,
    User,
} from '@packages/erp-protobuf/generated/business/business_pb';
import { DateOnlyHandler } from '@packages/utils/date';

import { VisitService } from '~/modules/visit/visit/visit.service';
import { SearchOptions as VisitSearchOptions } from '~/modules/visit/visit/visit.service.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';

import {
    EnumBusinessOpportunityCode,
    EnumBusinessTransactionOpportunity,
} from '../businessOpportunity/businessOpportunity.enum';
import { EnumBusinessStatusBuyingOpportunity } from '../businessStatus/businessStatus.enum';
import { EnumBusinessSortName } from './business.enum';
import { BusinessGrpcRepo } from './business.grpc.repo';
import { CreateInput, UpdateInput } from './business.input';
import {
    BuildPopulatingParams,
    BuildSortingParams,
    CountOptions,
    PopulateType,
    SearchOptions,
} from './business.service.type';
import { FilterInput } from './business.type';

@Service()
export class BusinessService extends BaseService<Business> {
    protected entity = Business;
    private gRPCrepo = new BusinessGrpcRepo();

    @Inject()
    private visitService: VisitService;

    async count(params?: FilterInput, options?: CountOptions): Promise<number> {
        const em = this.em.fork();

        const where: FilterQuery<Business>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            {
                salesTeamGroup: options?.ctx?.currentSalesTeamGroup,
                salesTeamGroups: options?.ctx?.visibleSalesTeamGroups,
            }
        ).build();
        where.push({ deleted: false });

        // Access control
        this.appendFilterByAccessControl(where, params, options);
        // Filter input
        this.appendFilterByParams(where, params);

        const repo = em.getRepository(this.entity);
        const count = await repo.count({ $and: where });

        return count;
    }

    async search(
        params?: FilterInput,
        options?: SearchOptions
    ): Promise<{ count: number | null; rows: Business[] }> {
        const em = this.em.fork();

        const where: FilterQuery<Business>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            {
                salesTeamGroup: options?.ctx?.currentSalesTeamGroup,
                salesTeamGroups: options?.ctx?.visibleSalesTeamGroups,
            }
        ).build();
        where.push({ deleted: false });

        // Access control
        this.appendFilterByAccessControl(where, params, options);
        // Filter input
        await this.appendFilterByParams(where, params, options);

        const populating = this.buildPopulating({ populating: options?.populate });
        const sorting = this.buildSorting({ sorting: options?.sorts });

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                fields: options?.fields ?? undefined,
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: options?.fields ? undefined : populating,
                orderBy: sorting,
            }
        );

        return { rows, count };
    }

    private appendFilterByAccessControl(
        where: FilterQuery<Business>[],
        params?: FilterInput,
        options?: { ctx?: Context }
    ) {
        const accesses: FilterQuery<Business> = [];

        // For field/mutation resolvers
        if (params?.id) {
            // field/mutation resolvers
            accesses.push({ id: params.id });
        }
        if (params?.ids) {
            // Data loader
            accesses.push({ id: { $in: params.ids } });
        }

        // For permissions
        const currentUser = options?.ctx?.currentUser;
        if (currentUser?.id) {
            accesses.push({
                salesTeamUnit: {
                    user: { id: currentUser.id },
                },
            });
        }
        const allowUserIds = options?.ctx?.currentPermission?.allowUserIds || [];
        if (allowUserIds.length) {
            accesses.push({
                salesTeamUnit: {
                    user: { id: { $in: allowUserIds } },
                },
            });
        }
        const allowSalesTeamIds = options?.ctx?.currentPermission?.allowSalesTeamIds || [];
        if (allowSalesTeamIds) {
            accesses.push({
                salesTeamUnit: {
                    salesTeam: { id: { $in: allowSalesTeamIds } },
                },
            });
        }

        // For use cases
        if (currentUser?.id) {
            // Workaround: 舊版 Web/Mobile apps 在「負責業務位置」設定為「選填」，需要先把它顯示給使用者。
            accesses.push({
                salesTeamUnit: { id: null },
                createdUser: { id: currentUser?.id },
            });
        }

        where.push({ $or: accesses });
    }

    private async appendFilterByParams(
        where: FilterQuery<Business>[],
        params?: FilterInput,
        options?: SearchOptions
    ): Promise<void> {
        if (params?.regionId) {
            where.push({ salesTeamGroup: { region: { id: params.regionId } } });
        }
        if (params?.salesTeamGroupCodes) {
            where.push({ salesTeamGroup: { code: { $in: params.salesTeamGroupCodes } } });
        }
        if (params?.keyword) {
            // 透過不同欄位搜索商機
            where.push({
                $or: [
                    { customer: { name: { $ilike: `%${params.keyword}%` } } },
                    { code: { $ilike: `%${params.keyword}%` } },
                ],
            });
        }
        if (params?.customerIds) {
            where.push({ customer: { id: { $in: params.customerIds } } });
        }
        if (params?.customerCode) {
            where.push({ customer: { code: { $ilike: `%${params.customerCode}%` } } });
        }
        if (params?.customerName) {
            where.push({ customer: { name: { $ilike: `%${params.customerName}%` } } });
        }
        if (params?.customerBusinessCode) {
            where.push({
                customer: { businessCode: { $ilike: `%${params.customerBusinessCode}%` } },
            });
        }
        if (params?.customerMedicalCode) {
            where.push({
                customer: { medicalCode: { $ilike: `%${params.customerMedicalCode}%` } },
            });
        }
        if (params?.customerCategoryId) {
            where.push({ customer: { categoryId: params.customerCategoryId } });
        }
        if (params?.winningOpportunityIds) {
            where.push({ winningOpportunity: { id: { $in: params.winningOpportunityIds } } });
        }
        if (params?.buyingOpportunityIds) {
            where.push({ buyingOpportunity: { id: { $in: params.buyingOpportunityIds } } });
        }
        if (params?.transactionOpportunity) {
            const EnumCode = EnumBusinessOpportunityCode;
            const EnumBuyingOpportunity = EnumBusinessStatusBuyingOpportunity;
            switch (params.transactionOpportunity) {
                case EnumBusinessTransactionOpportunity.High:
                    where.push({
                        $and: [
                            { winningOpportunity: { code: EnumCode['99%'] } },
                            { status: { buyingOpportunity: EnumBuyingOpportunity['99%'] } },
                        ],
                    });
                    break;
                case EnumBusinessTransactionOpportunity.MediumHigh:
                    where.push({
                        $or: [
                            {
                                $and: [
                                    { winningOpportunity: { code: EnumCode['75%'] } },
                                    { status: { buyingOpportunity: EnumBuyingOpportunity['99%'] } },
                                ],
                            },
                            {
                                $and: [
                                    { winningOpportunity: { code: EnumCode['99%'] } },
                                    { status: { buyingOpportunity: EnumBuyingOpportunity['75%'] } },
                                ],
                            },
                            {
                                $and: [
                                    { winningOpportunity: { code: EnumCode['50%'] } },
                                    { status: { buyingOpportunity: EnumBuyingOpportunity['99%'] } },
                                ]
                            }
                        ],
                    });
                    break;
                case EnumBusinessTransactionOpportunity.MediumHigh_99_75:
                    where.push({
                        $and: [
                            { winningOpportunity: { code: EnumCode['75%'] } },
                            { status: { buyingOpportunity: EnumBuyingOpportunity['99%'] } },
                        ],
                    });
                    break;
                case EnumBusinessTransactionOpportunity.MediumHigh_75_99:
                    where.push({
                        $and: [
                            { winningOpportunity: { code: EnumCode['99%'] } },
                            { status: { buyingOpportunity: EnumBuyingOpportunity['75%'] } },
                        ],
                    });
                    break;
                case EnumBusinessTransactionOpportunity.MediumHigh_99_50:
                    where.push({
                        $and: [
                            { winningOpportunity: { code: EnumCode['50%'] } },
                            { status: { buyingOpportunity: EnumBuyingOpportunity['99%'] } },
                        ],
                    })
                    break;
                case EnumBusinessTransactionOpportunity.Medium:
                    where.push({
                        $or: [
                            {
                                $and: [
                                    { winningOpportunity: { code: EnumCode['75%'] } },
                                    { status: { buyingOpportunity: EnumBuyingOpportunity['75%'] } },
                                ],
                            },
                            {
                                $and: [
                                    { winningOpportunity: { code: EnumCode['25%'] } },
                                    { status: { buyingOpportunity: EnumBuyingOpportunity['99%'] } },
                                ],
                            }
                        ]
                    });
                    break;

                case EnumBusinessTransactionOpportunity.Medium_99_25:
                    where.push({
                        $and: [
                            { winningOpportunity: { code: EnumCode['25%'] } },
                            { status: { buyingOpportunity: EnumBuyingOpportunity['99%'] } },
                        ],
                    })
                case EnumBusinessTransactionOpportunity.Others:
                    where.push({
                        $or: [
                            {
                                winningOpportunity: {
                                    code: { $nin: [EnumCode['99%'], EnumCode['75%']] },
                                },
                            },
                            {
                                status: {
                                    buyingOpportunity: {
                                        $nin: [
                                            EnumBuyingOpportunity['99%'],
                                            EnumBuyingOpportunity['75%'],
                                        ],
                                    },
                                },
                            },
                        ],
                    });
                    break;
            }
        }
        if (params?.statusIds) {
            where.push({ status: { id: { $in: params.statusIds } } });
        }
        if (params?.statusTypes) {
            where.push({ status: { type: { $in: params.statusTypes } } });
        }
        if (params?.closedDate1) {
            const date = new DateOnlyHandler({ date: params.closedDate1 }).toString();
            where.push({ closedDate: { $gte: date } });
        }
        if (params?.closedDate2) {
            const date = new DateOnlyHandler({ date: params.closedDate2 }).toString();
            where.push({ closedDate: { $lte: date } });
        }
        if (params?.expectedClosedDate1) {
            const date = new DateOnlyHandler({ date: params.expectedClosedDate1 }).toString();
            where.push({ expectedClosedDate: { $gte: date } });
        }
        if (params?.expectedClosedDate2) {
            const date = new DateOnlyHandler({ date: params.expectedClosedDate2 }).toString();
            where.push({ expectedClosedDate: { $lte: date } });
        }
        if (params?.primaryContactPersonIds) {
            where.push({
                businessesPrimaryContactPeople: {
                    contactPerson: { id: { $in: params.primaryContactPersonIds } },
                },
            });
        }
        if (params?.salesTeamUnitId) {
            where.push({ salesTeamUnit: { id: params.salesTeamUnitId } });
        }
        if (params?.salesTeamUnitIds) {
            where.push({ salesTeamUnit: { id: { $in: params.salesTeamUnitIds } } });
        }
        if (params?.primaryUserId) {
            where.push({ salesTeamUnit: { user: { id: params.primaryUserId } } });
        }
        if (params?.primaryUserIds) {
            where.push({ salesTeamUnit: { user: { id: { $in: params.primaryUserIds } } } });
        }
        if (params?.userId) {
            where.push({ businessesUsers: { user: { id: params.userId } } });
        }
        if (params?.userIds) {
            where.push({ businessesUsers: { user: { id: { $in: params.userIds } } } });
        }
        if (params?.competitorId) {
            where.push({ businessesCompetitors: { competitor: { id: params?.competitorId } } });
        }
        if (params?.orderCodes) {
            where.push({ orderCode: { $in: params.orderCodes } });
        }
        if (params?.budgetProductIds) {
            where.push({
                businessesToBudgetProducts: {
                    budgetProduct: { id: { $in: params.budgetProductIds } },
                },
            });
        }
        if (params?.dealProductIds) {
            where.push({
                businessesToDealProducts: {
                    dealProduct: { id: { $in: params.dealProductIds } },
                },
            });
        }
        if (params?.propertyTypeIds) {
            where.push({
                businessesProperties: {
                    businessProperty: {
                        type: { id: { $in: params.propertyTypeIds } },
                    },
                },
            });
        }
        if (params?.propertyIds) {
            where.push({
                businessesProperties: {
                    businessProperty: { id: { $in: params.propertyIds } },
                },
            });
        }
        if (params?.createdDate1) {
            const date = new DateOnlyHandler({ date: params.createdDate1 }).toString();
            where.push({ createdAt: { $gte: `${date}T00:00:00` } });
        }
        if (params?.createdDate2) {
            const date = new DateOnlyHandler({ date: params.createdDate2 }).toString();
            where.push({ createdAt: { $lte: `${date}T23:59:59` } });
        }
        if (params?.updatedDate1) {
            const date = new DateOnlyHandler({ date: params.updatedDate1 }).toString();
            where.push({ updatedAt: { $gte: `${date}T00:00:00` } });
        }
        if (params?.updatedDate2) {
            const date = new DateOnlyHandler({ date: params.updatedDate2 }).toString();
            where.push({ updatedAt: { $lte: `${date}T23:59:59` } });
        }
        if (!_.isUndefined(params?.hasVisitedVisit)) {
            if (params?.hasVisitedVisit) {
                where.push({ visits: { status: EnumVisitStatus.Visited } });
            } else {
                const idOptions: SearchOptions = {
                    ...options,
                    fields: ['id'],
                    count: false,
                    pagination: undefined,
                };
                const idFilters: FilterInput = { ...params, hasVisitedVisit: true };
                const businesses = await this.search(idFilters, idOptions);

                const ids = businesses.rows.map((r) => r.id);
                where.push({ id: { $nin: ids } });
            }
        }
        const hasVisitParams = Object.entries(params?.visit ?? {}).some(([, v]) => {
            return !_.isUndefined(v);
        });
        if (hasVisitParams) {
            const visitSearchOptions: VisitSearchOptions = {
                ...options,
                fields: ['id'],
                count: false,
                pagination: undefined,
            };
            const { rows } = await this.visitService.search(params?.visit, visitSearchOptions);

            const ids = rows.map((r) => r.id);
            where.push({ visits: { id: { $in: ids } } });
        }
    }

    private buildPopulating(params: BuildPopulatingParams): PopulateType {
        let populating: PopulateType = [
            'salesTeamGroup',
            'type',
            'winningOpportunity',
            'buyingOpportunity',
            'customer',
            'status',
            'salesTeam',
            'primaryUser',
            'createdUser',
            'updatedUser',
            'salesTeamUnit',
        ];
        if (params?.populating === 'Full') {
            const inserted: PopulateType = [
                'businessesCompetitors.competitor',
                'businessesToBudgetProducts.budgetProduct',
                'businessesToDealProducts.dealProduct',
                'businessesPrimaryContactPeople.contactPerson',
                'businessesProperties.businessProperty.type',
                'businessesUsers.user',
                'primaryUser.salesTeamsUsers.salesTeam',
                'salesTeamUnit.salesTeam',
                'salesTeamUnit.user',
            ];
            populating.push(...inserted);
        }
        return populating;
    }

    private buildSorting(params: BuildSortingParams): QueryOrderMap<Business>[] {
        const orderBy: QueryOrderMap<Business>[] = [
            { expectedClosedDate: QueryOrder.ASC },
            { id: QueryOrder.DESC },
        ];
        for (const val of (params.sorting ?? []).reverse()) {
            // 排序方向
            let orderKey: QueryOrder = QueryOrder.ASC;
            if (val.direction === 'DESC') {
                orderKey = QueryOrder.DESC;
            }

            // 排序欄位
            switch (val.name) {
                case EnumBusinessSortName.Code:
                    orderBy.unshift({ code: orderKey });
                    break;
                case EnumBusinessSortName.BudgetProductName:
                    orderBy.unshift({
                        businessesToBudgetProducts: { budgetProduct: { name: orderKey } },
                    });
                    break;
                case EnumBusinessSortName.WinningOpportunityName:
                    orderBy.unshift({ winningOpportunity: { name: orderKey } });
                    break;
                case EnumBusinessSortName.StatusBuyingOpportunity:
                    orderBy.unshift({ status: { buyingOpportunity: orderKey } });
                    break;
                case EnumBusinessSortName.ExpectedClosedDate:
                    orderBy.unshift({ expectedClosedDate: orderKey });
                    break;
                case EnumBusinessSortName.CustomerName:
                    orderBy.unshift({ customer: { name: orderKey } });
                    break;
                case EnumBusinessSortName.PrimaryUserCode:
                    orderBy.unshift({ primaryUser: { code: orderKey } });
                    break;
                case EnumBusinessSortName.PrimaryUserName:
                    orderBy.unshift({ primaryUser: { name: orderKey } });
                    break;
                case EnumBusinessSortName.PrimaryUserSalesTeamCode:
                    orderBy.unshift({
                        primaryUser: { salesTeamsUsers: { salesTeam: { code: orderKey } } },
                    });
                    break;
                case EnumBusinessSortName.PrimaryUserSalesTeamName:
                    orderBy.unshift({
                        primaryUser: { salesTeamsUsers: { salesTeam: { name: orderKey } } },
                    });
                    break;
            }
        }
        return orderBy;
    }

    async createByGrpc(params: CreateInput, options?: { ctx?: Context }): Promise<Business> {
        const inputs = new CreateBusinessParams({
            salesTeamGroupId: options?.ctx?.currentSalesTeamGroup?.id,
            regionId: options?.ctx?.currentSalesTeamGroup?.region?.id,
            typeId: params.typeId,
            title: params.title,
            content: params.content,
            orderCode: params.orderCode,
            eyeQuotationOrderCode: params.eyeQuotationOrderCode,
            budgetAmount: _.isNil(params.budgetAmount)
                ? undefined
                : new BigNumber(params.budgetAmount).toString(),
            budgetProducts: params.budgetProducts,
            buyingOpportunityId: params.buyingOpportunityId,
            closedDate: handler.toTimestamp({ input: params.closedDate }),
            competitorIds: params.competitorIds,
            customerId: params.customerId,
            dealerId: params.dealerId,
            salesMethodId: params.salesMethodId,
            dealAmount: _.isNil(params.dealAmount)
                ? undefined
                : new BigNumber(params.dealAmount).toString(),
            dealProducts: params.dealProducts,
            expectedClosedDate: handler.toTimestamp({ input: params.expectedClosedDate }),
            losingImprovementPlan: params.losingImprovementPlan,
            losingReason: params.losingReason,
            losingReasonIds: params.losingReasonIds,
            primaryContactPersonIds: params.primaryContactPeopleIds,
            salesTeamUnitId: params.salesTeamUnitId,
            primaryUserId: params.primaryUserId,
            customerMemo: params.customerMemo,
            propertyIds: params.propertyIds,
            saleAmount: _.isNil(params.saleAmount)
                ? undefined
                : new BigNumber(params.saleAmount).toString(),
            salesTeamId: params.salesTeamId,
            statusId: params.statusId,
            winningOpportunityId: params.winningOpportunityId,
            createdUserId: options?.ctx?.currentUser?.id,
        });
        if (params.users) {
            inputs.users = params.users.map((v) => {
                return new User({
                    salesTeamId: v.salesTeamId,
                    userId: v.userId,
                });
            });
        }
        const row = await this.gRPCrepo.create(inputs);

        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(params: UpdateInput, options?: { ctx?: Context }): Promise<Business> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateBusinessParams({
            id: row.id,
            typeId: handler.toNumber({ input: params.typeId, default: row.type?.id }),
            title: handler.toString({ input: params.title, default: row.title }),
            content: handler.toString({ input: params.content, default: row.content }),
            orderCode: handler.toString({ input: params.orderCode, default: row.orderCode }),
            eyeQuotationOrderCode: handler.toString({
                input: params.eyeQuotationOrderCode,
                default: row.eyeQuotationOrderCode,
            }),
            budgetAmount: handler.toDecimal({
                input: params.budgetAmount,
                default: row.budgetAmount,
            }),
            dealAmount: handler.toDecimal({ input: params.dealAmount, default: row.dealAmount }),
            saleAmount: handler.toDecimal({ input: params.saleAmount, default: row.saleAmount }),
            expectedClosedDate: handler.toTimestamp({
                input: params.expectedClosedDate,
                default: row.expectedClosedDate,
            }),
            closedDate: handler.toTimestamp({ input: params.closedDate, default: row.closedDate }),
            winningOpportunityId: handler.toNumber({
                input: params.winningOpportunityId,
                default: row.winningOpportunity?.id,
            }),
            buyingOpportunityId: handler.toNumber({
                input: params.buyingOpportunityId,
                default: row.buyingOpportunity?.id,
            }),
            losingReason: handler.toString({
                input: params.losingReason,
                default: row.losingReason,
            }),
            losingImprovementPlan: handler.toString({
                input: params.losingImprovementPlan,
                default: row.losingImprovementPlan,
            }),
            customerId: handler.toNumber({ input: params.customerId, default: row.customer?.id }),
            dealerId: handler.toNumber({ input: params.dealerId, default: row.dealer?.id }),
            salesMethodId: handler.toNumber({
                input: params.salesMethodId,
                default: row.salesMethod?.id,
            }),
            salesTeamUnitId: handler.toNumber({
                input: params.salesTeamUnitId,
                default: row.salesTeamUnit?.id,
            }),
            primaryUserId: handler.toNumber({
                input: params.primaryUserId,
                default: row.primaryUser?.id,
            }),
            customerMemo: handler.toString({
                input: params.customerMemo,
                default: row.customerMemo,
            }),
            salesTeamId: handler.toNumber({
                input: params.salesTeamId,
                default: row.salesTeam?.id,
            }),
            statusId: handler.toNumber({ input: params.statusId, default: row.status?.id }),

            updatedUserId: options?.ctx?.currentUser?.id,
        });

        if (params.dealProducts) {
            inputs.dealProducts = params.dealProducts.map((v) => {
                return new DealProduct({ dealProductId: v.dealProductId, qty: v.qty });
            });
        } else {
            if (!row.businessesToDealProducts.isInitialized()) {
                await row.businessesToDealProducts.init();
            }
            const items = row.businessesToDealProducts.getItems();
            inputs.dealProducts = items.map((v) => {
                return new DealProduct({ dealProductId: v.dealProductId, qty: v.qty });
            });
        }

        if (params.budgetProducts) {
            inputs.budgetProducts = params.budgetProducts.map((v) => {
                return new BudgetProduct({ budgetProductId: v.budgetProductId, qty: v.qty });
            });
        } else {
            if (!row.businessesToBudgetProducts.isInitialized()) {
                await row.businessesToBudgetProducts.init();
            }
            const items = row.businessesToBudgetProducts.getItems();
            inputs.budgetProducts = items.map((v) => {
                return new BudgetProduct({ budgetProductId: v.budgetProductId, qty: v.qty });
            });
        }

        if (params.competitorIds) {
            inputs.competitorIds = params.competitorIds;
        } else {
            if (!row.businessesCompetitors.isInitialized()) {
                await row.businessesCompetitors.init();
            }
            const items = row.businessesCompetitors.getItems();
            inputs.competitorIds = items.map((v) => v.competitorId);
        }

        if (params.primaryContactPeopleIds) {
            inputs.primaryContactPersonIds = params.primaryContactPeopleIds;
        } else {
            if (!row.businessesPrimaryContactPeople.isInitialized()) {
                await row.businessesPrimaryContactPeople.init();
            }
            const items = row.businessesPrimaryContactPeople.getItems();
            inputs.primaryContactPersonIds = items.map((v) => v.contactPersonId);
        }

        if (params.losingReasonIds) {
            inputs.losingReasonIds = params.losingReasonIds;
        } else {
            if (!row.businessesLosingReasons.isInitialized()) {
                await row.businessesLosingReasons.init();
            }
            const items = row.businessesLosingReasons.getItems();
            inputs.losingReasonIds = items.map((v) => v.businessLosingReasonId);
        }

        if (params.propertyIds) {
            inputs.propertyIds = params.propertyIds;
        } else {
            if (!row.businessesProperties.isInitialized()) {
                await row.businessesProperties.init();
            }
            const items = row.businessesProperties.getItems();
            inputs.propertyIds = items.map((v) => v.businessPropertyId);
        }

        if (params.users) {
            inputs.users = params.users.map((v) => {
                return new User({
                    salesTeamId: v.salesTeamId,
                    userId: v.userId,
                });
            });
        } else {
            if (!row.businessesUsers.isInitialized()) {
                await row.businessesUsers.init();
            }
            const items = row.businessesUsers.getItems();
            inputs.users = items.map((v) => {
                return new User({
                    salesTeamId: v.salesTeamId,
                    userId: v.userId,
                });
            });
        }

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteBusinessParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }

    /**「預估出貨金額」統計 http://asking.clinico.com.tw/issues/75755 */
    async budgetAmountSum(params?: FilterInput, options?: SearchOptions): Promise<number> {
        const { rows } = await this.search(params, { ...options, fields: ['id'] });

        const em = this.em.fork();
        const sql = `
        SELECT COALESCE(SUM(b.budget_amount), 0) AS total_budget_amount
        FROM sales_rep_workstation.businesses b
        WHERE b.id IN (${rows.length ? rows.map((r) => `'${r.id}'`).join(',') : `'0'`})
        `;
        const result = await em.execute(sql);

        return result[0].total_budget_amount ?? 0;
    }
}
