import { EnumBusinessStatusType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessStatus.model';
import { FastifySchema } from 'fastify';

import { EnumBusinessTransactionOpportunity } from '../businessOpportunity/businessOpportunity.enum';

// [workaround] generate schema from ChatGPT
export const downloadSchema: FastifySchema = {
    summary: '下載商機清單',
    tags: ['商機', '下載'],
    body: {
        type: 'object',
        properties: {
            regionId: {
                type: 'number',
                nullable: true,
                description: '區域',
            },
            keyword: {
                type: 'string',
                nullable: true,
                description: '關鍵字（同時查詢「編號、客戶名稱」欄位）',
            },
            salesTeamGroupId: {
                type: 'number',
                nullable: true,
                description: '業務團隊組織',
            },
            customerId: {
                type: 'number',
                nullable: true,
                description: '客戶',
            },
            orderCode: {
                type: 'string',
                nullable: true,
                description: '訂單編號',
            },
            customerName: {
                type: 'string',
                nullable: true,
                description: '客戶名稱',
            },
            customerCode: {
                type: 'string',
                nullable: true,
                description: '客戶編號',
            },
            customerBusinessCode: {
                type: 'string',
                nullable: true,
                description: '客戶營業統一編號',
            },
            customerMedicalCode: {
                type: 'string',
                nullable: true,
                description: '客戶醫事機構代碼',
            },
            code: {
                type: 'string',
                nullable: true,
                description: '編號',
            },
            winningOpportunityIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '贏單機會（複選）',
            },
            buyingOpportunityIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '購買機會（複選）',
            },
            transactionOpportunity: {
                type: 'string',
                nullable: true,
                enum: Object.values(EnumBusinessTransactionOpportunity),
                description: '成交機會',
            },
            statusIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '狀態（複選）',
            },
            statusTypes: {
                type: 'array',
                items: { type: 'string', enum: Object.values(EnumBusinessStatusType) },
                nullable: true,
                description: '狀態類型（複選）',
            },
            expectedClosedDate1: {
                type: 'string',
                format: 'date',
                nullable: true,
                description: '預計結案日期（起）',
            },
            expectedClosedDate2: {
                type: 'string',
                format: 'date',
                nullable: true,
                description: '預計結案日期（迄）',
            },
            closedDate1: {
                type: 'string',
                format: 'date',
                nullable: true,
                description: '實際結案日期（起）',
            },
            closedDate2: {
                type: 'string',
                format: 'date',
                nullable: true,
                description: '實際結案日期（迄）',
            },
            primaryContactPersonIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '主要負責聯絡人（複選）',
            },
            salesTeamUnitId: {
                type: 'number',
                nullable: true,
                description: '负责业务位置',
            },
            salesTeamUnitIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '负责业务位置（複選）',
            },
            primaryUserId: {
                type: 'number',
                nullable: true,
                description: '主要負責業務',
            },
            primaryUserIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '主要負責業務（複選）',
            },
            userId: {
                type: 'number',
                nullable: true,
                description: '負責（支援）業務',
            },
            userIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '負責（支援）業務（複選）',
            },
            competitorId: {
                type: 'number',
                nullable: true,
                description: '競爭對手',
            },
            budgetProductIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '預算商品（複選）',
            },
            dealProductIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '成交商品（複選）',
            },
            propertyTypeIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '屬性類型（複選）',
            },
            propertyIds: {
                type: 'array',
                items: { type: 'number' },
                nullable: true,
                description: '屬性（複選）',
            },
            updatedDate1: {
                type: 'string',
                format: 'date',
                nullable: true,
                description: '更新日期（起）',
            },
            updatedDate2: {
                type: 'string',
                format: 'date',
                nullable: true,
                description: '更新日期（迄）',
            },
        },
    },
};
