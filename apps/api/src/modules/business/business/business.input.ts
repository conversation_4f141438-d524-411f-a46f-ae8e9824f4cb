import { Field, Float, ID, InputType } from 'type-graphql';

import { CommonInput as CommonBudgetProductInput } from '../businessToBudgetProduct/businessToBudgetProduct.input';
import { CommonInput as CommonDealProductInput } from '../businessToDealProduct/businessToDealProduct.input';

@InputType('BusinessInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field(() => ID, { nullable: true })
    typeId?: number;

    @Field({ nullable: true })
    title?: string;

    @Field({ nullable: true })
    content?: string;

    @Field({ nullable: true })
    orderCode?: string;

    @Field({ nullable: true ,description: '报价单编号'})
    eyeQuotationOrderCode?: string;

    @Field(() => [CommonBudgetProductInput], { nullable: true })
    budgetProducts?: CommonBudgetProductInput[];

    @Field(() => [CommonDealProductInput], { nullable: true })
    dealProducts?: CommonDealProductInput[];

    @Field(() => Float, { nullable: true })
    budgetAmount?: string;

    @Field(() => Float, { nullable: true })
    saleAmount?: string;

    @Field(() => Float, { nullable: true })
    dealAmount?: string;

    @Field({ nullable: true })
    expectedClosedDate?: Date;

    @Field({ nullable: true })
    closedDate?: Date;

    @Field(() => ID, { nullable: true })
    winningOpportunityId?: number;

    @Field(() => ID, { nullable: true })
    buyingOpportunityId?: number;

    @Field({ nullable: true, deprecationReason: '改用多選處理' })
    losingReason?: string;

    @Field(() => [ID], { nullable: true, description: '丟單原因' })
    losingReasonIds?: number[];

    @Field({ nullable: true })
    losingImprovementPlan?: string;

    @Field(() => ID, { nullable: true })
    statusId?: number;

    @Field(() => ID, { nullable: true, description: '客戶' })
    customerId?: number;

    @Field(() => ID, { nullable: true, description: '經銷商' })
    dealerId?: number;

    @Field(() => ID, { nullable: true, description: '銷售方式' })
    salesMethodId?: number;

    @Field(() => ID, { nullable: true })
    salesTeamId?: number;

    @Field((type) => ID, { nullable: true, description: '业务团队位置' })
    salesTeamUnitId?: number;

    @Field(() => ID, { nullable: true, description: '主要負責業務' })
    primaryUserId?: number;

    @Field({ nullable: true, description: '客户备注' })
    customerMemo?: string;

    @Field(() => [UserInput], { nullable: true, description: '負責（支援）業務' })
    users?: UserInput[];

    @Field(() => [ID], { nullable: true })
    competitorIds?: number[];

    @Field(() => [ID], { nullable: true })
    primaryContactPeopleIds?: number[];

    @Field(() => [ID], { nullable: true })
    propertyIds?: number[];

    deleted?: boolean;
}

@InputType('BusinessCreateInput')
export class CreateInput extends CommonInput {}

@InputType('BusinessUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}

@InputType('BusinessUserInput')
export class UserInput {
    @Field(() => ID, { description: '負責（支援）業務' })
    userId: number;

    @Field(() => ID, { nullable: true, description: '業務團隊' })
    salesTeamId?: number;
}
