import type { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import type { EnumBusinessStatusType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessStatus.model';
import type { EnumSalesTeamGroupCode } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { IBusiness } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/business.model';
import { EnumBusinessStatusType as IEnumBusinessStatusType } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/businessStatus.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { FilterInput as VisitFilterInput } from '~/modules/visit/visit/visit.type';
import { EnumSortDirection } from '~/utils/types/base.enum';
import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

import { EnumBusinessTransactionOpportunity } from '../businessOpportunity/businessOpportunity.enum';
import { EnumBusinessSortName } from './business.enum';

@ObjectType('Business', { implements: IBusiness })
export class TBusiness extends IBusiness {}

@ObjectType('PaginatedBusinesses')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBusiness], { nullable: true })
    businesses: Business[];
}

@InputType('BusinessFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true, description: '區域' })
    regionId?: number;

    @Field({
        nullable: true,
        description: '關鍵字（同時查詢「編號、客戶名稱」欄位）',
    })
    keyword?: string;

    @Field(() => ID, { nullable: true, description: '業務團隊組織' })
    salesTeamGroupId?: number;

    @Field(() => ID, { nullable: true, description: '客戶' })
    customerId?: number;

    @Field(() => [ID], { nullable: true, description: '客戶（複選）' })
    customerIds?: number[];

    @Field({ nullable: true, description: '訂單編號' })
    orderCode?: string;

    @Field({ nullable: true, description: '客戶名稱' })
    customerName?: string;

    @Field({ nullable: true, description: '客戶編號' })
    customerCode?: string;

    @Field({ nullable: true, description: '客戶營業統一編號' })
    customerBusinessCode?: string;

    @Field({ nullable: true, description: '客戶醫事機構代碼' })
    customerMedicalCode?: string;

    @Field(() => ID, { nullable: true, description: '客戶分類' })
    customerCategoryId?: number;

    @Field({ nullable: true, description: '編號' })
    code?: string;

    @Field(() => [ID], { nullable: true, description: '贏單機會（複選）' })
    winningOpportunityIds?: number[];

    @Field(() => [ID], { nullable: true, description: '購買機會（複選）' })
    buyingOpportunityIds?: number[];

    @Field(() => EnumBusinessTransactionOpportunity, { nullable: true, description: '成交機會' })
    transactionOpportunity?: EnumBusinessTransactionOpportunity;

    @Field(() => [ID], { nullable: true, description: '狀態（複選）' })
    statusIds?: number[];

    @Field(() => [IEnumBusinessStatusType], { nullable: true, description: '狀態類型（複選）' })
    statusTypes?: EnumBusinessStatusType[];

    @Field(() => ID, { nullable: true, description: '銷售方式' })
    salesMethodId?: number;

    @Field(() => String, { nullable: true, description: '預計結案日期（起）' })
    expectedClosedDate1?: string;

    @Field(() => String, { nullable: true, description: '預計結案日期（迄）' })
    expectedClosedDate2?: string;

    @Field(() => String, { nullable: true, description: '實際結案日期（起）' })
    closedDate1?: string;

    @Field(() => String, { nullable: true, description: '實際結案日期（迄）' })
    closedDate2?: string;

    @Field(() => [ID], { nullable: true, description: '主要負責聯絡人（複選）' })
    primaryContactPersonIds?: number[];

    @Field(() => ID, { nullable: true, description: '负责业务位置' })
    salesTeamUnitId?: number;

    @Field(() => [ID], { nullable: true, description: '负责业务位置（複選）' })
    salesTeamUnitIds?: number[];

    @Field(() => ID, { nullable: true, description: '负责业务' })
    salesTeamId?: number;

    @Field(() => [ID], { nullable: true, description: '负责业务（複選）' })
    salesTeamIds?: number[];

    @Field(() => ID, { nullable: true, description: '主要負責業務' })
    primaryUserId?: number;

    @Field(() => [ID], { nullable: true, description: '主要負責業務（複選）' })
    primaryUserIds?: number[];

    @Field(() => ID, { nullable: true, description: '負責（支援）業務' })
    userId?: number;

    @Field(() => [ID], { nullable: true, description: '負責（支援）業務（複選）' })
    userIds?: number[];

    @Field(() => ID, { nullable: true, description: '競爭對手' })
    competitorId?: number;

    @Field(() => [ID], { nullable: true, description: '預算商品（複選）' })
    budgetProductIds?: number[];

    @Field(() => [ID], { nullable: true, description: '成交商品（複選）' })
    dealProductIds?: number[];

    @Field(() => [ID], { nullable: true, description: '屬性類型（複選）' })
    propertyTypeIds?: number[];

    @Field(() => [ID], { nullable: true, description: '屬性（複選）' })
    propertyIds?: number[];

    @Field(() => String, { nullable: true, description: '建立日期（起）' })
    createdDate1?: string;

    @Field(() => String, { nullable: true, description: '建立日期（迄）' })
    createdDate2?: string;

    @Field(() => String, { nullable: true, description: '更新日期（起）' })
    updatedDate1?: string;

    @Field(() => String, { nullable: true, description: '更新日期（迄）' })
    updatedDate2?: string;

    @Field(() => Boolean, { nullable: true, description: '是否有「已拜訪」的拜訪資料' })
    hasVisitedVisit?: boolean;

    @Field(() => VisitFilterInput, { nullable: true, description: '拜訪查詢項' })
    visit?: VisitFilterInput;

    /** 業務團隊組織編號（複選） */
    salesTeamGroupCodes?: EnumSalesTeamGroupCode[];

    /** 訂單編號（複選） */
    orderCodes?: string[];
}

@InputType('BusinessSortInput')
export class SortInput {
    @Field(() => EnumBusinessSortName)
    name: EnumBusinessSortName;

    @Field(() => EnumSortDirection)
    direction: EnumSortDirection;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;

    @Field(() => [SortInput], { nullable: true })
    sorts?: SortInput[];
}
