import { SalesTeam } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeam.model';
import { EnumSalesTeamGroupCode } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { RouteOptions } from 'fastify';
import _ from 'lodash';
import { DateTime } from 'luxon';
import mime from 'mime-types';
import Container from 'typedi';

import { EnumPermissionCode } from '~/constants';
import { RestUserAuthInterceptor } from '~/middlewares/auth.middleware';
import { BusinessService } from '~/modules/business/business/business.service';
import { SalesTeamService } from '~/modules/salesTeam/salesTeam/salesTeam.service';

import { downloadSchema } from './business.swagger';
import { BusinessTemplateCnService } from './sheets/business.template-cn.service';
import { BusinessTemplateTwService } from './sheets/business.template-tw.service';

const routes: Record<string, RouteOptions> = {
    download: {
        method: 'POST',
        url: '/download',
        preHandler: [RestUserAuthInterceptor(EnumPermissionCode['business.export'])],
        schema: downloadSchema,
        handler: async (req, reply) => {
            const body = req.body || {};

            const defaultLimit = 10_000;
            const businessService = Container.get(BusinessService);
            const { count, rows } = await businessService.search(body, {
                ctx: { ...req.ctx, ctx: { request: req, reply } },
                pagination: { limit: defaultLimit },
                populate: 'Full',
                count: true,
            });

            // START: Sales teams
            let salesTeamGroupIds: number[] = [];
            rows.forEach((row) => {
                if (row.salesTeamGroupId) {
                    salesTeamGroupIds.push(row.salesTeamGroupId);
                }
            });
            salesTeamGroupIds = _.uniq(salesTeamGroupIds);
            let salesTeams: SalesTeam[] = [];
            if (salesTeamGroupIds.length) {
                const salesTeamService = Container.get(SalesTeamService);
                salesTeams = await salesTeamService
                    .search({ groupIds: salesTeamGroupIds })
                    .then(({ rows }) => rows);
            }
            // END: Sales teams

            // START: Create Excel
            let template: BusinessTemplateTwService | BusinessTemplateCnService;
            switch (req.ctx.currentSalesTeamGroup?.code) {
                case EnumSalesTeamGroupCode.TWN_EYE: {
                    template = Container.get(BusinessTemplateTwService);
                    break;
                }
                case EnumSalesTeamGroupCode.CHN_EYE:
                default: {
                    template = Container.get(BusinessTemplateCnService);
                    break;
                }
            }
            await template.load();
            await template.render({ businesses: rows, salesTeams, count, limit: defaultLimit });
            // END: Create Excel

            const content = await template.template.output();
            const now = DateTime.now();
            const filename = `businesses_${now.toFormat('yyyyMMdd_HHmmss')}.xlsx`;
            const mimeType = mime.lookup(filename);

            reply.header('Content-Type', mimeType);
            reply.header('Content-Disposition', `attachment; filename="${filename}"`);
            reply.send(content);
        },
    },
};

export default Object.values(routes);
