import { registerEnumType } from 'type-graphql';

export enum EnumBusinessSortName {
    Code = 'Code',
    WinningOpportunityName = 'WinningOpportunityName',
    ExpectedClosedDate = 'ExpectedClosedDate',
    StatusBuyingOpportunity = 'StatusBuyingOpportunity',
    CustomerName = 'CustomerName',
    BudgetProductName = 'BudgetProductName',
    PrimaryUserCode = 'PrimaryUserCode',
    PrimaryUserName = 'PrimaryUserName',
    PrimaryUserSalesTeamCode = 'PrimaryUserSalesTeamCode',
    PrimaryUserSalesTeamName = 'PrimaryUserSalesTeamName',
}
registerEnumType(EnumBusinessSortName, {
    name: 'EnumBusinessSortName',
    description: '商機排序名稱',
    valuesConfig: {
        Code: { description: '編號' },
        WinningOpportunityName: { description: '贏單機會_名稱' },
        ExpectedClosedDate: { description: '預計結束日期' },
        StatusBuyingOpportunity: { description: '商機狀態_購買機會' },
        CustomerName: { description: '客戶_名稱' },
        BudgetProductName: { description: '預算商品_名稱' },
        PrimaryUserCode: { description: '主要負責業務_編號' },
        PrimaryUserName: { description: '主要負責業務_姓名' },
        PrimaryUserSalesTeamCode: { description: '主要負責業務_業務團隊_編號' },
        PrimaryUserSalesTeamName: { description: '主要負責業務_業務團隊_名稱' },
    },
});
