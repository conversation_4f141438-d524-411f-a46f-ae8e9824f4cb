import { Competitor } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/competitor.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import {
    CreateCompetitorParams,
    DeleteCompetitorParams,
    UpdateCompetitorParams,
} from '@packages/erp-protobuf/generated/competitor/competitor_pb';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CompetitorGrpcRepo } from './competitor.grpc.repo';
import { CreateInput, UpdateInput } from './competitor.input';
import { FilterInput } from './competitor.type';

@Service()
export class CompetitorService extends BaseService<Competitor> {
    protected entity = Competitor;
    private gRPCrepo = new CompetitorGrpcRepo();

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: Competitor[] }> {
        const em = this.em.fork();

        const where: FilterQuery<Competitor>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        // Access control
        const accesses: FilterQuery<Competitor> = [];
        const permission = options?.ctx?.currentPermission;
        if (permission?.allowSalesTeamGroupIds) {
            const allowSalesTeamGroupIds = permission?.allowSalesTeamGroupIds;
            accesses.push({ salesTeamGroup: { id: { $in: allowSalesTeamGroupIds } } });
        }
        if (permission?.allowRegionIds) {
            const allowRegionIds = permission?.allowRegionIds;
            accesses.push({ salesTeamGroup: { region: { id: { $in: allowRegionIds } } } });
        }
        where.push({ $and: accesses });

        // Filter inputs
        if (params?.keyword) {
            // 透過不同欄位搜索競爭對手
            const keyword = `%${params.keyword}%`;
            where.push({
                $or: [
                    { name: { $ilike: keyword } },
                    { brand: { $ilike: keyword } },
                    { model: { $ilike: keyword } },
                    { agents: { $ilike: keyword } },
                ],
            });
        }
        if (params?.businessId) {
            where.push({ businessesCompetitors: { business: { id: params.businessId } } });
        }
        if (params?.businessProductIds) {
            where.push({
                competitorsBusinessProducts: {
                    businessProduct: { id: { $in: params.businessProductIds } },
                },
            });
        }
        if (params?.businessProductName) {
            where.push({
                competitorsBusinessProducts: {
                    businessProduct: { name: { $ilike: `%${params.businessProductName}%` } },
                },
            });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
                populate: ['salesTeamGroup', 'createdUser', 'updatedUser'],
            }
        );

        return { rows, count };
    }

    async createByGrpc(params: CreateInput, options?: { ctx?: Context }): Promise<Competitor> {
        const inputs = new CreateCompetitorParams({
            salesTeamGroupId: options?.ctx?.currentSalesTeamGroup?.id,
            regionId: options?.ctx?.currentSalesTeamGroup?.region?.id,
            advantage: params.advantage,
            disadvantage: params.disadvantage,
            memo: params.memo,
            name: params.name,
            strategy: params.strategy,
            brand: params.brand,
            model: params.model,
            agents: params.agents,
            businessProductIds: params.businessProductIds,
            createdUserId: options?.ctx?.currentUser?.id,
        });
        const row = await this.gRPCrepo.create(inputs);

        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(params: UpdateInput, options?: { ctx?: Context }): Promise<Competitor> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateCompetitorParams({
            id: row.id,
            advantage: handler.toString({ input: params.advantage, default: row.advantage }),
            disadvantage: handler.toString({
                input: params.disadvantage,
                default: row.disadvantage,
            }),
            memo: handler.toString({ input: params.memo, default: row.memo }),
            name: handler.toString({ input: params.name, default: row.name }),
            strategy: handler.toString({ input: params.strategy, default: row.strategy }),
            brand: handler.toString({ input: params.brand, default: row.brand }),
            model: handler.toString({ input: params.model, default: row.model }),
            agents: handler.toString({ input: params.agents, default: row.agents }),
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        if (params.businessProductIds) {
            inputs.businessProductIds = params.businessProductIds;
        } else {
            if (!row.competitorsBusinessProducts.isInitialized()) {
                await row.competitorsBusinessProducts.init();
            }
            const items = row.competitorsBusinessProducts.getItems();
            inputs.businessProductIds = items.map((v) => v.businessProductId);
        }

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteCompetitorParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }
}
