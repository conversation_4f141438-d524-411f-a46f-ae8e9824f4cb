import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    CompetitorResult,
    CreateCompetitorParams,
    DeleteCompetitorParams,
    UpdateCompetitorParams,
} from '@packages/erp-protobuf/generated/competitor/competitor_pb';

@Service()
export class CompetitorGrpcRepo {
    serviceNodes: string[] = ['competitor', 'Competitor'];

    async create(params: CreateCompetitorParams): Promise<CompetitorResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CompetitorResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Competitor.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateCompetitorParams): Promise<CompetitorResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CompetitorResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Competitor.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteCompetitorParams): Promise<CompetitorResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CompetitorResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Competitor.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
