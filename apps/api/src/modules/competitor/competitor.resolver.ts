import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { BusinessProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/businessProduct.model';
import type { Competitor } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/competitor.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { EnumPermissionCode } from '~/constants';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { BusinessProductService } from '../business/businessProduct/businessProduct.service';
import { TBusinessProduct } from '../business/businessProduct/businessProduct.type';
import { SalesTeamGroupService } from '../salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '../salesTeam/salesTeamGroup/salesTeamGroup.type';
import { UserService } from '../user/user/user.service';
import { TUser } from '../user/user/user.type';
import { CreateInput, UpdateInput } from './competitor.input';
import { CompetitorService } from './competitor.service';
import { PaginatedObjects, SearchArgs, TCompetitor } from './competitor.type';

@Service()
@Resolver(() => TCompetitor)
export class CompetitorResolver {
    @Inject()
    private service: CompetitorService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCompetitors' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; competitors: Competitor[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, competitors: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCompetitor], { name: 'competitors' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<Competitor[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCompetitor, { name: 'competitor' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<Competitor> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['provider.create'])
    @Mutation(() => TCompetitor, { name: 'createCompetitor' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<Competitor> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['provider.update'])
    @Mutation(() => TCompetitor, { name: 'updateCompetitor' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<Competitor> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['provider.delete'])
    @Mutation(() => Boolean, { name: 'deleteCompetitor' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: Competitor): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroupId });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: Competitor): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUser?.id });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: Competitor): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUser?.id });
    }

    @Inject()
    private businessProductService: BusinessProductService;
    @FieldResolver(() => [TBusinessProduct], { description: '商機商品', nullable: true })
    async businessProducts(@Root() model: Competitor): Promise<BusinessProduct[]> {
        const { rows } = await this.businessProductService.search({ competitorId: model.id });
        return rows;
    }
}
