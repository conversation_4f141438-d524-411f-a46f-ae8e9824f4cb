import { Field, ID, InputType } from 'type-graphql';

@InputType('CompetitorInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    advantage?: string;

    @Field({ nullable: true })
    disadvantage?: string;

    @Field({ nullable: true })
    strategy?: string;

    @Field({ nullable: true })
    brand?: string;

    @Field({ nullable: true })
    model?: string;

    @Field({ nullable: true })
    agents?: string;

    @Field({ nullable: true })
    memo?: string;

    @Field(() => [ID], { nullable: true })
    businessProductIds?: number[];

    deleted?: boolean;
}

@InputType('CompetitorCreateInput')
export class CreateInput extends CommonInput {}

@InputType('CompetitorUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
