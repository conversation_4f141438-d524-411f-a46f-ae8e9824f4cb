import type { Competitor } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/competitor.model';
import { ICompetitor } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/competitor.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('Competitor', { implements: ICompetitor })
export class TCompetitor extends ICompetitor {}

@ObjectType('PaginatedCompetitors')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCompetitor], { nullable: true })
    competitors: Competitor[];
}

@InputType('CompetitorFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field({
        nullable: true,
        description: '關鍵字（同時查詢「名稱、廠牌、機型、代理商」欄位）',
    })
    keyword?: string;

    @Field(() => ID, { nullable: true, description: '商機' })
    businessId?: number;

    @Field({ nullable: true, description: '名稱' })
    name?: string;

    @Field({ nullable: true, description: '廠牌' })
    brand?: string;

    @Field({ nullable: true, description: '機型' })
    model?: string;

    @Field({ nullable: true, description: '代理商' })
    agents?: string;

    @Field(() => [ID], { nullable: true, description: '商機商品（複選）' })
    businessProductIds?: number[];

    @Field({ nullable: true, description: '商機商品名稱' })
    businessProductName?: string;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
