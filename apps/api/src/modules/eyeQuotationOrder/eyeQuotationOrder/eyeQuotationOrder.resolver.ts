import { BaseError } from '@clinico/base-error';
import type { EyeServiceOrder } from '@clinico/mikro-orm-persistence/models/maintenance/eyeServiceOrder.model';
import type { EyeWarrantyPeriodType } from '@clinico/mikro-orm-persistence/models/maintenance/eyeWarrantyPeriodType.model';
import type { PublicCity } from '@clinico/mikro-orm-persistence/models/public/city.model';
import type { PublicCompany } from '@clinico/mikro-orm-persistence/models/public/company.model';
import { PublicCostCenter } from '@clinico/mikro-orm-persistence/models/public/costCenter.model';
import type { PublicCreditPeriod } from '@clinico/mikro-orm-persistence/models/public/creditPeriod.model';
import type { PublicCurrency } from '@clinico/mikro-orm-persistence/models/public/currency.model';
import type { PublicDepartment } from '@clinico/mikro-orm-persistence/models/public/department.model';
import type { PublicDistrict } from '@clinico/mikro-orm-persistence/models/public/district.model';
import type { PublicEyeQuotationOrder } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrder.model';
import { PublicEyeQuotationOrderAttachFile } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrderAttachFile.model';
import { PublicEyeQuotationOrderBusiness } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrderBusiness.model';
import { PublicEyeQuotationOrderCommissionAmount } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrderCommissionAmount.model';
import { PublicEyeQuotationOrderCommissionType } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrderCommissionType.model';
import type { PublicEyeQuotationOrderProduct } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrderProduct.model';
import type { PublicEyeQuotationOrderProductItem } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrderProductItem.model';
import { PublicEyeQuotationOrderProductItemWarrantyPrice } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrderProductItemWarrantyPrice.model';
import type { PublicEyeQuotationOrderPromotion } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrderPromotion.model';
import { PublicEyeQuotationOrderType } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrderType.model';
import type { PublicEyeQuotationOrderWarrantyItem } from '@clinico/mikro-orm-persistence/models/public/eyeQuotationOrderWarrantyItem.model';
import { PublicFinancialCompany } from '@clinico/mikro-orm-persistence/models/public/financialCompany.model';
import type { PublicProvince } from '@clinico/mikro-orm-persistence/models/public/province.model';
import type { PublicRegion } from '@clinico/mikro-orm-persistence/models/public/region.model';
import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import { PublicWarehouse } from '@clinico/mikro-orm-persistence/models/public/warehouse.model';
import type { Business } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/business.model';
import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import { URLResolver } from 'graphql-scalars';
import { FORBIDDEN } from 'http-status';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Mutation, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';



import { configs } from '~/app.config';
import { EnumPermissionCode } from '~/constants';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { EnumBPMForm } from '~/modules/bpm/formInstance/formInstance.type';
import { BusinessService } from '~/modules/business/business/business.service';
import { TBusiness } from '~/modules/business/business/business.type';
import { CustomerService } from '~/modules/customer/customer/customer.service';
import { TCustomer } from '~/modules/customer/customer/customer.type';
import { EyeServiceOrderService } from '~/modules/eyeServiceOrder/eyeServiceOrder/eyeServiceOrder.service';
import { TEyeServiceOrder } from '~/modules/eyeServiceOrder/eyeServiceOrder/eyeServiceOrder.type';
import { EyeWarrantyPeriodTypeService } from '~/modules/eyeWarranty/eyeWarrantyPeriodType/eyeWarrantyPeriodType.service';
import { TEyeWarrantyPeriodType } from '~/modules/eyeWarranty/eyeWarrantyPeriodType/eyeWarrantyPeriodType.type';
import { FormInstanceTaskService } from '~/modules/forms/formInstanceTask/formInstanceTask.service';
import { TFormInstanceTask } from '~/modules/forms/formInstanceTask/formInstanceTask.type';
import { CityService } from '~/modules/public/city/city.service';
import { TCity } from '~/modules/public/city/city.type';
import { CompanyService } from '~/modules/public/company/company.service';
import { TCompany } from '~/modules/public/company/company.type';
import { CostCenterService } from '~/modules/public/costCenter/costCenter.service';
import { TCostCenter } from '~/modules/public/costCenter/costCenter.type';
import { CreditPeriodService } from '~/modules/public/creditPeriod/creditPeriod.service';
import { TCreditPeriod } from '~/modules/public/creditPeriod/creditPeriod.type';
import { CurrencyService } from '~/modules/public/currency/currency.service';
import { TCurrency } from '~/modules/public/currency/currency.type';
import { DistrictService } from '~/modules/public/district/district.service';
import { TDistrict } from '~/modules/public/district/district.type';
import { FinancialCompanyService } from '~/modules/public/financialCompany/financialCompany.service';
import { TFinancialCompany } from '~/modules/public/financialCompany/financialCompany.type';
import { ProvinceService } from '~/modules/public/province/province.service';
import { TProvince } from '~/modules/public/province/province.type';
import { RegionService } from '~/modules/public/region/region.service';
import { TRegion } from '~/modules/public/region/region.type';
import { SalesTeamUnitService } from '~/modules/salesTeam/salesTeamUnit/salesTeamUnit.service';
import { TSalesTeamUnit } from '~/modules/salesTeam/salesTeamUnit/salesTeamUnit.type';
import { DepartmentService } from '~/modules/user/department/department.service';
import { TDepartment } from '~/modules/user/department/department.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import { WarehouseService } from '~/modules/warehouse/warehouse.service';
import { TWarehouse } from '~/modules/warehouse/warehouse.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';



import { EyeQuotationOrderAttachFileService } from '../eyeQuotationOrderAttachFile/eyeQuotationOrderAttachFile.service';
import { TEyeQuotationOrderAttachFile } from '../eyeQuotationOrderAttachFile/eyeQuotationOrderAttachFile.type';
import { EyeQuotationOrderBusinessService } from '../eyeQuotationOrderBusiness/eyeQuotationOrderBusiness.service';
import { TEyeQuotationOrderBusiness } from '../eyeQuotationOrderBusiness/eyeQuotationOrderBusiness.type';
import { EyeQuotationOrderCommissionAmountService } from '../eyeQuotationOrderCommissionAmount/eyeQuotationOrderCommissionAmount.service';
import { TEyeQuotationOrderCommissionAmount } from '../eyeQuotationOrderCommissionAmount/eyeQuotationOrderCommissionAmount.type';
import { EyeQuotationOrderCommissionTypeService } from '../eyeQuotationOrderCommissionType/eyeQuotationOrderCommissionType.service';
import { TEyeQuotationOrderCommissionType } from '../eyeQuotationOrderCommissionType/eyeQuotationOrderCommissionType.type';
import { EyeQuotationOrderProductService } from '../eyeQuotationOrderProduct/eyeQuotationOrderProduct.service';
import { TEyeQuotationOrderProduct } from '../eyeQuotationOrderProduct/eyeQuotationOrderProduct.type';
import { EyeQuotationOrderProductItemService } from '../eyeQuotationOrderProductItem/eyeQuotationOrderProductItem.service';
import { TEyeQuotationOrderProductItem } from '../eyeQuotationOrderProductItem/eyeQuotationOrderProductItem.type';
import { EyeQuotationOrderProductItemWarrantyPriceService } from '../eyeQuotationOrderProductItemWarrantyPrice/eyeQuotationOrderProductItemWarrantyPrice.service';
import { TEyeQuotationOrderProductItemWarrantyPrice } from '../eyeQuotationOrderProductItemWarrantyPrice/eyeQuotationOrderProductItemWarrantyPrice.type';
import { EyeQuotationOrderPromotionService } from '../eyeQuotationOrderPromotion/eyeQuotationOrderPromotion.service';
import { TEyeQuotationOrderPromotion } from '../eyeQuotationOrderPromotion/eyeQuotationOrderPromotion.type';
import { EyeQuotationOrderTypeService } from '../eyeQuotationOrderType/eyeQuotationOrderType.service';
import { TEyeQuotationOrderType } from '../eyeQuotationOrderType/eyeQuotationOrderType.type';
import { TEyeQuotationOrderWarrantyItem } from '../eyeQuotationOrderWarrantyItem/eyeQuotationOrderWarrantyItem.type';
import { CreateInput, RequestEyeQuotationOrderOfficialSealInput, UpdateInput } from './eyeQuotationOrder.input';
import { EyeQuotationOrderService } from './eyeQuotationOrder.service';
import { PaginatedObjects, SearchArgs, TEyeQuotationOrder } from './eyeQuotationOrder.type';


@Service()
@Resolver(() => TEyeQuotationOrder)
export class EyeQuotationOrderResolver {
    @Inject()
    private service: EyeQuotationOrderService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeQuotationOrders' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeQuotationOrders: PublicEyeQuotationOrder[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeQuotationOrders: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeQuotationOrder], { name: 'eyeQuotationOrders' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeQuotationOrder[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeQuotationOrder, { name: 'eyeQuotationOrder' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeQuotationOrder> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['quotation.create'])
    @Mutation(() => TEyeQuotationOrder, { name: 'createEyeQuotationOrder' })
    async create(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<PublicEyeQuotationOrder> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TEyeQuotationOrder, { name: 'updateEyeQuotationOrder' })
    async update(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<PublicEyeQuotationOrder> {
        throw new BaseError('PublicEyeQuotationOrder cannot be updated', FORBIDDEN);
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, {
        name: 'requestEyeQuotationOrderOfficialSeal',
        description: '申請報價單合約用印',
    })
    async requestOfficialSeal(
        @Ctx() ctx: Context,
        @Arg('input') input: RequestEyeQuotationOrderOfficialSealInput
    ): Promise<boolean> {
        await this.service.requestOfficialSealByGrpc(input, { ctx });
        return true;
    }

    @Inject()
    private regionService: RegionService;
    @FieldResolver(() => TRegion)
    async region(@Root() model: PublicEyeQuotationOrder): Promise<PublicRegion | undefined> {
        return await this.regionService.findOne({ id: model.regionId });
    }

    @Inject()
    private companyService: CompanyService;
    @FieldResolver(() => TCompany)
    async company(@Root() model: PublicEyeQuotationOrder): Promise<PublicCompany | undefined> {
        return await this.companyService.findOne({ id: model.companyId });
    }

    @Inject()
    private businessService: BusinessService;
    @FieldResolver(() => TBusiness, { nullable: true, deprecationReason: '改用「多對一關聯」' })
    async business(@Root() model: PublicEyeQuotationOrder): Promise<Business | undefined> {
        return await this.businessService.findOne({ id: model.businessId });
    }

    @Inject()
    private eyeQuotationOrderBusinessService: EyeQuotationOrderBusinessService;
    @FieldResolver(() => [TEyeQuotationOrderBusiness], { description: '眼科報價單商機' })
    async eyeQuotationOrderBusinesses(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicEyeQuotationOrderBusiness[]> {
        const { rows } = await this.eyeQuotationOrderBusinessService.search({
            eyeQuotationOrderId: model.id,
        });
        return rows;
    }

    @Inject()
    private customerService: CustomerService;
    @FieldResolver(() => TCustomer, { nullable: true })
    async invoicingCustomer(@Root() model: PublicEyeQuotationOrder): Promise<Customer | undefined> {
        return await this.customerService.findOne({ id: model.invoicingCustomerId });
    }
    @FieldResolver(() => TCustomer, { nullable: true })
    async customer(@Root() model: PublicEyeQuotationOrder): Promise<Customer | undefined> {
        return await this.customerService.findOne({ id: model.customerId });
    }

    @Inject()
    private provinceService: ProvinceService;
    @FieldResolver(() => TProvince, { nullable: true })
    async province(@Root() model: PublicEyeQuotationOrder): Promise<PublicProvince | undefined> {
        return await this.provinceService.findOne({ id: model.provinceId });
    }

    @Inject()
    private cityService: CityService;
    @FieldResolver(() => TCity, { nullable: true })
    async city(@Root() model: PublicEyeQuotationOrder): Promise<PublicCity | undefined> {
        return await this.cityService.findOne({ id: model.cityId });
    }

    @Inject()
    private districtService: DistrictService;
    @FieldResolver(() => TDistrict, { nullable: true })
    async district(@Root() model: PublicEyeQuotationOrder): Promise<PublicDistrict | undefined> {
        return await this.districtService.findOne({ id: model.districtId });
    }

    @Inject()
    private departmentService: DepartmentService;
    @FieldResolver(() => TDepartment)
    async dept(@Root() model: PublicEyeQuotationOrder): Promise<PublicDepartment | undefined> {
        return await this.departmentService.findOne({ id: model.deptId });
    }

    @Inject()
    private eyeWarrantyPeriodTypeService: EyeWarrantyPeriodTypeService;
    @FieldResolver(() => TEyeWarrantyPeriodType, { nullable: true })
    async eyeWarrantyPeriodType(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<EyeWarrantyPeriodType | undefined> {
        return await this.eyeWarrantyPeriodTypeService.findOne({ id: model.warrantyPeriodTypeId });
    }

    @Inject()
    private creditPeriodService: CreditPeriodService;
    @FieldResolver(() => TCreditPeriod, { nullable: true })
    async creditPeriod(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicCreditPeriod | undefined> {
        return await this.creditPeriodService.findOne({ id: model.creditPeriodId });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser)
    async user(@Root() model: PublicEyeQuotationOrder): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.userId });
    }
    @FieldResolver(() => TUser)
    async createdUser(@Root() model: PublicEyeQuotationOrder): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser)
    async updatedUser(@Root() model: PublicEyeQuotationOrder): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }

    @Inject()
    private currencyService: CurrencyService;
    @FieldResolver(() => TCurrency)
    async currency(@Root() model: PublicEyeQuotationOrder): Promise<PublicCurrency | undefined> {
        return await this.currencyService.findOne({ id: model.currencyId });
    }
    @FieldResolver(() => TCurrency)
    async localCurrency(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicCurrency | undefined> {
        return await this.currencyService.findOne({ id: model.localCurrencyId });
    }

    @Inject()
    private costCenterService: CostCenterService;
    @FieldResolver(() => TCostCenter, { nullable: true, description: '成本中心' })
    async costCenter(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicCostCenter | undefined> {
        return await this.costCenterService.findOne({ id: model.costCenterId });
    }

    @Inject()
    private eyeQuotationOrderProductService: EyeQuotationOrderProductService;
    @FieldResolver(() => [TEyeQuotationOrderProduct])
    async eyeQuotationOrderProducts(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicEyeQuotationOrderProduct[]> {
        const { rows } = await this.eyeQuotationOrderProductService.search({
            eyeQuotationOrderId: model.id,
        });
        return rows;
    }

    @Inject()
    private eyeQuotationOrderPromotionService: EyeQuotationOrderPromotionService;
    @FieldResolver(() => [TEyeQuotationOrderPromotion])
    async eyeQuotationOrderPromotions(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicEyeQuotationOrderPromotion[]> {
        const { rows } = await this.eyeQuotationOrderPromotionService.search({
            eyeQuotationOrderId: model.id,
        });
        return rows;
    }

    @Inject()
    private eyeQuotationOrderProductItemService: EyeQuotationOrderProductItemService;
    @FieldResolver(() => [TEyeQuotationOrderProductItem])
    async eyeQuotationOrderProductItems(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicEyeQuotationOrderProductItem[]> {
        const { rows } = await this.eyeQuotationOrderProductItemService.search({
            eyeQuotationOrderId: model.id,
        });
        return rows;
    }

    @Inject()
    private eyeServiceOrderService: EyeServiceOrderService;
    @FieldResolver(() => TEyeServiceOrder, { nullable: true })
    async eyeServiceOrder(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<EyeServiceOrder | undefined> {
        return await this.eyeServiceOrderService.findOne({ id: model.eyeServiceOrder?.id });
    }

    @FieldResolver(() => [TEyeQuotationOrderWarrantyItem])
    async eyeQuotationOrderWarrantyItems(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicEyeQuotationOrderWarrantyItem[]> {
        if (!model.eyeQuotationOrderWarrantyItems.isInitialized()) {
            await model.eyeQuotationOrderWarrantyItems.init();
        }
        return model.eyeQuotationOrderWarrantyItems.getItems();
    }

    @FieldResolver(() => URLResolver, { nullable: true, description: 'BPM 連結' })
    async bpmUrl(@Root() model: PublicEyeQuotationOrder): Promise<string | undefined> {
        if (!model.bpmInstanceId) return undefined;

        const url = new URL('/formInstance', configs.bpm.web.host);
        url.searchParams.set(
            'code',
            model.regionId == 1 ? EnumBPMForm.QuotationOrderTW : EnumBPMForm.QuotationOrderCN
        );
        url.searchParams.set('formInstanceId', model.bpmInstanceId);
        return url.toString();
    }

    @Inject()
    private eyeQuotationOrderTypeService: EyeQuotationOrderTypeService;
    @FieldResolver(() => TEyeQuotationOrderType, { nullable: true, description: '眼科报价单类别' })
    async eyeQuotationOrderType(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicEyeQuotationOrderType | undefined> {
        return await this.eyeQuotationOrderTypeService.findOne({
            id: model.eyeQuotationOrderTypeId,
        });
    }

    @FieldResolver((returns) => URLResolver, {
        nullable: true,
        description: 'BPM連結',
    })
    async officialSealBpmUrl(@Root() model: PublicEyeQuotationOrder): Promise<string | undefined> {
        if (!model.bpmOfficialSealInstanceId) return undefined;

        const url = new URL('/formInstance', configs.bpm.web.host);
        url.searchParams.set(
            'code',
            model.regionId == 1
                ? EnumBPMForm.QuotationOfficialSealTW
                : EnumBPMForm.QuotationOfficialSealCN
        );
        url.searchParams.set('formInstanceId', model.bpmOfficialSealInstanceId);
        return url.toString();
    }

    @Inject()
    private financialCompanyService: FinancialCompanyService;
    @FieldResolver(() => TFinancialCompany, { nullable: true, description: '公司识别号' })
    async financialCompany(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicFinancialCompany | undefined> {
        return await this.financialCompanyService.findOne({
            id: model.financialCompanyId,
        });
    }

    @Inject()
    private eyeQuotationOrderProductItemWarrantyPriceService: EyeQuotationOrderProductItemWarrantyPriceService;
    @FieldResolver(() => [TEyeQuotationOrderProductItemWarrantyPrice])
    async eyeQuotationOrderProductItemWarrantyPrices(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicEyeQuotationOrderProductItemWarrantyPrice[]> {
        const result = await this.eyeQuotationOrderProductItemWarrantyPriceService.search({
            eyeQuotationOrderId: model.id,
        });
        return result.rows;
    }

    @Inject()
    private eyeQuotationOrderCommissionTypeService: EyeQuotationOrderCommissionTypeService;
    @FieldResolver(() => TEyeQuotationOrderCommissionType, {
        nullable: true,
        description: '研究费类别',
    })
    async eyeQuotationOrderCommissionType(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicEyeQuotationOrderCommissionType | undefined> {
        return await this.eyeQuotationOrderCommissionTypeService.findOne({
            id: model.eyeQuotationOrderCommissionTypeId,
        });
    }

    @Inject()
    private eyeQuotationOrderCommissionAmountService: EyeQuotationOrderCommissionAmountService;
    @FieldResolver((returns) => [TEyeQuotationOrderCommissionAmount], {
        nullable: true,
        description: '研究费',
    })
    async eyeQuotationOrderCommissionAmounts(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicEyeQuotationOrderCommissionAmount[]> {
        const { rows } = await this.eyeQuotationOrderCommissionAmountService.search({
            eyeQuotationOrderId: model.id,
        });
        return rows;
    }

    @Inject()
    private formInstanceTaskService: FormInstanceTaskService;
    @FieldResolver((returns) => TFormInstanceTask, {
        nullable: true,
        description: '当前审批人',
    })
    async formInstanceTasks(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<TFormInstanceTask | null> {
        const bpmInstanceId = model.bpmInstanceId;
        if (!bpmInstanceId) return null;
        const result = await this.formInstanceTaskService.search({
            formInstanceId: Number(bpmInstanceId),
        });
        return result;
    }

    @Inject()
    private warehouseService: WarehouseService;
    @FieldResolver((returns) => TWarehouse, {
        nullable: true,
        description: '倉儲',
    })
    async warehouse(@Root() model: PublicEyeQuotationOrder): Promise<TWarehouse | undefined> {
        return await this.warehouseService.findOne({ id: model.warehouseId });
    }

    @Inject()
    private salesTeamUnitService: SalesTeamUnitService;
    @FieldResolver(() => TSalesTeamUnit, { nullable: true, description: '業務團隊位置' })
    async salesTeamUnit(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<TSalesTeamUnit | undefined> {
        return await this.salesTeamUnitService.findOne({ id: model.salesTeamUnitId });
    }

    @Inject()
    private eyeQuotationOrderAttachFileService: EyeQuotationOrderAttachFileService;
    @FieldResolver((returns) => [TEyeQuotationOrderAttachFile], {
        nullable: true,
        description: '附件',
    })
    async eyeQuotationOrderAttachFiles(
        @Root() model: PublicEyeQuotationOrder
    ): Promise<PublicEyeQuotationOrderAttachFile[]> {
        const { rows } = await this.eyeQuotationOrderAttachFileService.search({
            eyeQuotationOrderId: model.id,
        });

        return rows;
    }
}