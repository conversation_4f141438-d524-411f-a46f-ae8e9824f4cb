export enum EnumBPMForm {
    QuotationOrderCN = 'CN_Quotation',
    QuotationOrderTW = 'TW_Quotation',
    FixedAssetRentalCN = 'CN_FixedAssetRental',
    FixedAssetRentalTW = 'TW_FixedAssetRental',
    FixedAssetRentalExtendCN = 'CN_FixedAssetRentalExtend',
    FixedAssetRentalExtendTW = 'TW_FixedAssetRentalExtend',
    QuotationOfficialSealCN = 'CN_QuotationOfficialSeal',
    QuotationOfficialSealTW = 'TW_QuotationOfficialSeal',
    FixedAssetRentalUpdateCN = 'CN_FixedAssetRentalUpdate',
    FixedAssetRentalUpdateTW = 'TW_FixedAssetRentalUpdate',
    MarketActivityCN = 'CN_MarketActivity',
    DistributorAuthorizationTW = 'TW_DistributorAuthorization',
    DistributorAuthorizationCN = 'CN_DistributorAuthorization',
}
