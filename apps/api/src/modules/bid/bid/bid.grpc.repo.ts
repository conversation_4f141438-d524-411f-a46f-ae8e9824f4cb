import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    BidResult,
    CreateBidParams,
    DeleteBidParams,
    UpdateBidParams,
} from '@packages/erp-protobuf/generated/bid/bid_pb';

@Service()
export class BidGrpcRepo {
    serviceNodes: string[] = ['bid', 'Bid'];

    async create(params: CreateBidParams): Promise<BidResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BidResult>('create', params);
        
            return res;
        } catch (err: any) {
            const message = ['[gRPC.Bid.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateBidParams): Promise<BidResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BidResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Bid.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteBidParams): Promise<BidResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BidResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Bid.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
