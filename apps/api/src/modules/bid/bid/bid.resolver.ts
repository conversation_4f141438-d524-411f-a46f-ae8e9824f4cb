import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { AuthorizationProduct as BidEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/authorizationProduct.model';
import type { Bid } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/bid.model';
import type { BidAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/bidAttachment.model';
import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { CustomerService } from '~/modules/customer/customer/customer.service';
import { TCustomer } from '~/modules/customer/customer/customer.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { TBidAttachment } from '../bidAttachment/bidAttachment.type';
import { TBidEquipment } from '../bidEquipment/bidEquipment.type';
import { CreateInput, UpdateInput } from './bid.input';
import { BidService } from './bid.service';
import { PaginatedObjects, SearchArgs, TBid } from './bid.type';

@Service()
@Resolver(() => TBid)
export class BidResolver {
    @Inject()
    private service: BidService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBids' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; bids: Bid[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, bids: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBid], { name: 'bids' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<Bid[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBid, { name: 'bid' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<Bid> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBid, { name: 'createBid' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<Bid> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBid, { name: 'updateBid' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<Bid> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteBid' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private customerService: CustomerService;
    @FieldResolver(() => TCustomer, { description: '客戶', nullable: true })
    async customer(@Root() model: Bid): Promise<Customer | undefined> {
        return await this.customerService.findOne({ id: model.customer.id });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: Bid): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUser?.id });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: Bid): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUser?.id });
    }

    // TODO: wait to check
    @FieldResolver(() => [TBidAttachment], { description: '附件' })
    async attachments(@Root() model: Bid): Promise<BidAttachment[]> {
        if (!model.bidAttachments.isInitialized()) {
            await model.bidAttachments.init({
                where: { deleted: false },
            });
        }
        return model.bidAttachments.getItems();
    }

    // TODO: wait to check bidEquipments
    @FieldResolver(() => [TBidEquipment], { description: '招投標設備' })
    async bidEquipments(@Root() model: Bid): Promise<BidEquipment[]> {
        if (!model.bidsEquipment.isInitialized()) {
            await model.bidsEquipment.init({
                populate: ['bidEquipment'],
                where: { bidEquipment: { deleted: false } },
            });
        }
        return model.bidsEquipment.getItems().map((v) => v.bidEquipment);
    }
}
