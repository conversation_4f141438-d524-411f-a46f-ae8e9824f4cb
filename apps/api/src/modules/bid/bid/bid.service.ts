import { Bid } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/bid.model';
import { FilterQuery } from '@mikro-orm/core';
import _ from 'lodash';
import { Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import {
    AttachmentParams,
    CreateBidParams,
    DeleteBidParams,
    FileInfo,
    UpdateBidParams,
} from '@packages/erp-protobuf/generated/bid/bid_pb';
import { DateOnlyHandler } from '@packages/utils/date';

import { streamToBuffer } from '~/utils/helpers/fileHandler.helper';
import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { BidGrpcRepo } from './bid.grpc.repo';
import { CreateInput, UpdateInput } from './bid.input';
import { FilterInput } from './bid.type';

@Service()
export class BidService extends BaseService<Bid> {
    protected entity = Bid;
    private gRPCrepo = new BidGrpcRepo();

    async search(
        params?: FilterInput,
        options?: {
            ctx?: Context;
            pagination?: PaginationInput;
            count?: boolean;
            populate?: 'Default' | 'Full';
        }
    ): Promise<{ count: number | null; rows: Bid[] }> {
        const em = this.em.fork();

        const where: FilterQuery<Bid>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        // Filter input
        this.appendFilterByParams(where, params);

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
                populate: ['customer', 'createdUser', 'updatedUser'],
            }
        );

        return { rows, count };
    }

    private appendFilterByParams(where: FilterQuery<Bid>[], params: FilterInput | undefined) {
        if (params?.bidDate1) {
            const date = new DateOnlyHandler({ date: params.bidDate1 }).toString();
            where.push({ bidDate: { $gte: date } });
        }
        if (params?.bidDate2) {
            const date = new DateOnlyHandler({ date: params.bidDate2 }).toString();
            where.push({ bidDate: { $lte: date } });
        }
        if (params?.publishDate1) {
            const date = new DateOnlyHandler({ date: params.publishDate1 }).toString();
            where.push({ publishDate: { $gte: date } });
        }
        if (params?.publishDate2) {
            const date = new DateOnlyHandler({ date: params.publishDate2 }).toString();
            where.push({ publishDate: { $lte: date } });
        }
        if (params?.registrationDate1) {
            const date = new DateOnlyHandler({ date: params.registrationDate1 }).toString();
            where.push({ registrationDate: { $gte: date } });
        }
        if (params?.registrationDate2) {
            const date = new DateOnlyHandler({ date: params.registrationDate2 }).toString();
            where.push({ registrationDate: { $lte: date } });
        }
        if (params?.customerId) {
            where.push({ customer: { id: params.customerId } });
        }
        if (params?.code) {
            where.push({ code: { $ilike: `%${params.code}%` } });
        }

        if (params?.name) {
            where.push({ name: { $ilike: `%${params.name}%` } });
        }
        if (params?.result) {
            where.push({ result: params.result });
        }
        if (params?.customerCode) {
            where.push({ customer: { code: { $ilike: `%${params.customerCode}%` } } });
        }
        if (params?.customerName) {
            where.push({ customer: { name: { $ilike: `%${params.customerName}%` } } });
        }
        if (params?.equipmentIds && params?.equipmentIds.length) {
            where.push({
                bidsEquipment: {
                    bidEquipment: {
                        id: { $in: params.equipmentIds },
                        deleted: false,
                    },
                },
            });
        }
    }

    async createByGrpc(params: CreateInput, options?: { ctx?: Context }): Promise<Bid> {
        const inputs = new CreateBidParams({
            regionId: options?.ctx?.currentSalesTeamGroup?.region?.id,
            customerId: params.customerId,
            bidDate: handler.toTimestamp({ input: params.bidDate }),
            publishDate: handler.toTimestamp({ input: params.publishDate }),
            registrationDate: handler.toTimestamp({ input: params.registrationDate }),
            name: handler.toString({ input: params.name }),
            publicLinkUrl: handler.toString({ input: params.publicLinkUrl }),
            content: handler.toString({ input: params.content }),
            result: _.isNil(params.result) ? undefined : params.result,
            notes: handler.toString({ input: params.notes }),
            equipmentIds: params.equipmentIds,
            createdUserId: options?.ctx?.currentUser?.id,
        });
        if (params.attachments) {
            inputs.attachments = [];
            for (const attachment of params.attachments) {
                const file = await attachment.file;
                const stream = file.createReadStream();
                const buffer = await streamToBuffer(stream);

                const params = new AttachmentParams({
                    name: attachment.name || file.filename,
                    file: new FileInfo({ ...file, content: buffer }),
                });
                inputs.attachments.push(params);
            }
        }

        const row = await this.gRPCrepo.create(inputs);
        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(params: UpdateInput, options?: { ctx?: Context }): Promise<Bid> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateBidParams({
            id: row.id,
            customerId: handler.toNumber({
                input: params.customerId,
                default: row.customerId,
            }),
            bidDate: handler.toTimestamp({ input: params.bidDate, default: row.bidDate }),
            publishDate: handler.toTimestamp({
                input: params.publishDate,
                default: row.publishDate,
            }),
            registrationDate: handler.toTimestamp({
                input: params.registrationDate,
                default: row.registrationDate,
            }),
            name: handler.toString({ input: params.name, default: row.name }),
            publicLinkUrl: handler.toString({
                input: params.publicLinkUrl,
                default: row.publicLinkUrl,
            }),
            content: handler.toString({ input: params.content, default: row.content }),
            result: (() => {
                const val = handler.toString({ input: params.result, default: row.result });
                return val ? val : undefined;
            })(),
            notes: handler.toString({ input: params.notes, default: row.notes }),
            updatedUserId: options?.ctx?.currentUser?.id,
        });


        if (params.equipmentIds) {
            inputs.equipmentIds = params.equipmentIds;
        } else {
            if (!row.bidsEquipment.isInitialized()) {
                await row.bidsEquipment.init({ populate: ['bidEquipment'] });
            }
            const items = row.bidsEquipment.getItems();
            inputs.equipmentIds = items.map((item) => item.bidEquipment.id);
        }


        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteBidParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }
}
