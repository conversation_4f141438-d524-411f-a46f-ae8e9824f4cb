import type {
    Bid,
    EnumBidResult,
} from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/bid.model';
import {
    IBid,
    EnumBidResult as IEnumBidResult,
} from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/bid.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('Bid', { implements: IBid })
export class TBid extends IBid {}

@ObjectType('PaginatedBids')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBid], { nullable: true })
    customerEquipments: Bid[];
}

@InputType('BidFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => String, { nullable: true })
    code?: string;

    @Field(() => ID, { nullable: true })
    customerId?: number;

    @Field({ nullable: true, description: '客戶名稱' })
    customerName?: string;

    @Field({ nullable: true, description: '客戶編號' })
    customerCode?: string;

    @Field(() => String, { nullable: true, description: '投標開始時間（起）' })
    bidDate1?: string;

    @Field(() => String, { nullable: true, description: '投標開始時間（迄）' })
    bidDate2?: string;

    @Field(() => String, { nullable: true, description: '發佈時間（起）' })
    publishDate1?: string;

    @Field(() => String, { nullable: true, description: '發佈時間（迄）' })
    publishDate2?: string;

    @Field(() => String, { nullable: true, description: '報名開始時間（起）' })
    registrationDate1?: string;

    @Field(() => String, { nullable: true, description: '報名開始時間（迄）' })
    registrationDate2?: string;

    @Field({ nullable: true, description: '項目名稱' })
    name?: string;

    @Field(() => IEnumBidResult, { nullable: true, description: '結果' })
    result?: EnumBidResult;

    @Field((type) => [ID], { nullable: true, description: '投標設備' })
    equipmentIds?: number[];
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
