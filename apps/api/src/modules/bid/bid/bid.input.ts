import { EnumBidResult } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/bid.model';
import { EnumBidResult as IEnumBidResult } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/bid.model';
import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('BidInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    customerId?: number;

    @Field({ nullable: true })
    bidDate?: Date;

    @Field({ nullable: true })
    publishDate?: Date;

    @Field({ nullable: true })
    registrationDate?: Date;

    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    content?: string;

    @Field({ nullable: true })
    notes?: string;

    @Field({ nullable: true })
    publicLinkUrl?: string;

    @Field(() => IEnumBidResult, { nullable: true })
    result?: EnumBidResult;

    @Field((type) => [ID], { nullable: true })
    equipmentIds?: number[];
}

@InputType('BidEAttachmentRelationInput')
export class RelationInput extends CommonInput {
    @Field(() => GraphQLUpload)
    file: Promise<FileUpload>;
}

@InputType('BidCreateInput')
export class CreateInput extends CommonInput {
    @Field(() => [RelationInput], { nullable: true })
    attachments?: RelationInput[];
}


@InputType('BidUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
