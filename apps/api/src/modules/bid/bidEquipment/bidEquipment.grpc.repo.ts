import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    BidEquipmentResult,
    CreateBidEquipmentParams,
    DeleteBidEquipmentParams,
    UpdateBidEquipmentParams,
} from '@packages/erp-protobuf/generated/bid/bidEquipment_pb';

@Service()
export class BidEquipmentGrpcRepo {
    serviceNodes: string[] = ['bidEquipment', 'BidEquipment'];

    async create(params: CreateBidEquipmentParams): Promise<BidEquipmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BidEquipmentResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.BidEquipment.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateBidEquipmentParams): Promise<BidEquipmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BidEquipmentResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.BidEquipment.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteBidEquipmentParams): Promise<BidEquipmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BidEquipmentResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.BidEquipment.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
