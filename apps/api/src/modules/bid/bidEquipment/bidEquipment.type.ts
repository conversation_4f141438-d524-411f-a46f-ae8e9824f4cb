import type { AuthorizationProduct as BidEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/authorizationProduct.model';
import { IAuthorizationProduct as IBidEquipment } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/authorizationProduct.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BidEquipment', { implements: IBidEquipment })
export class TBidEquipment extends IBidEquipment {}

@ObjectType('PaginatedBidEquipments')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBidEquipment], { nullable: true })
    bidEquipments: BidEquipment[];
}

@InputType('BidEquipmentFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field({ nullable: true })
    code?: string;

    @Field({ nullable: true })
    name?: string;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
