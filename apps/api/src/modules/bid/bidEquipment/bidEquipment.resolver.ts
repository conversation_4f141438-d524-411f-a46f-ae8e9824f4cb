import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { AuthorizationProduct as BidEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/authorizationProduct.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { CreateInput, UpdateInput } from './bidEquipment.input';
import { BidEquipmentService } from './bidEquipment.service';
import { PaginatedObjects, SearchArgs, TBidEquipment } from './bidEquipment.type';

@Service()
@Resolver(() => TBidEquipment)
export class BidEquipmentResolver {
    @Inject()
    private service: BidEquipmentService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBidEquipments' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; bidEquipments: BidEquipment[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, bidEquipments: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBidEquipment], { name: 'bidEquipments' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<BidEquipment[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBidEquipment, { name: 'bidEquipment' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<BidEquipment> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBidEquipment, { name: 'createBidEquipment' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<BidEquipment> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBidEquipment, { name: 'updateBidEquipment' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<BidEquipment> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteBidEquipment' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }
}
