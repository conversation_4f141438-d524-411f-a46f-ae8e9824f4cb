import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('BidEquipmentInput')
export class CommonInput {
    @Field({ nullable: true })
    code?: string;

    @Field({ nullable: true })
    name?: string;
}

@InputType('BidEquipmentCreateInput')
export class CreateInput extends CommonInput {}

@InputType('BidEquipmentUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
