import { AuthorizationProduct as BidEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/authorizationProduct.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import {
    CreateBidEquipmentParams,
    DeleteBidEquipmentParams,
    UpdateBidEquipmentParams,
} from '@packages/erp-protobuf/generated/bid/bidEquipment_pb';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { BidEquipmentGrpcRepo } from './bidEquipment.grpc.repo';
import { CreateInput, UpdateInput } from './bidEquipment.input';
import { FilterInput } from './bidEquipment.type';

@Service()
export class BidEquipmentService extends BaseService<BidEquipment> {
    protected entity = BidEquipment;
    private gRPCrepo = new BidEquipmentGrpcRepo();

    async search(
        params?: FilterInput,
        options?: {
            ctx?: Context;
            pagination?: PaginationInput;
            count?: boolean;
            populate?: 'Default' | 'Full';
        }
    ): Promise<{ count: number | null; rows: BidEquipment[] }> {
        const em = this.em.fork();

        const where: FilterQuery<BidEquipment>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    async createByGrpc(params: CreateInput, options?: { ctx?: Context }): Promise<BidEquipment> {
        const inputs = new CreateBidEquipmentParams({
            code: params.code,
            name: params.name,
            createdUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.create(inputs);
        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(params: UpdateInput, options?: { ctx?: Context }): Promise<BidEquipment> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateBidEquipmentParams({
            id: row.id,
            code: handler.toString({ input: params.code, default: row.code }),
            name: handler.toString({ input: params.name, default: row.name }),
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteBidEquipmentParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }
}
