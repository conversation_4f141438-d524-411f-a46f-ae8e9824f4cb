import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    BidAttachmentUpdateResult,
    BidBulkAttachmentCreateResult,
    BulkCreateBidAttachmentParams,
    CreateBidAttachmentParams,
    DeleteBidAttachmentParams,
    UpdateBidAttachmentParams,
} from '@packages/erp-protobuf/generated/bid/bidAttachment_pb';

@Service()
export class BidAttachmentGrpcRepo {
    serviceNodes: string[] = ['bidAttachment', 'BidAttachment'];

    async bulkCreate(
        params: BulkCreateBidAttachmentParams
    ): Promise<BidBulkAttachmentCreateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BidBulkAttachmentCreateResult>('bulkCreate', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.bid.bulkCreate]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async create(params: CreateBidAttachmentParams): Promise<BidAttachmentUpdateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BidAttachmentUpdateResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.BidAttachment.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateBidAttachmentParams): Promise<BidAttachmentUpdateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BidAttachmentUpdateResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.BidAttachment.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteBidAttachmentParams): Promise<BidAttachmentUpdateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<BidAttachmentUpdateResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.BidAttachment.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
