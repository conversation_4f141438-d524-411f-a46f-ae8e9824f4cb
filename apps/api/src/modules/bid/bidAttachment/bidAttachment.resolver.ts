import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { BidAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/bidAttachment.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { createFileBaseResolver } from '~/utils/providers/fileBase.resolver';

import { BidAttachmentService } from '../bidAttachment/bidAttachment.service';
import { BulkCreateInput, CreateInput, UpdateInput } from './bidAttachment.input';
import { PaginatedObjects, SearchArgs, TBidAttachment } from './bidAttachment.type';

const FileBaseResolver = createFileBaseResolver(TBidAttachment);

@Service()
@Resolver(() => TBidAttachment)
export class BidAttachmentResolver extends FileBaseResolver {
    @Inject()
    private service: BidAttachmentService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedBidAttachments' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; bidAttachments: BidAttachment[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, bidAttachments: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TBidAttachment], { name: 'bidAttachments' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<BidAttachment[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TBidAttachment, { name: 'bidAttachment' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<BidAttachment> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBidAttachment, { name: 'createBidAttachment' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<BidAttachment> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => [TBidAttachment], { name: 'bulkCreateBidAttachment' })
    async bulkCreate(
        @Ctx() ctx: Context,
        @Arg('input') input: BulkCreateInput
    ): Promise<BidAttachment[]> {
        const rows = await this.service.bulkCreateByGrpc(input, { ctx });
        return rows;
    }

    @UserAuthInterceptor()
    @Mutation(() => TBidAttachment, { name: 'updateBidAttachment' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<BidAttachment> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteBidAttachment' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: BidAttachment): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: BidAttachment): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }
}
