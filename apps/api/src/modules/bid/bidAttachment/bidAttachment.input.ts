import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('BidAttachmentInput')
export class CommonInput {
    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    memo?: string;

    deleted?: boolean;
}

@InputType('FileInput')
export class FileInput extends CommonInput {
    @Field(() => GraphQLUpload)
    file: Promise<FileUpload>;
}

@InputType('BidAttachmentCreateInput')
export class CreateInput extends CommonInput {
    @Field(() => ID)
    bidId: number;

    @Field(() => GraphQLUpload)
    file: Promise<FileUpload>;
}

@InputType('BidAttachmentBulkCreateInput')
export class BulkCreateInput {
    @Field(() => ID)
    bidId: number;

    @Field(() => [FileInput])
    attachments: FileInput[];
}

@InputType('BidAttachmentUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
