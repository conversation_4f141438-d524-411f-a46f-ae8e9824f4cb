import type { BidAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/bidAttachment.model';
import { IBidAttachment } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/bidAttachment.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('BidAttachment', { implements: IBidAttachment })
export class TBidAttachment extends IBidAttachment {}

@ObjectType('PaginatedBidAttachments')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TBidAttachment], { nullable: true })
    bidAttachments: BidAttachment[];
}

@InputType('BidAttachmentFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    bidId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
