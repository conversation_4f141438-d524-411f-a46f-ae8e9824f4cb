import {
    EnumEyeProductStatus,
    PublicEyeProduct,
} from '@clinico/mikro-orm-persistence/models/public/eyeProduct.model';
import {
    EnumEyeProductStatus as IEnumEyeProductStatus,
    IEyeProduct,
} from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeProduct.model';
import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeProduct', { implements: IEyeProduct })
export class TEyeProduct extends IEyeProduct { }

@ObjectType('PaginatedEyeProducts')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TEyeProduct], { nullable: true })
    eyeProducts: PublicEyeProduct[];
}

@InputType('EyeProductFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true, description: '區域' })
    regionId?: number;

    @Field({ nullable: true, description: '名稱' })
    name?: string;

    @Field({ nullable: true, description: '廠牌' })
    brand?: string;

    @Field({ nullable: true, description: '型號' })
    model?: string;

    @Field(() => ID, { nullable: true, description: '眼科商品類型' })
    eyeProductTypeId?: number;

    @Field(() => Int, { nullable: true, description: '販售金額（起）' })
    salePrice1?: number;

    @Field(() => Int, { nullable: true, description: '販售金額（迄）' })
    salePrice2?: number;

    @Field(() => ID, { nullable: true, description: '貨幣' })
    currencyId?: number;

    @Field(() => [IEnumEyeProductStatus], { nullable: true, description: '狀態' })
    statuses?: EnumEyeProductStatus[];

    @Field({ nullable: true, description: '今日有效（可選擇）的商品' })
    isValidToday?: boolean;

    @Field(() => ID, { nullable: true, description: '眼科促銷活動' })
    eyePromotionId?: number;

    @Field(() => [ID], { nullable: true, description: '眼科促銷活動' })
    eyePromotionIds?: number[];

    @Field(() => ID, { nullable: true, description: '眼科商品群組' })
    eyeProductGroupId?: number;

    @Field(() => [ID], { nullable: true, description: '产品线' })
    productLineIds?: number[];
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
