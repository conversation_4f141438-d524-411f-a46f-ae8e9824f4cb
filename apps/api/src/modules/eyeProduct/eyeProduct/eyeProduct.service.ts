import { Helpers } from '@clinico/clinico-node-framework';
import {
    EnumEyeProductStatus,
    PublicEyeProduct,
} from '@clinico/mikro-orm-persistence/models/public/eyeProduct.model';
import { FilterQuery } from '@mikro-orm/core';
import BigNumber from 'bignumber.js';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './eyeProduct.type';

@Service()
export class EyeProductService extends BaseService<PublicEyeProduct> {
    protected entity = PublicEyeProduct;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: PublicEyeProduct[] }> {
        const em = this.em.fork();

        const where: FilterQuery<PublicEyeProduct>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        if (params?.salePrice1) {
            where.push({ salePrice: { $gte: BigNumber(params.salePrice1).toString() } });
        }
        if (params?.salePrice2) {
            where.push({ salePrice: { $lte: BigNumber(params.salePrice2).toString() } });
        }
        if (params?.statuses) {
            where.push({ status: { $in: params.statuses } });
        }
        if (params?.name) {
            where.push({ name: { $ilike: `%${params.name}%` } });
        }
        if (params?.isValidToday) {
            const today = Helpers.Date.formatDate(Helpers.Date.now());
            where.push({
                status: EnumEyeProductStatus.Enabled,
                $and: [
                    { $or: [{ date1: null }, { date1: { $gte: today } }] },
                    { $or: [{ date2: null }, { date2: { $lte: today } }] },
                ],
            });
        }
        if (params?.eyePromotionId) {
            where.push({
                eyePromotionProductMappings: {
                    eyePromotion: {
                        id: params.eyePromotionId,
                        deleted: false,
                    },
                },
            });
        }
        if (params?.eyePromotionIds) {
            where.push({
                eyePromotionProductMappings: {
                    eyePromotion: {
                        id: { $in: params.eyePromotionIds },
                        deleted: false,
                    },
                },
            });
        }

        if (params?.productLineIds && params.productLineIds.length) {
            where.push({
                eyeProductsToProductLines: {
                    productLine: {
                        id: { $in: params.productLineIds },
                        deleted: false,
                    },
                },
            });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['eyeProductType', 'currency', 'createdUser', 'updatedUser'],
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }
}
