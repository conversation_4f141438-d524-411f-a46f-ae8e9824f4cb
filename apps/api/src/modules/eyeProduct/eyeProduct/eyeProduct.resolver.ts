import type { PublicCompany } from '@clinico/mikro-orm-persistence/models/public/company.model';
import type { PublicCurrency } from '@clinico/mikro-orm-persistence/models/public/currency.model';
import type { PublicEyeProduct } from '@clinico/mikro-orm-persistence/models/public/eyeProduct.model';
import { PublicEyeProductAttachment } from '@clinico/mikro-orm-persistence/models/public/eyeProductAttachment.model';
import { PublicEyeProductGroup } from '@clinico/mikro-orm-persistence/models/public/eyeProductGroup.model';
import type { PublicEyeProductItem } from '@clinico/mikro-orm-persistence/models/public/eyeProductItem.model';
import type { PublicEyeProductType } from '@clinico/mikro-orm-persistence/models/public/eyeProductType.model';
import type { PublicEyePromotion } from '@clinico/mikro-orm-persistence/models/public/eyePromotion.model';
import type { PublicRegion } from '@clinico/mikro-orm-persistence/models/public/region.model';
import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import { ProductLine } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/productLine.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { EyePromotionService } from '~/modules/eyePromotion/eyePromotion/eyePromotion.service';
import { TEyePromotion } from '~/modules/eyePromotion/eyePromotion/eyePromotion.type';
import { ProductLineService } from '~/modules/productLine/productLine.service';
import { TProductLine } from '~/modules/productLine/productLine.type';
import { CompanyService } from '~/modules/public/company/company.service';
import { TCompany } from '~/modules/public/company/company.type';
import { CurrencyService } from '~/modules/public/currency/currency.service';
import { TCurrency } from '~/modules/public/currency/currency.type';
import { RegionService } from '~/modules/public/region/region.service';
import { TRegion } from '~/modules/public/region/region.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { EyeProductAttachmentService } from '../eyeProductAttachment/eyeProductAttachment.service';
import { TEyeProductAttachment } from '../eyeProductAttachment/eyeProductAttachment.type';
import { EyeProductGroupService } from '../eyeProductGroup/eyeProductGroup.service';
import { TEyeProductGroup } from '../eyeProductGroup/eyeProductGroup.type';
import { EyeProductItemService } from '../eyeProductItem/eyeProductItem.service';
import { TEyeProductItem } from '../eyeProductItem/eyeProductItem.type';
import { EyeProductTypeService } from '../eyeProductType/eyeProductType.service';
import { TEyeProductType } from '../eyeProductType/eyeProductType.type';
import { EyeProductService } from './eyeProduct.service';
import { PaginatedObjects, SearchArgs, TEyeProduct } from './eyeProduct.type';

@Service()
@Resolver(() => TEyeProduct)
export class EyeProductResolver {
    @Inject()
    private service: EyeProductService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeProducts' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeProducts: PublicEyeProduct[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeProducts: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeProduct], { name: 'eyeProducts' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeProduct[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeProduct, { name: 'eyeProduct' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeProduct> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private currencyService: CurrencyService;
    @FieldResolver(() => TCurrency, { nullable: true })
    async currency(@Root() model: PublicEyeProduct): Promise<PublicCurrency | undefined> {
        return await this.currencyService.findOne({ id: model.currencyId });
    }
    @FieldResolver(() => TCurrency, { nullable: true })
    async singleCurrency(@Root() model: PublicEyeProduct): Promise<PublicCurrency | undefined> {
        return await this.currencyService.findOne({ id: model.singleCurrencyId });
    }

    @Inject()
    private eyeProductTypeService: EyeProductTypeService;
    @FieldResolver(() => TEyeProductType, { nullable: true })
    async eyeProductType(
        @Root() model: PublicEyeProduct
    ): Promise<PublicEyeProductType | undefined> {
        return await this.eyeProductTypeService.findOne({ id: model.eyeProductTypeId });
    }

    @Inject()
    private regionService: RegionService;
    @FieldResolver(() => TRegion)
    async region(@Root() model: PublicEyeProduct): Promise<PublicRegion | undefined> {
        return await this.regionService.findOne({ id: model.regionId });
    }

    @Inject()
    private companyService: CompanyService;
    @FieldResolver(() => TCompany)
    async company(@Root() model: PublicEyeProduct): Promise<PublicCompany | undefined> {
        return await this.companyService.findOne({ id: model.companyId });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { nullable: true })
    async createdUser(@Root() model: PublicEyeProduct): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser, { nullable: true })
    async updatedUser(@Root() model: PublicEyeProduct): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }

    @Inject()
    private eyeProductItemService: EyeProductItemService;
    @FieldResolver(() => [TEyeProductItem])
    async eyeProductItems(@Root() model: PublicEyeProduct): Promise<PublicEyeProductItem[]> {
        const { rows } = await this.eyeProductItemService.search({ eyeProductId: model.id });
        return rows;
    }

    @Inject()
    private eyePromotionService: EyePromotionService;
    @FieldResolver(() => [TEyePromotion], { description: '眼科商品' })
    async eyePromotions(@Root() model: PublicEyeProduct): Promise<PublicEyePromotion[]> {
        if (!model.eyePromotionProductMappings.isInitialized()) {
            await model.eyePromotionProductMappings.init();
        }
        const items = model.eyePromotionProductMappings.getItems();
        const { rows } = await this.eyePromotionService.search({
            eyeProductIds: items.map((item) => item.eyeProductId),
        });
        return rows;
    }

    @Inject()
    private eyeProductGroupService: EyeProductGroupService;
    @FieldResolver(() => TEyeProductGroup, { nullable: true })
    async eyeProductGroup(
        @Root() model: PublicEyeProduct
    ): Promise<PublicEyeProductGroup | undefined> {
        return await this.eyeProductGroupService.findOne({ id: model.eyeProductGroupId });
    }

    @Inject()
    private eyeProductAttachmentService: EyeProductAttachmentService;
    @FieldResolver(() => [TEyeProductAttachment])
    async eyeProductAttachments(
        @Root() model: PublicEyeProduct
    ): Promise<PublicEyeProductAttachment[]> {
        const { rows } = await this.eyeProductAttachmentService.search({ eyeProductId: model.id });
        return rows;
    }

    @FieldResolver(() => [TProductLine], { nullable: true, description: '产品线' })
    async productLine(@Root() model: PublicEyeProduct): Promise<ProductLine[]> {
        if (!model.eyeProductsToProductLines.isInitialized()) {
            await model.eyeProductsToProductLines.init({
                populate: ['productLine'],
                where: { productLine: { deleted: false } },
            });
        }
        return model.eyeProductsToProductLines.getItems().map((v) => v.productLine);
    }
}
