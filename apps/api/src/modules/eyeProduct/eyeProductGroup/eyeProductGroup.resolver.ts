import { PublicCompany } from '@clinico/mikro-orm-persistence/models/public/company.model';
import { PublicEyeProductGroup } from '@clinico/mikro-orm-persistence/models/public/eyeProductGroup.model';
import { PublicRegion } from '@clinico/mikro-orm-persistence/models/public/region.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { CompanyService } from '~/modules/public/company/company.service';
import { TCompany } from '~/modules/public/company/company.type';
import { RegionService } from '~/modules/public/region/region.service';
import { TRegion } from '~/modules/public/region/region.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { EyeProductGroupService } from './eyeProductGroup.service';
import { SearchArgs, TEyeProductGroup } from './eyeProductGroup.type';

@Service()
@Resolver((of) => TEyeProductGroup)
export class EyeProductGroupResolver {
    @Inject()
    private eyeProductGroupService: EyeProductGroupService;
    @Inject()
    private regionService: RegionService;
    @Inject()
    private companyService: CompanyService;

    @UserAuthInterceptor()
    @Query(() => [TEyeProductGroup], { name: 'eyeProductGroups' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeProductGroup[]> {
        const options = { pagination, ctx };
        const { rows } = await this.eyeProductGroupService.search(filters, options);
        return rows;
    }

    @FieldResolver(() => TRegion)
    async region(@Root() model: PublicEyeProductGroup): Promise<PublicRegion | undefined> {
        return await this.regionService.findOne({ id: model.regionId });
    }

    @FieldResolver(() => TCompany)
    async company(@Root() model: PublicEyeProductGroup): Promise<PublicCompany | undefined> {
        return await this.companyService.findOne({ id: model.companyId });
    }
}
