import { PublicEyeProductGroup } from '@clinico/mikro-orm-persistence/models/public/eyeProductGroup.model';
import { IEyeProductGroup } from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeProductGroup.model';
import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeProductGroup', { implements: IEyeProductGroup })
export class TEyeProductGroup extends IEyeProductGroup {}

@InputType('EyeProductGroupFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true, description: '區域' })
    regionId?: number;

    @Field({ nullable: true, description: '名稱' })
    name?: string;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
