import { PublicEyeProductGroup } from '@clinico/mikro-orm-persistence/models/public/eyeProductGroup.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './eyeProductGroup.type';

@Service()
export class EyeProductGroupService extends BaseService<PublicEyeProductGroup> {
    protected entity = PublicEyeProductGroup;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: PublicEyeProductGroup[] }> {
        const em = this.em.fork();

        const where: FilterQuery<PublicEyeProductGroup>[] = new FilterBuilder({
            em,
            ent: this.entity,
            filters: params,
        }).build();

        if (params?.name) {
            where.push({ name: { $ilike: `%${params.name}%` } });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ viewOrder: 'ASC' }],
            }
        );

        return { rows, count };
    }
}
