import type { PublicEyeProductAttachment } from '@clinico/mikro-orm-persistence/models/public/eyeProductAttachment.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { EyeProductAttachmentService } from './eyeProductAttachment.service';
import { PaginatedObjects, SearchArgs, TEyeProductAttachment } from './eyeProductAttachment.type';
import { createFileBaseResolver } from '~/utils/providers/fileBase.resolver';

const FileBaseResolver = createFileBaseResolver(TEyeProductAttachment);

@Service()
@Resolver(() => TEyeProductAttachment)
export class EyeProductAttachmentResolver extends FileBaseResolver {
    @Inject()
    private service: EyeProductAttachmentService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeProductAttachments' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeProducts: PublicEyeProductAttachment[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeProducts: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeProductAttachment], { name: 'eyeProductAttachments' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeProductAttachment[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeProductAttachment, { name: 'eyeProductAttachment' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeProductAttachment> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }


}
