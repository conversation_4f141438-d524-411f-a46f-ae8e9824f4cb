import {
    PublicEyeProductAttachment,
} from '@clinico/mikro-orm-persistence/models/public/eyeProductAttachment.model';
import {
    IEyeProductAttachment,
} from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeProductAttachment.model';
import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeProductAttachment', { implements: IEyeProductAttachment })
export class TEyeProductAttachment extends IEyeProductAttachment {}

@ObjectType('PaginatedEyeProductAttachments')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TEyeProductAttachment], { nullable: true })
    eyeProductAttachments: PublicEyeProductAttachment[];
}

@InputType('EyeProductAttachmentFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true, description: '眼科商品' })
    eyeProductId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
