import {
    PublicEyeProductAttachment,
} from '@clinico/mikro-orm-persistence/models/public/eyeProductAttachment.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './eyeProductAttachment.type';

@Service()
export class EyeProductAttachmentService extends BaseService<PublicEyeProductAttachment> {
    protected entity = PublicEyeProductAttachment;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: PublicEyeProductAttachment[] }> {
        const em = this.em.fork();

        const where: FilterQuery<PublicEyeProductAttachment>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        if (params?.eyeProductId) {
            where.push({ eyeProduct: { id: params?.eyeProductId } });
        }
        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['eyeProduct'],
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }
}
