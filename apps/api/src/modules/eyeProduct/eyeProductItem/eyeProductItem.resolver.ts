import type { InventoryMaterial } from '@clinico/mikro-orm-persistence/models/inventory/material.model';
import type { PublicEyeProduct } from '@clinico/mikro-orm-persistence/models/public/eyeProduct.model';
import type { PublicEyeProductItem } from '@clinico/mikro-orm-persistence/models/public/eyeProductItem.model';
import type { PublicEyeProductItemType } from '@clinico/mikro-orm-persistence/models/public/eyeProductItemType.model';
import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { MaterialService } from '~/modules/material/material/material.service';
import { TMaterial } from '~/modules/material/material/material.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { EyeProductService } from '../eyeProduct/eyeProduct.service';
import { TEyeProduct } from '../eyeProduct/eyeProduct.type';
import { EyeProductItemTypeService } from '../eyeProductItemType/eyeProductItemType.service';
import { TEyeProductItemType } from '../eyeProductItemType/eyeProductItemType.type';
import { EyeProductItemService } from './eyeProductItem.service';
import { PaginatedObjects, SearchArgs, TEyeProductItem } from './eyeProductItem.type';

@Service()
@Resolver(() => TEyeProductItem)
export class EyeProductItemResolver {
    @Inject()
    private service: EyeProductItemService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeProductItems' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeProductItems: PublicEyeProductItem[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeProductItems: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeProductItem], { name: 'eyeProductItems' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeProductItem[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeProductItem, { name: 'eyeProductItem' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeProductItem> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private materialService: MaterialService;
    @FieldResolver(() => TMaterial)
    async material(@Root() model: PublicEyeProductItem): Promise<InventoryMaterial | undefined> {
        return await this.materialService.findOne({ id: model.material?.id });
    }

    @Inject()
    private eyeProductService: EyeProductService;
    @FieldResolver(() => TEyeProduct)
    async eyeProduct(@Root() model: PublicEyeProductItem): Promise<PublicEyeProduct | undefined> {
        return await this.eyeProductService.findOne({ id: model.eyeProductId });
    }

    @Inject()
    private eyeProductItemTypeService: EyeProductItemTypeService;
    @FieldResolver(() => TEyeProductItemType)
    async eyeProductItemType(
        @Root() model: PublicEyeProductItem
    ): Promise<PublicEyeProductItemType | undefined> {
        return await this.eyeProductItemTypeService.findOne({ id: model.eyeProductItemTypeId });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { nullable: true })
    async createdUser(@Root() model: PublicEyeProductItem): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser, { nullable: true })
    async updatedUser(@Root() model: PublicEyeProductItem): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }
}
