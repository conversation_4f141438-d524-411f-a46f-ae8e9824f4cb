import type { PublicEyeProductItem } from '@clinico/mikro-orm-persistence/models/public/eyeProductItem.model';
import { IEyeProductItem } from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeProductItem.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeProductItem', { implements: IEyeProductItem })
export class TEyeProductItem extends IEyeProductItem {}

@ObjectType('PaginatedEyeProductItems')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TEyeProductItem], { nullable: true })
    eyeProductItems: PublicEyeProductItem[];
}

@InputType('EyeProductItemFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    eyeProductId?: number;

    @Field(() => ID, { nullable: true })
    eyeProductItemTypeId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
