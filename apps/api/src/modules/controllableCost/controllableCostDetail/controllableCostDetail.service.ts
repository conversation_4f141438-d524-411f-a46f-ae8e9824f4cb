import { Inject, Service } from 'typedi';
import { CommonYonyouService } from '~/utils/providers/commonYonyou.service';
import { FilterInput, IControllableCostDetail } from './controllableCostDetail.type';
import { SearchOptions } from './controllableCostDetail.service.type';
import { DI } from '~/container';


@Service()
export class ControllableCostDetailService extends CommonYonyouService<IControllableCostDetail> {
    get commonDataNotFoundMessage(): string {
        return 'ControllableCostDetail not found';
    }

    async search(
        params?: FilterInput,
        options?: SearchOptions
    ): Promise<{ count: number; rows: IControllableCostDetail[] }> {
        let filters = '';

        if (params?.year) {
            filters += ` AND YEAR(a.[accounting_date]) = ${params.year} `;
        }
        if (params?.months) {
            filters += ` AND MONTH(a.[accounting_date]) IN (${params.months.join(
                ',',
            )}) `;
        }
        if (params?.deptCode) {
            filters += ` AND a.[dept_code] = '${params.deptCode}' `;
        }
        if (params?.statisticalSubject) {
            filters += ` AND b.[statist_name] = '${params.statisticalSubject}' `;
        }

         // For sort
        const sort: string[] = [];
        if (params?.postingDate) {
            sort.push(` a.[accounting_date] ${params.postingDate} `);
        }
        if (params?.amount) {
            sort.push(` a.amount ${params.amount} `);
        }

        const orderBy = {
            sort: sort.length
                ? `${sort.join(' , ')}`
                : 'a.[accounting_date] ASC',
        };

        const sql = `
           SELECT ROW_NUMBER() OVER (ORDER BY ${orderBy.sort}) AS row_number
                 ,a.[account_code] AS account
			     ,a.[voucher_number] AS documentCode
			     ,b.[statist_name] AS statisticalSubject
			     ,a.[accounting_code] AS subjectCode
			     ,a.[accounting_name] AS subject
			     ,a.[dept_code] AS deptCode
			     ,a.[dept_name] AS deptName
			     ,a.[accounting_date] AS postingDate
			     ,a.[user_code] AS personCode 
			     ,a.[user_name] AS personName
			     ,a.[amount] AS amount
			     ,a.[explain] AS describe
            FROM [CLINICO_CHINA].[dbo].[v_controllable_expense] A
            LEFT JOIN [CLINICO_CHINA].[dbo].[t_statist_account] B on a.accounting_code = b.accounting_code
            WHERE 1 = 1
            ${filters}
        `;

        const rowsSql = this.limitSQL(sql, options?.pagination?.offset, options?.pagination?.limit);
        const countSql = this.countSQL(sql);
        if (!DI.yonyouDB) return { count: 0, rows: [] };
        const [rows, countResult] = await Promise.all([
            <IControllableCostDetail[]>(
                (<unknown>DI.yonyouDB.query(rowsSql))
            ),
            DI.yonyouDB.query(countSql),
        ]);
        const count = countResult[0]['count'];

        return {
            rows: rows,
            count: count,
        };
    }
}
