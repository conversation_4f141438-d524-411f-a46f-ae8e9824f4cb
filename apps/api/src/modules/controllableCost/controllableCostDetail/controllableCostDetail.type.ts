import { DateResolver } from 'graphql-scalars';
import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    Int,
    InterfaceType,
    ObjectType,
} from 'type-graphql';
import { EnumSortDirection } from '~/utils/types/base.enum';
import { PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@InterfaceType()
export class IControllableCostDetail {
    @Field({ nullable: true, description: '账套' })
    account?: string;

    @Field({ nullable: true, description: '单据号' })
    documentCode?: string;

    @Field({ nullable: true, description: '统计科目' })
    statisticalSubject?: string;

    @Field({ nullable: true, description: '会科编号' })
    subjectCode?: string;

    @Field({ nullable: true, description: '会科' })
    subject?: string;

    @Field({ nullable: true, description: '部门编号' })
    deptCode?: string;

    @Field({ nullable: true, description: '部门' })
    deptName?: string;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '过帐日期',
    })
    postingDate?: Date;

    @Field({ nullable: true, description: '员编' })
    personCode?: string;

    @Field({ nullable: true, description: '员工姓名' })
    personName?: string;

    @Field((type) => Float, { nullable: true, description: '金额' })
    amount?: number;

    @Field({ nullable: true, description: '说明' })
    describe?: string;
}

@ObjectType('ControllableCostDetail', { implements: IControllableCostDetail })
export class TControllableCostDetail extends IControllableCostDetail {}

@ObjectType('PaginatedControllableCostDetail')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TControllableCostDetail], { nullable: true })
    controllableCostDetails: TControllableCostDetail[];
}
@InputType('ControllableCostDetailFilterInput')
export class FilterInput {
    @Field((type) => Int, { nullable: true, description: '年份' })
    year?: number;

    @Field((type) => [Int], { nullable: true, description: '月份' })
    months?: number[];

    @Field({ nullable: true, description: '部门编码' })
    deptCode?: string;

    @Field({ nullable: true, description: '统计科目' })
    statisticalSubject?: string;

    @Field((type) => EnumSortDirection, {
        nullable: true,
        description: '日期排序',
    })
    postingDate?: EnumSortDirection;

    @Field((type) => EnumSortDirection, {
        nullable: true,
        description: '金额排序',
    })
    amount?: EnumSortDirection;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
