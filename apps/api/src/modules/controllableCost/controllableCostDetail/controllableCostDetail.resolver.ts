import { Arg, Args, <PERSON>tx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { VOrderDetail } from '@packages/nav-orm/models/vOrderDetail.model';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { PaginatedObjects, SearchArgs, TControllableCostDetail, IControllableCostDetail } from './controllableCostDetail.type';
import { ControllableCostDetailService } from './controllableCostDetail.service';

@Service()
@Resolver(() => TControllableCostDetail)
export class ControllableCostDetailResolver {
    @Inject()
    service: ControllableCostDetailService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedControllableCostDetails' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; controllableCostDetails: IControllableCostDetail[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, controllableCostDetails: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TControllableCostDetail], { name: 'controllableCostDetails' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<IControllableCostDetail[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }
}
