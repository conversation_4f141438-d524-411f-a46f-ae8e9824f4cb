import { DateResolver } from 'graphql-scalars';
import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    Int,
    InterfaceType,
    ObjectType,
} from 'type-graphql';
import { PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('ControllableCostByMonth')
export class ControllableCostByMonth {
    @Field({ nullable: true, description: '部门编码' })
    deptCode?: string;

    @Field({ nullable: true, description: '部门名称' })
    deptName?: string;

    @Field({ nullable: true, description: '部门负责人编码' })
    deptPersonCode?: string;

    @Field({ nullable: true, description: '部门负责人名称' })
    deptPersonName?: string;

    @Field((type) => Float, { nullable: true, description: '总实际金额' })
    totalActualAmount?: string;

    @Field((type) => Float, { nullable: true, description: '总预计金额' })
    totalBudgetAmount?: string;

    @Field((type) => Float, { nullable: true, description: '总达成率' })
    totalAchievementRate?: string | null;

    @Field((type) => Float, { nullable: true, description: '总累计实际金额' })
    totalAccumulatedActualAmount?: string;

    @Field((type) => Float, { nullable: true, description: '总累计预计金额' })
    totalAccumulatedBudgetAmount?: string;

    @Field((type) => Float, { nullable: true, description: '总累计达成率' })
    totalAccumulatedAchievementRate?: string | null;

    @Field((type) => [ControllableCostByMonthItem], { description: '金额数据' })
    items?: ControllableCostByMonthItem[];

}
@ObjectType('ControllableCostByMonthItem')
export class ControllableCostByMonthItem {
    @Field({ nullable: true, description: '成本类型' })
    costType?: string;

    @Field((type) => Float, { nullable: true, description: '实际金额' })
    actualAmount?: number;

    @Field((type) => Float, { nullable: true, description: '预算金额' })
    budgetAmount?: number;

    @Field((type) => Float, { nullable: true, description: '达成率' })
    achievementRate?: string | null;

    @Field((type) => Float, { nullable: true, description: '累计实际金额' })
    accumulatedActualAmount?: number;

    @Field((type) => Float, { nullable: true, description: '累计预算金额' })
    accumulatedBudgetAmount?: number;

    @Field((type) => Float, { nullable: true, description: '累计达成率' })
    accumulatedAchievementRate?: string | null;
}

@InputType('ControllableCostByMonthFilterInput')
export class FilterInput {
    @Field((type) => Int, { nullable: true, description: '年份' })
    year?: number;

    @Field((type) => Int, { nullable: true, description: '月份' })
    month?: number;

    @Field((type) => [String], { nullable: true, description: '部门编码' })
    deptCodes?: string[];

    @Field((type) => [String], { nullable: true, description: '部门编码' })
    departmentCodes?: string[];
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
