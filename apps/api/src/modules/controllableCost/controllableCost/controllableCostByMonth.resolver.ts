import { Inject, Service } from 'typedi';
import { <PERSON>rg, <PERSON>rg<PERSON>, FieldResolver, Query, <PERSON>solver, Root } from 'type-graphql';
import { ControllableCostByMonth } from './controllableCostByMonth.type';
import { CostCenterService } from '~/modules/public/costCenter/costCenter.service';
import { TCostCenter } from '~/modules/public/costCenter/costCenter.type';
import { PublicCostCenter } from '@clinico/mikro-orm-persistence/models/public/costCenter.model';

@Service()
@Resolver((of) => ControllableCostByMonth)
export class ControllableCostByMonthResolver {
    @Inject()
    private costCenterService: CostCenterService;

    @FieldResolver((of) => TCostCenter)
    async costCenter(@Root() controllableCostByMonth: ControllableCostByMonth): Promise<PublicCostCenter | undefined> {
        const { rows } = await this.costCenterService.search({
            code: controllableCostByMonth.deptCode,
        });
        return rows[0];

    }


}