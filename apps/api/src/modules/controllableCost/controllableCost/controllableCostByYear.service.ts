import _ from 'lodash';
import { Inject, Service } from 'typedi';
import { ControllableCostByYear, FilterInput } from './controllableCostByYear.type';
import { DI } from '~/container';
import { ControllableCostByYearItem } from '../controllableCostByYearItem/controllableCostByYearItem.type';
import { ControllableCostByYearItemService } from '../controllableCostByYearItem/controllableCostByYearItem.service';
import { toPercentOrNull } from '@packages/utils/number.util';

@Service()
export class ControllableCostByYearService {
    @Inject()
    private controllableCostByYearItemService: ControllableCostByYearItemService;

    async getControllableCostByYear(
        params?: FilterInput,
    ): Promise<ControllableCostByYear[]> {
        const year = params?.year ?? new Date().getFullYear();
        let filters = '';
        if (params?.deptCodes && params.deptCodes.length > 0) {
            filters += ` AND d.code IN ('${params.deptCodes.join("','")}') `;
        }
        if (params?.departmentCodes && params.departmentCodes.length > 0) {
            filters += ` AND d.code IN ('${params.departmentCodes.join(
                "','",
            )}') `;
        }
        let yonyouItems: any[] = [];
        if (DI.yonyouDB) {
            const qr = DI.yonyouDB.createQueryRunner();
            const yonyouSql = this.buildRawSql({ year, filters });
            yonyouItems = await qr.query(yonyouSql);
            await qr.release();
        }
        const controllableCostByYearItems =
            await this.getControllableCostByYearItem(year);
        const results: ControllableCostByYear[] = this.transform(yonyouItems, controllableCostByYearItems);
        return results;
    }

    private transform(
        rows: any[],
        controllableCostByYearItems: ControllableCostByYearItem[],
    ): ControllableCostByYear[] {
        type Result = ControllableCostByYear;
        const results: Result[] = [];

        rows.forEach((row) => {
            const detail: Result = {
                deptCode: row.deptCode,
                deptName: row.deptName,
                year: row.year,
                month1: row.month1,
                month2: row.month2,
                month3: row.month3,
                month4: row.month4,
                month5: row.month5,
                month6: row.month6,
                month7: row.month7,
                month8: row.month8,
                month9: row.month9,
                month10: row.month10,
                month11: row.month11,
                month12: row.month12,
                total: row.total,
                totalBudget: row.total_budget,
                achievementRate: toPercentOrNull({
                    dividend: row.total,
                    divisor: row.total_budget,
                }),
                items: _.filter(controllableCostByYearItems, {
                    deptCode: row.deptCode,
                    year: row.year,
                }),
            };
            results.push(detail);
        });
        return results;
    }

    async getControllableCostByYearItem(
        year: number,
    ): Promise<ControllableCostByYearItem[]> {
        return await this.controllableCostByYearItemService.getControllableCostByYearItem(
            {
                year: year,
            },
        );
    }

    private buildRawSql(params: { year: number; filters: string }): string {
        const { year, filters } = params;
        // prettier-ignore
        const sql = `
            WITH w_controllable_expense_object AS (
		        SELECT iyear
					  ,dept_code
					  ,dept_name
					  ,SUM(Total) AS total_budget	
		        FROM [CLINICO_CHINA].[dbo].[t_controllable_expense_object]
		        WHERE iyear = ${year}  
		        GROUP BY dept_code,dept_name,iyear		
            )       
            SELECT D.[code] AS deptCode
				  ,D.[name] AS deptName
                  ,B.iyear AS year
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 1 THEN A.[amount] ELSE 0 END) AS month1
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 2 THEN A.[amount] ELSE 0 END) AS month2
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 3 THEN A.[amount] ELSE 0 END) AS month3
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 4 THEN A.[amount] ELSE 0 END) AS month4
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 5 THEN A.[amount] ELSE 0 END) AS month5
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 6 THEN A.[amount] ELSE 0 END) AS month6
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 7 THEN A.[amount] ELSE 0 END) AS month7
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 8 THEN A.[amount] ELSE 0 END) AS month8
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 9 THEN A.[amount] ELSE 0 END) AS month9
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 10 THEN A.[amount] ELSE 0 END) AS month10
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 11 THEN A.[amount] ELSE 0 END) AS month11
				  ,SUM(CASE WHEN MONTH(A.[accounting_date]) = 12 THEN A.[amount] ELSE 0 END) AS month12
				  ,SUM([amount]) AS total
                  ,B.total_budget AS total_budget
            FROM [CLINICO_CHINA].[dbo].[v_depts] D
            LEFT outer JOIN [CLINICO_CHINA].[dbo].[v_controllable_expense] A ON D.code =A.dept_code AND YEAR(A.[accounting_date]) = ${year}
            LEFT JOIN w_controllable_expense_object B ON D.code = B.dept_code AND B.[iyear] = ${year}
            WHERE LEN(D.level_code) >2
            AND D.name is not null
            AND D.code IN(
							SELECT DISTINCT dept_code FROM [CLINICO_CHINA].[dbo].[t_controllable_expense_object]
							WHERE [iyear] = ${year}
						)
            ${filters}
            GROUP BY D.[code], D.[name], B.dept_code,B.dept_name, B.total_budget, B.iyear
            HAVING SUM(CASE WHEN MONTH(A.[accounting_date]) = 1 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 2 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 3 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 4 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 5 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 6 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 7 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 8 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 9 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 10 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 11 THEN A.[amount] ELSE 0 END) > 0
                OR SUM(CASE WHEN MONTH(A.[accounting_date]) = 12 THEN A.[amount] ELSE 0 END) > 0
            ORDER BY D.[code] ASC                      
            `;
        return sql;
    }
}
