import { DateResolver } from 'graphql-scalars';
import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    Int,
    InterfaceType,
    ObjectType,
} from 'type-graphql';
import { PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';
import { ControllableCostByYearItem } from '../controllableCostByYearItem/controllableCostByYearItem.type';

@ObjectType('ControllableCostByYear')
export class ControllableCostByYear {
    @Field({ nullable: true, description: '部门编码' })
    deptCode?: string;

    @Field({ nullable: true, description: '部门名称' })
    deptName?: string;

    @Field((type) => Int, { nullable: true, description: '年份' })
    year?: number;

    @Field((type) => Float, { nullable: true, description: '1月金额' })
    month1?: number;

    @Field((type) => Float, { nullable: true, description: '2月金额' })
    month2?: number;

    @Field((type) => Float, { nullable: true, description: '3月金额' })
    month3?: number;

    @Field((type) => Float, { nullable: true, description: '4月金额' })
    month4?: number;

    @Field((type) => Float, { nullable: true, description: '5月金额' })
    month5?: number;

    @Field((type) => Float, { nullable: true, description: '6月金额' })
    month6?: number;

    @Field((type) => Float, { nullable: true, description: '7月金额' })
    month7?: number;

    @Field((type) => Float, { nullable: true, description: '8月金额' })
    month8?: number;

    @Field((type) => Float, { nullable: true, description: '9月金额' })
    month9?: number;

    @Field((type) => Float, { nullable: true, description: '10月金额' })
    month10?: number;

    @Field((type) => Float, { nullable: true, description: '11月金额' })
    month11?: number;

    @Field((type) => Float, { nullable: true, description: '12月金额' })
    month12?: number;

    @Field((type) => Float, { nullable: true, description: '总金额' })
    total?: number;

    @Field((type) => Float, { nullable: true, description: '预算总计' })
    totalBudget?: number;

    @Field((type) => Float, { nullable: true, description: '达成率' })
    achievementRate?: string | null;

    @Field((type) => [ControllableCostByYearItem], {
        nullable: true,
        description: '详情',
    })
    items: ControllableCostByYearItem[];

}


@InputType('ControllableCostByYearFilterInput')
export class FilterInput {
    @Field((type) => Int, { nullable: true, description: '年份' })
    year?: number;

    @Field((type) => Int, { nullable: true, description: '月份' })
    month?: number;

    @Field((type) => [String], { nullable: true, description: '部门编码' })
    deptCodes?: string[];

    @Field((type) => [String], { nullable: true, description: '部门编码' })
    departmentCodes?: string[];
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
