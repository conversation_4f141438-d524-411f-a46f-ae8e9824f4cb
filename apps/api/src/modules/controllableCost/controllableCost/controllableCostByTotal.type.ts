import { DateResolver } from 'graphql-scalars';
import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    Int,
    InterfaceType,
    ObjectType,
} from 'type-graphql';
import { PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('ControllableCostByTotal')
export class ControllableCostByTotal {
    @Field({ nullable: true, description: '成本类型' })
    statistName?: string;

    @Field((type) => Float, { nullable: true, description: '实际金额' })
    actualAmount?: number;

    @Field((type) => Float, { nullable: true, description: '预算金额' })
    budgetAmount?: number;

    @Field((type) => Float, { nullable: true, description: '达成率' })
    achievementRate?: string | null;

    @Field((type) => Float, { nullable: true, description: '累计实际金额' })
    accumulatedActualAmount?: number;

    @Field((type) => Float, { nullable: true, description: '累计预算金额' })
    accumulatedBudgetAmount?: number;

    @Field((type) => Float, { nullable: true, description: '累计达成率' })
    accumulatedAchievementRate?: string | null;
}

@InputType('ControllableCostByTotalFilterInput')
export class FilterInput {
    @Field((type) => Int, { nullable: true, description: '年份' })
    year?: number;

    @Field((type) => Int, { nullable: true, description: '月份' })
    month?: number;

    @Field((type) => [String], { nullable: true, description: '部门编码' })
    deptCodes?: string[];

    @Field((type) => [String], { nullable: true, description: '部门编码' })
    departmentCodes?: string[];
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
