import _ from 'lodash';
import { Inject, Service } from 'typedi';
import { ControllableCostByMonthItem, FilterInput, ControllableCostByMonth } from './controllableCostByMonth.type';
import { DI } from '~/container';
import { toPercentOrNull } from '@packages/utils/number.util';

@Service()
export class ControllableCostByMonthService {
    async getControllableCostByMonth(
        params?: FilterInput,
    ): Promise<ControllableCostByMonth[]> {
        const year = params?.year ?? new Date().getFullYear();
        const month = params?.month ?? new Date().getMonth();
        let filters = '';
        if (params?.deptCodes && params.deptCodes.length > 0) {
            filters += ` AND d.code IN ('${params.deptCodes.join("','")}') `;
        }
        if (params?.departmentCodes && params.departmentCodes.length > 0) {
            filters += ` AND d.code IN ('${params.departmentCodes.join(
                "','",
            )}') `;
        }

        let yonyouItems: any[] = [];
        if (DI.yonyouDB) {
            const qr = DI.yonyouDB.createQueryRunner();
            const yonyouSql = this.buildRawSql({ year, month, filters });
            yonyouItems = await qr.query(yonyouSql);
            await qr.release();
        }
        const results: ControllableCostByMonth[] = this.transform(yonyouItems);
        return results;
    }

    private transform(rows: any[]): ControllableCostByMonth[] {
        const grouped = _.groupBy(rows, (item) => item.dept_code);

        const result: ControllableCostByMonth[] = [];

        _.forEach(grouped, (items, deptCode) => {
            const deptName = items[0].dept_name;
            const deptPersonCode = items[0].dept_person_code;
            const deptPersonName = items[0].dept_person_name;

            // 初始化各项总和
            let totalActualAmount = 0;
            let totalBudgetAmount = 0;
            let totalAccumulatedActualAmount = 0;
            let totalAccumulatedBudgetAmount = 0;

            const itemsData: ControllableCostByMonthItem[] = _.map(
                _.groupBy(items, (item) => item.cost_type),
                (costItems, costType) => {
                    let actualAmount = 0;
                    let budgetAmount = 0;
                    let accumulatedActualAmount = 0;
                    let accumulatedBudgetAmount = 0;

                    costItems.forEach((costItem) => {
                        if (costItem.actual_amount !== null)
                            actualAmount += costItem.actual_amount;
                        if (costItem.budget_amount !== null)
                            budgetAmount += costItem.budget_amount;
                        if (costItem.accumulated_actual_amount !== null)
                            accumulatedActualAmount +=
                                costItem.accumulated_actual_amount;
                        if (costItem.accumulated_budget_amount !== null)
                            accumulatedBudgetAmount +=
                                costItem.accumulated_budget_amount;
                    });

                    // 累加到部门总和
                    totalActualAmount += actualAmount;
                    totalBudgetAmount += budgetAmount;
                    totalAccumulatedActualAmount += accumulatedActualAmount;
                    totalAccumulatedBudgetAmount += accumulatedBudgetAmount;

                    const achievementRate = toPercentOrNull({
                        dividend: actualAmount,
                        divisor: budgetAmount,
                    });
                    const accumulatedAchievementRate =
                        toPercentOrNull({
                            dividend: accumulatedActualAmount,
                            divisor: accumulatedBudgetAmount,
                        });

                    return {
                        costType,
                        actualAmount,
                        budgetAmount,
                        achievementRate,
                        accumulatedActualAmount,
                        accumulatedBudgetAmount,
                        accumulatedAchievementRate,
                    };
                },
            );

            const totalAchievementRate = toPercentOrNull({
                dividend: totalActualAmount,
                divisor: totalBudgetAmount,
                precision: 4,
            });
            const totalAccumulatedAchievementRate = toPercentOrNull({
                dividend: totalAccumulatedActualAmount,
                divisor: totalAccumulatedBudgetAmount,
                precision: 4,
            });
            result.push({
                deptCode,
                deptName,
                deptPersonCode,
                deptPersonName,
                totalActualAmount: totalActualAmount.toFixed(2),
                totalBudgetAmount: totalBudgetAmount.toFixed(2),
                totalAchievementRate,
                totalAccumulatedActualAmount:
                    totalAccumulatedActualAmount.toFixed(2),
                totalAccumulatedBudgetAmount:
                    totalAccumulatedBudgetAmount.toFixed(2),
                totalAccumulatedAchievementRate,
                items: itemsData,
            });
        });

        return result;
    }

    private buildRawSql(params: {
        year: number;
        month: number;
        filters: string;
    }): string {
        const { year, month, filters } = params;
        // prettier-ignore
        const sql = `
            SELECT D.[code] AS dept_code
                   ,D.[name] AS dept_name
                   ,D.dep_person_code AS dept_person_code
                   ,D.dep_person_name AS dept_person_name
	               ,y.statist_name AS cost_type
	               ,f.当月金额 AS actual_amount
	               ,y.当月预算 AS budget_amount
	               ,e.累计金额 AS accumulated_actual_amount
	               ,y.当月累计 AS accumulated_budget_amount
            FROM [CLINICO_CHINA].[dbo].[v_depts] D
            left outer join 
                (SELECT [iyear]
                        ,[dept_code]
                        ,[dept_name]
                        ,[statist_name]
	                    ,CASE ${month}
		                    WHEN 1 THEN ROUND(ISNULL([Jan], 0), 0)
		                    WHEN 2 THEN ROUND(ISNULL([Feb], 0), 0)
		                    WHEN 3 THEN ROUND(ISNULL(mar, 0), 0)
		                    WHEN 4 THEN ROUND(ISNULL(apr, 0), 0)
		                    WHEN 5 THEN ROUND(ISNULL(may, 0), 0)
		                    WHEN 6 THEN ROUND(ISNULL(jun, 0), 0)
		                    WHEN 7 THEN ROUND(ISNULL(jul, 0), 0)
		                    WHEN 8 THEN ROUND(ISNULL(aug, 0), 0)
		                    WHEN 9 THEN ROUND(ISNULL(sep, 0), 0)
		                    WHEN 10 THEN ROUND(ISNULL(dec, 0), 0)
		                    WHEN 11 THEN ROUND(ISNULL(nov, 0), 0)
		                    WHEN 12 THEN ROUND(ISNULL(oct, 0), 0)
	                    END as 当月预算
                        ,CASE ${month}
	                        WHEN 1 THEN ROUND(ISNULL([Jan], 0), 0)
	                        WHEN 2 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)
	                        WHEN 3 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)
	                        WHEN 4 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)
	                        WHEN 5 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)
	                        WHEN 6 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)
	                        WHEN 7 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)
	                        WHEN 8 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)+ROUND(ISNULL(Aug, 0), 0)
	                        WHEN 9 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)+ROUND(ISNULL(Aug, 0), 0)+ROUND(ISNULL(Sep, 0), 0)
	                        WHEN 10 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)+ROUND(ISNULL(Aug, 0), 0)+ROUND(ISNULL(Sep, 0), 0)+ROUND(ISNULL(Nov, 0), 0)
	                        WHEN 11 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)+ROUND(ISNULL(Aug, 0), 0)+ROUND(ISNULL(Sep, 0), 0)+ROUND(ISNULL(Nov, 0), 0)+ROUND(ISNULL(Dec, 0), 0)
	                        WHEN 12 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)+ROUND(ISNULL(Aug, 0), 0)+ROUND(ISNULL(Sep, 0), 0)+ROUND(ISNULL(Nov, 0), 0)+ROUND(ISNULL(Dec, 0), 0)+ROUND(ISNULL(oct, 0), 0)
                        END as 当月累计
                        FROM [CLINICO_CHINA].[dbo].[t_controllable_expense_object]
	                    WHERE [iyear] = ${year}
                ) Y on D.code =Y.dept_code
                left outer join 
                    (SELECT a.[dept_code]
                            ,a.[dept_name]
	                        ,b.statist_name
                            ,sum(a.[amount]) as 累计金额 
                    FROM [CLINICO_CHINA].[dbo].[v_controllable_expense] A left outer join t_statist_account B on a.accounting_code = b.accounting_code
                    where YEAR(a.[accounting_date]) = ${year}
	                AND MONTH(a.[accounting_date]) <= ${month}
                    group by a.[dept_code],a.[dept_name],b.statist_name) E on d.code = e.dept_code and y.statist_name = e.statist_name
                left outer join 
                        (SELECT a.[dept_code]
                                ,a.[dept_name]
	                            ,b.statist_name
                                ,sum(a.[amount]) as 当月金额 
                        FROM [CLINICO_CHINA].[dbo].[v_controllable_expense] A left outer join t_statist_account B on a.accounting_code = b.accounting_code
                        where YEAR(a.[accounting_date]) = ${year}
	                    AND MONTH(a.[accounting_date]) = ${month}
                        group by a.[dept_code],a.[dept_name],b.statist_name) f on d.code = f.dept_code and y.statist_name = f.statist_name
            where y.statist_name is not null and name is not null and LEN(level_code) >2
            ${filters}
            ORDER BY D.[code] ASC
            `;
        return sql;
    }
}
