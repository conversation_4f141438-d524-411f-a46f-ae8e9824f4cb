import { Inject, Service } from 'typedi';
import { <PERSON>rg, <PERSON>rg<PERSON>, FieldResolver, Query, <PERSON>solver, Root } from 'type-graphql';
import { ControllableCostByMonthService } from './controllableCostByMonth.service';
import { ControllableCost } from './controllableCost.type';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { EnumPermissionCode } from '~/constants';
import { 
    ControllableCostByMonth, 
    SearchArgs as ControllableCostByMonthSearchArgs 
} from './controllableCostByMonth.type';
import { ControllableCostByYearService } from './controllableCostByYear.service';
import { 
    ControllableCostByYear,
    SearchArgs as ControllableCostByYearSearchArgs
} from './controllableCostByYear.type';
import { ControllableCostByTotalService } from './controllableCostByTotal.service';
import { 
    ControllableCostByTotal,
    SearchArgs as ControllableCostByTotalSearchArgs
} from './controllableCostByTotal.type';


@Service()
@Resolver((of) => ControllableCost)
export class ControllableCostResolver {
    @Inject()
    private controllableCostByMonthService: ControllableCostByMonthService;
    @Inject()
    private controllableCostByYearService: ControllableCostByYearService;
    @Inject()
    private controllableCostByTotalService: ControllableCostByTotalService;

    @UserAuthInterceptor(EnumPermissionCode['controllable_cost.read'])
    @Query(() => ControllableCost, { name: 'controllableCosts' })
    async stat(): Promise<ControllableCost> {
        return {};
    }

    @FieldResolver(() => [ControllableCostByMonth], {
        description: '可控费用根据月份统计',
    })
    async controllableCostByMonth(
         @Args() { filters, pagination }: ControllableCostByMonthSearchArgs,
    ): Promise<ControllableCostByMonth[]> {
        const result =
            await this.controllableCostByMonthService.getControllableCostByMonth(
               { ...filters },
            );
        return result;
    }

    @FieldResolver(() => [ControllableCostByYear], {
        description: '可控费用根据年份统计',
    })
    async controllableCostByYear(
         @Args() { filters, pagination }: ControllableCostByYearSearchArgs,
    ): Promise<ControllableCostByYear[]> {
        const result =
            await this.controllableCostByYearService.getControllableCostByYear(
               { ...filters },
            );
        return result;
    }

     @FieldResolver(() => [ControllableCostByTotal], {
        description: '可控费用总统计',
    })
    async controllableCostByTotal(
         @Args() { filters, pagination }: ControllableCostByTotalSearchArgs,
    ): Promise<ControllableCostByTotal[]> {
        const result =
            await this.controllableCostByTotalService.getControllableCostByTotal(
               { ...filters },
            );
        return result;
    }
}
