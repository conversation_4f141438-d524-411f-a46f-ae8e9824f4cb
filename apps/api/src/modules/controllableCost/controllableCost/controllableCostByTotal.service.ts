import _ from 'lodash';
import { Inject, Service } from 'typedi';
import { DI } from '~/container';
import { ControllableCostByTotal, FilterInput } from './controllableCostByTotal.type';
import { toPercentOrNull } from '@packages/utils/number.util';

@Service()
export class ControllableCostByTotalService {
    async getControllableCostByTotal(
        params?: FilterInput,
    ): Promise<ControllableCostByTotal[]> {
        const year = params?.year ?? new Date().getFullYear();
        const month = params?.month ?? new Date().getMonth();
        let filters = '';
        if (params?.deptCodes && params.deptCodes.length > 0) {
            filters += ` AND dept_code IN ('${params.deptCodes.join("','")}') `;
        }
        if (params?.departmentCodes && params.departmentCodes.length > 0) {
            filters += ` AND dept_code IN ('${params.departmentCodes.join(
                "','",
            )}') `;
        }

        let yonyouItems: any[] = [];
        if (DI.yonyouDB) {
            const qr = DI.yonyouDB.createQueryRunner();
            const yonyouSql = this.buildRawSql({ year, month, filters });
            yonyouItems = await qr.query(yonyouSql);
            await qr.release();
        }
        const results: ControllableCostByTotal[] = this.transform(yonyouItems);
        return results;
    }

    private transform(rows: any[]): ControllableCostByTotal[] {
        type Result = ControllableCostByTotal;
        const results: Result[] = [];
        rows.forEach((row) => {
            const detail: Result = {
                statistName: row.statistName, //成本类型
                budgetAmount: row.budgetAmount, //预算金额
                accumulatedBudgetAmount: row.accumulatedBudgetAmount, //累计预算金额
                achievementRate: toPercentOrNull({
                    dividend: row.actualAmount,
                    divisor: row.budgetAmount,
                }),
                actualAmount: row.actualAmount, //实际金额
                accumulatedActualAmount: row.accumulatedActualAmount, //累计实际金额
                accumulatedAchievementRate: toPercentOrNull({
                    dividend: row.accumulatedActualAmount,
                    divisor: row.accumulatedBudgetAmount,
                }),
            };
            results.push(detail);
        });
        return results;
    }

    private buildRawSql(params: {
        year: number;
        month: number;
        filters: string;
    }): string {
        const { year, month, filters } = params;
        // prettier-ignore
        const sql = `
            SELECT a.statist_name AS statistName
                  ,a.budgetAmount AS budgetAmount
                  ,a.accumulatedBudgetAmount AS accumulatedBudgetAmount
                  ,b.accumulatedActualAmount AS accumulatedActualAmount
                  ,c.actualAmount AS actualAmount
            FROM (
                        SELECT [iyear]
                              ,[statist_name]
	                          ,SUM(CASE ${month}
		                           WHEN 1 THEN ROUND(ISNULL([Jan], 0), 0)
		                           WHEN 2 THEN ROUND(ISNULL([Feb], 0), 0)
		                           WHEN 3 THEN ROUND(ISNULL(mar, 0), 0)
		                           WHEN 4 THEN ROUND(ISNULL(apr, 0), 0)
		                           WHEN 5 THEN ROUND(ISNULL(may, 0), 0)
		                           WHEN 6 THEN ROUND(ISNULL(jun, 0), 0)
		                           WHEN 7 THEN ROUND(ISNULL(jul, 0), 0)
		                           WHEN 8 THEN ROUND(ISNULL(aug, 0), 0)
		                           WHEN 9 THEN ROUND(ISNULL(sep, 0), 0)
		                           WHEN 10 THEN ROUND(ISNULL(dec, 0), 0)
		                           WHEN 11 THEN ROUND(ISNULL(nov, 0), 0)
		                           WHEN 12 THEN ROUND(ISNULL(oct, 0), 0)
	                            END )as budgetAmount
                             ,SUM(CASE ${month}
	                               WHEN 1 THEN ROUND(ISNULL([Jan], 0), 0)
	                               WHEN 2 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)
	                               WHEN 3 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)
	                               WHEN 4 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)
	                               WHEN 5 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)
	                               WHEN 6 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)
	                               WHEN 7 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)
	                               WHEN 8 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)+ROUND(ISNULL(Aug, 0), 0)
	                               WHEN 9 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)+ROUND(ISNULL(Aug, 0), 0)+ROUND(ISNULL(Sep, 0), 0)
	                               WHEN 10 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)+ROUND(ISNULL(Aug, 0), 0)+ROUND(ISNULL(Sep, 0), 0)+ROUND(ISNULL(Nov, 0), 0)
	                               WHEN 11 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)+ROUND(ISNULL(Aug, 0), 0)+ROUND(ISNULL(Sep, 0), 0)+ROUND(ISNULL(Nov, 0), 0)+ROUND(ISNULL(Dec, 0), 0)
	                               WHEN 12 THEN ROUND(ISNULL([Jan], 0), 0)+ROUND(ISNULL([Feb], 0), 0)+ ROUND(ISNULL([Mar], 0), 0)+ROUND(ISNULL(Apr, 0), 0)+ROUND(ISNULL(May, 0), 0)+ROUND(ISNULL(Jun, 0), 0)+ROUND(ISNULL(Jul, 0), 0)+ROUND(ISNULL(Aug, 0), 0)+ROUND(ISNULL(Sep, 0), 0)+ROUND(ISNULL(Nov, 0), 0)+ROUND(ISNULL(Dec, 0), 0)+ROUND(ISNULL(oct, 0), 0)
                                END) as accumulatedBudgetAmount
                        FROM [CLINICO_CHINA].[dbo].[t_controllable_expense_object]
	                    WHERE [iyear] = ${year}
                        ${filters}
						GROUP BY [iyear],[statist_name]
                    ) AS a
            LEFT JOIN (
                        SELECT b.statist_name
                              ,SUM(a.[amount]) AS accumulatedActualAmount
                        FROM [CLINICO_CHINA].[dbo].[v_controllable_expense] A 
                        LEFT OUTER JOIN t_statist_account B ON a.accounting_code = b.accounting_code
                        WHERE YEAR(a.[accounting_date]) = ${year}
                        AND MONTH(a.[accounting_date]) <= ${month}
                        ${filters}
                        GROUP BY b.statist_name
            ) AS b ON a.statist_name = b.statist_name
            LEFT JOIN (
                        SELECT b.statist_name
                              ,SUM(a.[amount]) AS actualAmount
                        FROM [CLINICO_CHINA].[dbo].[v_controllable_expense] A 
                        LEFT OUTER JOIN t_statist_account B ON a.accounting_code = b.accounting_code
                        WHERE YEAR(a.[accounting_date]) = ${year}
                        AND MONTH(a.[accounting_date]) = ${month}
                        ${filters}
                        GROUP BY b.statist_name
            ) AS c ON a.statist_name = c.statist_name;
            `;
        return sql;
    }
}
