import { Inject, Service } from 'typedi';
import { Arg, Args, FieldResolver, Query, Resolver, Root } from 'type-graphql';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';

import { ControllableCostByYearItemService } from './controllableCostByYearItem.service';
import { ControllableCostByYearItem, SearchArgs } from './controllableCostByYearItem.type';

@Service()
@Resolver((of) => ControllableCostByYearItem)
export class ControllableCostByYearItemResolver {
    @Inject()
    private controllableCostByYearItemService: ControllableCostByYearItemService;

    @UserAuthInterceptor()
    @Query(() => [ControllableCostByYearItem])
    async controllableCostByYearItems(
        @Args() { filters, pagination }: SearchArgs,
    ): Promise<ControllableCostByYearItem[]> {
        const result =
            await this.controllableCostByYearItemService.getControllableCostByYearItem(
                { ...filters },
            );
        return result;
    }
}
