import { BaseError } from '@clinico/base-error';
import { Utils } from '@clinico/clinico-node-framework';
import { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import { INTERNAL_SERVER_ERROR, UNAUTHORIZED } from 'http-status';
import { Inject, Service } from 'typedi';

import { EnumErrorMessage, EnumPermissionCode } from '~/constants';
import { DI } from '~/container';
import { UserPermissionService } from '~/modules/permission/userPermission/userPermission.service';

import { RefreshTokenLogService } from './refreshTokenLog.service';

const { em, auth } = DI;

@Service('AuthService')
export class AuthService {
    protected entity = PublicUser;

    async loginBySSO(SSOToken: string): Promise<{ row: PublicUser; token: string }> {
        try {
            const { userCode } = await Utils.SSO.verify(SSOToken);
            const row = await em.findOneOrFail(PublicUser, { code: userCode });
            const token = await this.setToken(row);

            this.writeLoginLog({ row });

            return { row, token };
        } catch (error: any) {
            if (error.message === 'Invalid SSO Token') {
                throw new BaseError(EnumErrorMessage.InvalidSSOToken, UNAUTHORIZED);
            } else {
                throw new BaseError(error.message, INTERNAL_SERVER_ERROR);
            }
        }
    }

    @Inject()
    private refreshTokenLogService: RefreshTokenLogService;
    private async writeLoginLog(params: { row: PublicUser }) {
        await this.refreshTokenLogService.create({ userId: params.row.id }, { skipError: true });
    }

    @Inject('UserPermissionService')
    private userPermissionService: UserPermissionService;
    async checkUserPermission(params: {
        userId: number;
        permissions: EnumPermissionCode[];
    }): Promise<boolean> {
        const userPermission = await this.userPermissionService.findOneOrCreate({
            userId: params.userId,
        });

        const has = userPermission.codes.some((code) =>
            (params.permissions as string[]).includes(code)
        );
        return has;
    }

    async setToken(payload: PublicUser): Promise<string> {
        const token = auth.sign({
            id: payload.id,
            email: payload.email,
            name: payload.name,
            code: payload.code,
        });

        return token;
    }
}
