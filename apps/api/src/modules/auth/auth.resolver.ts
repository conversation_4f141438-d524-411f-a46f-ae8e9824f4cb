import { Args, Ctx, Mutation, Resolver } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { AuthSSOLoginArgs } from '~/modules/auth/auth.input';
import { AuthService } from '~/modules/auth/auth.service';
import { Auth } from '~/modules/auth/auth.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

@Service()
@Resolver(() => Auth)
export class AuthResolver {
    @Inject('AuthService')
    private service: AuthService;

    @Mutation(() => Auth, { name: 'loginBySSO' })
    async loginBySSO(@Ctx() ctx: Context, @Args() { SSOToken }: AuthSSOLoginArgs): Promise<Auth> {
        const { row, token } = await this.service.loginBySSO(SSOToken);

        return { token, user: row };
    }
}
