import { PublicRefreshTokenLog } from '@clinico/mikro-orm-persistence/models/public/refreshTokenLog.model';
import { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { logger } from '@packages/logger';

import { configs } from '~/app.config';
import { BaseService } from '~/utils/providers/base.service';

import { SearchOptions } from './refreshTokenLog.service.type';
import { FilterInput } from './refreshTokenLog.type';

@Service()
export class RefreshTokenLogService extends BaseService<PublicRefreshTokenLog> {
    protected entity = PublicRefreshTokenLog;

    async search(
        filters: FilterInput,
        options?: SearchOptions
    ): Promise<{ count: number | null; rows: PublicRefreshTokenLog[] }> {
        const em = this.em.fork();

        const where: FilterQuery<PublicRefreshTokenLog>[] = [];
        this.appendFilterByAccessControl(where, filters);

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
            }
        );

        return { rows, count };
    }

    private appendFilterByAccessControl(
        where: FilterQuery<PublicRefreshTokenLog>[],
        params?: FilterInput
    ) {
        const accesses: FilterQuery<PublicRefreshTokenLog> = [];

        // For field/mutation resolvers
        if (params?.id) {
            // field/mutation resolvers
            accesses.push({ id: params.id });
        }
        if (params?.ids) {
            // Data loader
            accesses.push({ id: { $in: params.ids } });
        }

        where.push({ $or: accesses });
    }

    async create(
        params: { userId: number },
        options?: { skipError?: boolean }
    ): Promise<PublicRefreshTokenLog | undefined> {
        try {
            const em = this.em.fork();

            const row = new this.entity();
            row.user = em.getReference(PublicUser, params.userId);
            row.applicationName = configs.app.name;
            await em.persist(row).flush();

            const created = await this.findOneOrError({ id: row.id });
            return created;
        } catch (error: any) {
            if (!options?.skipError) {
                throw error;
            }
            logger.error(error);

            return undefined;
        }
    }
}
