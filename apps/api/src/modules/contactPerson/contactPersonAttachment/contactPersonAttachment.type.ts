import type { ContactPeopleAttachment as ContactPersonAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPeopleAttachment.model';
import { IContactPeopleAttachment as IContactPersonAttachment } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/contactPeopleAttachment.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('ContactPersonAttachment', { implements: IContactPersonAttachment })
export class TContactPersonAttachment extends IContactPersonAttachment {}

@ObjectType('PaginatedContactPersonAttachments')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TContactPersonAttachment], { nullable: true })
    contactPersonAttachments: ContactPersonAttachment[];
}

@InputType('ContactPersonAttachmentFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    contactPeopleId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
