import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('ContactPersonAttachmentInput')
export class CommonInput {
    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    memo?: string;

    deleted?: boolean;
}

@InputType('ContactPersonAttachmentCreateInput')
export class CreateInput extends CommonInput {
    @Field(() => ID)
    contactPeopleId: number;

    @Field(() => GraphQLUpload)
    file: Promise<FileUpload>;
}

@InputType('ContactPersonAttachmentBulkCreateInput')
export class BulkCreateInput {
    @Field(() => ID)
    contactPeopleId: number;

    @Field(() => [CreateAttachmentInput])
    attachments: CreateAttachmentInput[];
}

@InputType('ContactPersonAttachmentCreateAttachmentInput')
export class CreateAttachmentInput extends CommonInput {
    @Field(() => GraphQLUpload)
    file: Promise<FileUpload>;
}

@InputType('ContactPersonAttachmentUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
