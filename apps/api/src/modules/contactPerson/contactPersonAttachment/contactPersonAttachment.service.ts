import { ContactPeopleAttachment as ContactPersonAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPeopleAttachment.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import {
    AttachmentParams,
    BulkCreateContactPersonAttachmentParams,
    CreateContactPersonAttachmentParams,
    DeleteContactPersonAttachmentParams,
    UpdateContactPersonAttachmentParams,
} from '@packages/erp-protobuf/generated/contactPerson/contactPersonAttachment_pb';
import { FileInfo } from '@packages/erp-protobuf/generated/contactPerson/contactPersonAttachment_pb';

import { streamToBuffer } from '~/utils/helpers/fileHandler.helper';
import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { ContactPersonAttachmentGrpcRepo } from './contactPersonAttachment.grpc.repo';
import { BulkCreateInput, CreateInput, UpdateInput } from './contactPersonAttachment.input';
import { FilterInput } from './contactPersonAttachment.type';

@Service()
export class ContactPersonAttachmentService extends BaseService<ContactPersonAttachment> {
    protected entity = ContactPersonAttachment;
    private gRPCrepo = new ContactPersonAttachmentGrpcRepo();

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: ContactPersonAttachment[] }> {
        const em = this.em.fork();

        const where: FilterQuery<ContactPersonAttachment>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
                populate: ['contactPerson'],
            }
        );

        return { rows, count };
    }

    async createByGrpc(
        params: CreateInput,
        options?: { ctx?: Context }
    ): Promise<ContactPersonAttachment> {
        const file = await params.file;
        const stream = file.createReadStream();
        const buffer = await streamToBuffer(stream);

        const input = new CreateContactPersonAttachmentParams({
            contactPeopleId: params.contactPeopleId,
            name: params.name || file.filename,
            file: new FileInfo({ ...file, content: buffer }),
            createdUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.create(input);

        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async bulkCreateByGrpc(
        params: BulkCreateInput,
        options?: { ctx?: Context }
    ): Promise<ContactPersonAttachment[]> {
        const inputs = new BulkCreateContactPersonAttachmentParams({
            contactPeopleId: params.contactPeopleId,
            createdUserId: options?.ctx?.currentUser?.id,
        });

        const attachments: AttachmentParams[] = [];
        for (const attachment of params.attachments) {
            const file = await attachment.file;
            const stream = file.createReadStream();
            const buffer = await streamToBuffer(stream);

            const next = new AttachmentParams({
                name: attachment.name || file.filename,
                file: new FileInfo({ ...file, content: buffer }),
            });
            attachments.push(next);
        }
        inputs.attachments = attachments;

        const row = await this.gRPCrepo.bulkCreate(inputs);
        const { rows } = await this.search({ ids: row.result?.data?.ids ?? [] });
        return rows;
    }

    async updateByGrpc(
        params: UpdateInput,
        options?: { ctx?: Context }
    ): Promise<ContactPersonAttachment> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateContactPersonAttachmentParams({
            id: row.id,
            name: params.name,
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteContactPersonAttachmentParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.delete(inputs);
        return res.success;
    }
}
