import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    BulkCreateContactPersonAttachmentParams,
    ContactPersonAttachmentUpdateResult,
    ContactPersonBulkAttachmentCreateResult,
    CreateContactPersonAttachmentParams,
    DeleteContactPersonAttachmentParams,
    UpdateContactPersonAttachmentParams,
} from '@packages/erp-protobuf/generated/contactPerson/contactPersonAttachment_pb';

@Service()
export class ContactPersonAttachmentGrpcRepo {
    serviceNodes: string[] = ['contactPersonAttachment', 'ContactPersonAttachment'];

    async bulkCreate(
        params: BulkCreateContactPersonAttachmentParams
    ): Promise<ContactPersonBulkAttachmentCreateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<ContactPersonBulkAttachmentCreateResult>(
                'bulkCreate',
                params
            );

            return res;
        } catch (err: any) {
            const message = ['[gRPC.ContactPersonAttachment.bulkCreate]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async create(
        params: CreateContactPersonAttachmentParams
    ): Promise<ContactPersonAttachmentUpdateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<ContactPersonAttachmentUpdateResult>('create', params);
            return res;
        } catch (err: any) {
            const message = ['[gRPC.ContactPersonAttachment.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(
        params: UpdateContactPersonAttachmentParams
    ): Promise<ContactPersonAttachmentUpdateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<ContactPersonAttachmentUpdateResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.ContactPersonAttachment.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(
        params: DeleteContactPersonAttachmentParams
    ): Promise<ContactPersonAttachmentUpdateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<ContactPersonAttachmentUpdateResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.ContactPersonAttachment.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
