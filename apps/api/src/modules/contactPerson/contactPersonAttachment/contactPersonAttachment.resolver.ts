import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { ContactPeopleAttachment as ContactPersonAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPeopleAttachment.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { createFileBaseResolver } from '~/utils/providers/fileBase.resolver';
import { ContactPersonService } from '../contactPerson/contactPerson.service';
import { TContactPerson } from '../contactPerson/contactPerson.type';
import { BulkCreateInput, CreateInput, UpdateInput } from './contactPersonAttachment.input';
import { ContactPersonAttachmentService } from './contactPersonAttachment.service';
import { PaginatedObjects, SearchArgs, TContactPersonAttachment } from './contactPersonAttachment.type';
import { ContactPerson } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPerson.model';

const FileBaseResolver = createFileBaseResolver(TContactPersonAttachment);

@Service()
@Resolver(() => TContactPersonAttachment)
export class ContactPersonAttachmentResolver extends FileBaseResolver {
    @Inject()
    private service: ContactPersonAttachmentService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedContactPersonAttachments' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerAttachments: ContactPersonAttachment[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerAttachments: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TContactPersonAttachment], { name: 'contactPersonAttachments' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<ContactPersonAttachment[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TContactPersonAttachment, { name: 'contactPersonAttachment' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<ContactPersonAttachment> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TContactPersonAttachment, { name: 'createContactPersonAttachment' })
    async create(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<ContactPersonAttachment> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => [TContactPersonAttachment], { name: 'bulkCreateContactPersonAttachment' })
    async bulkCreate(
        @Ctx() ctx: Context,
        @Arg('input') input: BulkCreateInput
    ): Promise<ContactPersonAttachment[]> {
        const rows = await this.service.bulkCreateByGrpc(input, { ctx });
        return rows;
    }

    @UserAuthInterceptor()
    @Mutation(() => TContactPersonAttachment, { name: 'updateContactPersonAttachment' })
    async update(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<ContactPersonAttachment> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteContactPersonAttachment' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private contactPersonService: ContactPersonService;
    @FieldResolver(() => TContactPerson, { description: '客戶' })
    async contactPerson(@Root() model: ContactPersonAttachment): Promise<ContactPerson | undefined> {
        return await this.contactPersonService.findOne({ id: model.contactPerson.id });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: ContactPersonAttachment): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: ContactPersonAttachment): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }
}
