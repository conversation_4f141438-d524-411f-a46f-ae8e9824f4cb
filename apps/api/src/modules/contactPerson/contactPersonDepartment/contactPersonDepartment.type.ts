import type { ContactPersonDepartment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPersonDepartment.model';
import { IContactPersonDepartment } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/contactPersonDepartment.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('ContactPersonDepartment', { implements: IContactPersonDepartment })
export class TContactPersonDepartment extends IContactPersonDepartment {}

@ObjectType('PaginatedContactPersonDepartments')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TContactPersonDepartment], { nullable: true })
    contactPersonDepartments: ContactPersonDepartment[];
}

@InputType('ContactPersonDepartmentFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
