import type { ContactPersonDepartment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPersonDepartment.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { ContactPersonDepartmentService } from './contactPersonDepartment.service';
import {
    PaginatedObjects,
    SearchArgs,
    TContactPersonDepartment,
} from './contactPersonDepartment.type';

@Service()
@Resolver(() => TContactPersonDepartment)
export class ContactPersonDepartmentResolver {
    @Inject()
    private service: ContactPersonDepartmentService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedContactPersonDepartments' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; contactPersonDepartments: ContactPersonDepartment[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, contactPersonDepartments: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TContactPersonDepartment], { name: 'contactPersonDepartments' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<ContactPersonDepartment[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TContactPersonDepartment, { name: 'contactPersonDepartment' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<ContactPersonDepartment> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(
        @Root() model: ContactPersonDepartment
    ): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }
}
