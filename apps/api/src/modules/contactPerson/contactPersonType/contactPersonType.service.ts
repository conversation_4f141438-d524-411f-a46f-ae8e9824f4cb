import { ContactPeopleType as ContactPersonType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPeopleType.model';
import { EntityManager, FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import {
    CreateContactPersonTypeParams,
    DeleteContactPersonTypeParams,
    UpdateContactPersonTypeParams,
} from '@packages/erp-protobuf/generated/contactPerson/contactPersonType_pb';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { EntityUpdater } from '~/utils/providers/entityUpdater.provider';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { ContactPersonTypeGrpcRepo } from './contactPersonType.grpc.repo';
import { CommonInput, CreateInput, UpdateInput } from './contactPersonType.input';
import { FilterInput } from './contactPersonType.type';
import { handler } from '@packages/erp-protobuf';

@Service()
export class ContactPersonTypeService extends BaseService<ContactPersonType> {
    protected entity = ContactPersonType;
    private gRPCrepo = new ContactPersonTypeGrpcRepo();
    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: ContactPersonType[] }> {
        const em = this.em.fork();

        const where: FilterQuery<ContactPersonType>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ viewOrder: 'ASC' }, { id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    async createByGrpc(
        params: CreateInput,
        options?: { ctx?: Context }
    ): Promise<ContactPersonType> {
        const input = new CreateContactPersonTypeParams({
            name: params.name,
            viewOrder: params.viewOrder,
            createdUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.create(input);
        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(
        params: UpdateInput,
        options?: { ctx?: Context }
    ): Promise<ContactPersonType> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateContactPersonTypeParams({
            id: row.id,
            name: params.name,
            viewOrder: handler.toNumber({ input: params.viewOrder, default: row.viewOrder }),
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteContactPersonTypeParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.delete(inputs);
        return res.success;
    }
}
