import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('ContactPersonTypeInput')
export class CommonInput {
    @Field({ nullable: true })
    name?: string;

    @Field(() => Int, { nullable: true })
    viewOrder?: number;

    deleted?: boolean;
}

@InputType('ContactPersonTypeCreateInput')
export class CreateInput extends CommonInput {}

@InputType('ContactPersonTypeUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
