import type { ContactPeopleType as ContactPersonType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPeopleType.model';
import { IContactPeopleType } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/contactPeopleType.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('ContactPersonType', { implements: IContactPeopleType })
export class TContactPersonType extends IContactPeopleType {}

@ObjectType('PaginatedContactPersonTypes')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TContactPersonType], { nullable: true })
    contactPersonTypes: ContactPersonType[];
}

@InputType('ContactPersonTypeFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
