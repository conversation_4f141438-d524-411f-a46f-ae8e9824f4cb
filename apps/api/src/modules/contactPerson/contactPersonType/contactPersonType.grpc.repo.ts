import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    ContactPersonTypeResult,
    CreateContactPersonTypeParams,
    DeleteContactPersonTypeParams,
    UpdateContactPersonTypeParams,
} from '@packages/erp-protobuf/generated/contactPerson/contactPersonType_pb';

@Service()
export class ContactPersonTypeGrpcRepo {
    serviceNodes: string[] = ['contactPersonType', 'ContactPersonType'];

    async create(params: CreateContactPersonTypeParams): Promise<ContactPersonTypeResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<ContactPersonTypeResult>('create', params);
            return res;
        } catch (err: any) {
            const message = ['[gRPC.ContactPersonType.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }


    async update(params: UpdateContactPersonTypeParams): Promise<ContactPersonTypeResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<ContactPersonTypeResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.ContactPersonType.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteContactPersonTypeParams): Promise<ContactPersonTypeResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<ContactPersonTypeResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.ContactPersonType.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
