import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { ContactPeopleAttachment as ContactPersonAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPeopleAttachment.model';
import type { ContactPeopleType as ContactPersonType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPeopleType.model';
import type { ContactPerson } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPerson.model';
import type { ContactPersonDepartment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPersonDepartment.model';
import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { SalesTeamUnit } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamUnit.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { EnumPermissionCode } from '~/constants';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { TSalesTeamUnit } from '~/modules/salesTeam/salesTeamUnit/salesTeamUnit.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { TCustomer } from '../../customer/customer/customer.type';
import { SalesTeamGroupService } from '../../salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '../../salesTeam/salesTeamGroup/salesTeamGroup.type';
import { UserService } from '../../user/user/user.service';
import { TUser } from '../../user/user/user.type';
import { TContactPersonAttachment } from '../contactPersonAttachment/contactPersonAttachment.type';
import { ContactPersonDepartmentService } from '../contactPersonDepartment/contactPersonDepartment.service';
import { TContactPersonDepartment } from '../contactPersonDepartment/contactPersonDepartment.type';
import { ContactPersonTypeService } from '../contactPersonType/contactPersonType.service';
import { TContactPersonType } from '../contactPersonType/contactPersonType.type';
import { CreateInput, UpdateInput } from './contactPerson.input';
import { ContactPersonService } from './contactPerson.service';
import { PaginatedObjects, SearchArgs, TContactPerson } from './contactPerson.type';

@Service()
@Resolver(() => TContactPerson)
export class ContactPersonResolver {
    @Inject()
    private service: ContactPersonService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedContactPeople' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; contactPeople: ContactPerson[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, contactPeople: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TContactPerson], { name: 'contactPeople' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<ContactPerson[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TContactPerson, { name: 'contactPerson' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<ContactPerson> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['contact_people.create'])
    @Mutation(() => TContactPerson, { name: 'createContactPerson' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<ContactPerson> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['contact_people.update'])
    @Mutation(() => TContactPerson, { name: 'updateContactPerson' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<ContactPerson> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['contact_people.delete'])
    @Mutation(() => Boolean, { name: 'deleteContactPerson' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: ContactPerson): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }

    @Inject()
    private contactPersonDepartmentService: ContactPersonDepartmentService;
    @FieldResolver(() => TContactPersonDepartment, { description: '部門', nullable: true })
    async department(@Root() model: ContactPerson): Promise<ContactPersonDepartment | undefined> {
        return await this.contactPersonDepartmentService.findOne({ id: model.department?.id });
    }

    @Inject()
    private contactPersonTypeService: ContactPersonTypeService;
    @FieldResolver(() => TContactPersonType, { description: '級數', nullable: true })
    async type(@Root() model: ContactPerson): Promise<ContactPersonType | undefined> {
        return await this.contactPersonTypeService.findOne({ id: model.type?.id });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: ContactPerson): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUser?.id });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: ContactPerson): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUser?.id });
    }

    @FieldResolver(() => [TCustomer], { description: '客戶' })
    async customers(@Root() model: ContactPerson): Promise<Customer[]> {
        if (!model.customersContactPeople.isInitialized()) {
            await model.customersContactPeople.init({
                populate: ['customer'],
                where: { customer: { deleted: false } },
            });
        }
        return model.customersContactPeople.getItems().map((item) => item.customer);
    }

    @FieldResolver(() => [TContactPersonAttachment], { description: '聯絡人附件' })
    async attachments(@Root() model: ContactPerson): Promise<ContactPersonAttachment[]> {
        if (!model.contactPeopleAttachments.isInitialized()) {
            await model.contactPeopleAttachments.init({
                where: { deleted: false },
            });
        }
        return model.contactPeopleAttachments.getItems();
    }

    @FieldResolver(() => [TSalesTeamUnit], { description: '业务团队位置' })
    async salesTeamUnits(@Root() model: ContactPerson): Promise<SalesTeamUnit[]> {
        if (!model.contactPeopleSalesTeamUnits.isInitialized()) {
            await model.contactPeopleSalesTeamUnits.init({ populate: ['salesTeamUnit'] });
        }
        return model.contactPeopleSalesTeamUnits.getItems().map((item) => item.salesTeamUnit);
    }

    @FieldResolver(() => [TUser], { description: '主要負責業務' })
    async primaryUsers(@Root() model: ContactPerson): Promise<PublicUser[]> {
        if (!model.contactPeoplePrimaryUsers.isInitialized()) {
            await model.contactPeoplePrimaryUsers.init({ populate: ['user'] });
        }
        return model.contactPeoplePrimaryUsers.getItems().map((item) => item.user);
    }

    @FieldResolver(() => [TUser], { description: '負責業務' })
    async users(@Root() model: ContactPerson): Promise<PublicUser[]> {
        if (!model.contactPeopleUsers.isInitialized()) {
            await model.contactPeopleUsers.init({ populate: ['user'] });
        }
        return model.contactPeopleUsers.getItems().map((item) => item.user);
    }
}
