import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    ContactPersonResult,
    CreateContactPersonParams,
    DeleteContactPersonParams,
    UpdateContactPersonParams,
} from '@packages/erp-protobuf/generated/contactPerson/contactPerson_pb';

@Service()
export class ContactPersonGrpcRepo {
    serviceNodes: string[] = ['contactPerson', 'ContactPerson'];

    async create(params: CreateContactPersonParams): Promise<ContactPersonResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<ContactPersonResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.ContactPerson.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateContactPersonParams): Promise<ContactPersonResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<ContactPersonResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.ContactPerson.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteContactPersonParams): Promise<ContactPersonResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<ContactPersonResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.ContactPerson.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
