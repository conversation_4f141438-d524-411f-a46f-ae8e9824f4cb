import { ContactPerson } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPerson.model';
import { FilterQuery } from '@mikro-orm/core';
import _ from 'lodash';
import { Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import {
    AttachmentParams,
    CreateContactPersonParams,
    DeleteContactPersonParams,
    FileInfo,
    UpdateContactPersonParams,
} from '@packages/erp-protobuf/generated/contactPerson/contactPerson_pb';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { ContactPersonGrpcRepo } from './contactPerson.grpc.repo';
import { CreateInput, UpdateInput } from './contactPerson.input';
import { FilterInput } from './contactPerson.type';
import { streamToBuffer } from '~/utils/helpers/fileHandler.helper';

@Service()
export class ContactPersonService extends BaseService<ContactPerson> {
    protected entity = ContactPerson;
    private gRPCrepo = new ContactPersonGrpcRepo();

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: ContactPerson[] }> {
        const em = this.em.fork();

        const where: FilterQuery<ContactPerson>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        // Access control
        const accesses: FilterQuery<ContactPerson> = [];
        const permission = options?.ctx?.currentPermission;
        if (permission?.allowSalesTeamGroupIds) {
            const allowSalesTeamGroupIds = permission?.allowSalesTeamGroupIds;
            accesses.push({ salesTeamGroup: { id: { $in: allowSalesTeamGroupIds } } });
        }
        if (permission?.allowRegionIds) {
            const allowRegionIds = permission?.allowRegionIds;
            accesses.push({ salesTeamGroup: { region: { id: { $in: allowRegionIds } } } });
        }
        where.push({ $and: accesses });

        // Filter inputs
        if (params?.businessId) {
            where.push({ businessesPrimaryContactPeople: { business: { id: params.businessId } } });
        }
        if (!_.isUndefined(params?.customerId)) {
            where.push({ customersContactPeople: { customer: { id: params?.customerId } } });
        }
        if (!_.isUndefined(params?.notCustomerId)) {
            const rows = await this.em
                .fork()
                .find(
                    ContactPerson,
                    { customersContactPeople: { customer: { id: params?.notCustomerId } } },
                    { fields: ['id'] }
                );
            where.push({ id: { $nin: rows.map((row) => row.id) } });
        }
        if (params?.customerName) {
            where.push({
                customersContactPeople: {
                    customer: { name: { $ilike: `%${params.customerName}%` } },
                },
            });
        }
        if (params?.customerCode) {
            where.push({
                customersContactPeople: {
                    customer: { code: { $ilike: `%${params.customerCode}%` } },
                },
            });
        }
        if (params?.customerBusinessCode) {
            where.push({
                customersContactPeople: {
                    customer: { businessCode: { $ilike: `%${params.customerBusinessCode}%` } },
                },
            });
        }
        if (params?.customerMedicalCode) {
            where.push({
                customersContactPeople: {
                    customer: { medicalCode: { $ilike: `%${params.customerMedicalCode}%` } },
                },
            });
        }
        if (params?.salesTeamUnitId) {
            where.push({
                contactPeopleSalesTeamUnits: { salesTeamUnit: { id: params.salesTeamUnitId } },
            });
        }
        if (params?.salesTeamUnitIds) {
            where.push({
                contactPeopleSalesTeamUnits: {
                    salesTeamUnit: { id: { $in: params.salesTeamUnitIds } },
                },
            });
        }
        if (params?.primaryUserId) {
            where.push({
                contactPeopleSalesTeamUnits: {
                    salesTeamUnit: {
                        user: { id: params.primaryUserId },
                    },
                },
            });
        }
        if (params?.primaryUserIds) {
            where.push({
                contactPeopleSalesTeamUnits: {
                    salesTeamUnit: {
                        user: { id: { $in: params.primaryUserIds } },
                    },
                },
            });
        }
        if (params?.userId) {
            where.push({ contactPeopleUsers: { user: { id: params.userId } } });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: [
                    'salesTeamGroup',
                    'createdUser',
                    'updatedUser',
                ],
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    async createByGrpc(params: CreateInput, options?: { ctx?: Context }): Promise<ContactPerson> {
        const inputs = new CreateContactPersonParams({
            salesTeamGroupId: options?.ctx?.currentSalesTeamGroup?.id,
            regionId: options?.ctx?.currentSalesTeamGroup?.region?.id,
            typeId: params?.typeId,
            bankBranch: params?.bankBranch,
            bankCardNumber: params?.bankCardNumber,
            hobby: params?.hobby,
            jobTitle: params?.jobTitle,
            memo: params?.memo,
            mobile: params?.mobile,
            name: params?.name,
            nickname: params?.nickname,
            citizenCode: params?.citizenCode,
            phone: params?.phone,
            skill: params?.skill,
            website: params?.website,
            address: params.address,
            departmentId: params?.departmentId,
            doctorCode: params.doctorCode,
            email: params.email,
            fax: params.fax,
            gender: _.isNil(params.gender) ? undefined : params.gender,
            customerIds: params.customerIds,
            primaryUserIds: params.primaryUserIds,
            salesTeamUnitIds: params.salesTeamUnitIds,
            createdUserId: options?.ctx?.currentUser?.id,
        });
        if (params.attachments) {
            inputs.attachments = [];
            for (const attachment of params.attachments) {
                const file = await attachment.file;
                const stream = file.createReadStream();
                const buffer = await streamToBuffer(stream);

                const params = new AttachmentParams({
                    name: attachment.name || file.filename,
                    file: new FileInfo({ ...file, content: buffer }),
                });
                inputs.attachments.push(params);
            }
        }

        const row = await this.gRPCrepo.create(inputs);

        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(params: UpdateInput, options?: { ctx?: Context }): Promise<ContactPerson> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateContactPersonParams({
            id: row.id,
            salesTeamGroupId: row.salesTeamGroup.id,
            typeId: handler.toNumber({ input: params.typeId, default: row.type?.id }),
            bankBranch: handler.toString({ input: params?.bankBranch, default: row.bankBranch }),
            bankCardNumber: handler.toString({
                input: params?.bankCardNumber,
                default: row.bankCardNumber,
            }),
            hobby: handler.toString({ input: params.hobby, default: row.hobby }),
            jobTitle: handler.toString({ input: params?.jobTitle, default: row.jobTitle }),
            memo: handler.toString({ input: params?.memo, default: row.memo }),
            mobile: handler.toString({ input: params?.mobile, default: row.mobile }),
            name: handler.toString({ input: params?.name, default: row.name }),
            nickname: handler.toString({ input: params?.nickname, default: row.nickname }),
            citizenCode: handler.toString({ input: params?.citizenCode, default: row.citizenCode }),
            phone: handler.toString({ input: params?.phone, default: row.phone }),
            skill: handler.toString({ input: params?.skill, default: row.skill }),
            website: handler.toString({ input: params?.website, default: row.website }),
            address: handler.toString({ input: params.address, default: row.address }),
            departmentId: handler.toNumber({
                input: params?.departmentId,
                default: row.department?.id,
            }),
            doctorCode: handler.toString({ input: params.doctorCode, default: row.doctorCode }),
            email: handler.toString({ input: params.email, default: row.email }),
            fax: handler.toString({ input: params.fax, default: row.fax }),
            gender: (() => {
                const val = handler.toString({ input: params.gender, default: row.gender });
                return val ? val : undefined;
            })(),
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        if (params.customerIds) {
            inputs.customerIds = params.customerIds;
        } else {
            if (!row.customersContactPeople.isInitialized()) {
                await row.customersContactPeople.init({ populate: ['customer'] });
            }
            const items = row.customersContactPeople.getItems();
            inputs.customerIds = items.map((item) => item.customer.id);
        }

        if (params.salesTeamUnitIds) {
            inputs.salesTeamUnitIds = params.salesTeamUnitIds;
        } else {
            if (!row.contactPeopleSalesTeamUnits.isInitialized()) {
                await row.contactPeopleSalesTeamUnits.init({ populate: ['salesTeamUnit'] });
            }
            const items = row.contactPeopleSalesTeamUnits.getItems();
            inputs.salesTeamUnitIds = items.map((item) => item.salesTeamUnit.id);
        }

        if (params.primaryUserIds) {
            inputs.primaryUserIds = params.primaryUserIds;
        } else {
            if (!row.contactPeoplePrimaryUsers.isInitialized()) {
                await row.contactPeoplePrimaryUsers.init({ populate: ['user'] });
            }
            const items = row.contactPeoplePrimaryUsers.getItems();
            inputs.primaryUserIds = items.map((item) => item.user.id);
        }

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteContactPersonParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }
}
