import { EnumContactPeopleGender } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPerson.model';
import { EnumContactPeopleGender as IEnumContactPeopleGender } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/contactPerson.model';
import { Field, ID, InputType } from 'type-graphql';

import {
    CreateAttachmentInput,
} from '../contactPersonAttachment/contactPersonAttachment.input';

@InputType('ContactPersonInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field(() => [ID], { nullable: true })
    customerIds?: number[];

    @Field(() => ID, { nullable: true })
    typeId?: number;

    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    nickname?: string;

    @Field(() => IEnumContactPeopleGender, { nullable: true })
    gender?: EnumContactPeopleGender;

    @Field({ nullable: true })
    jobTitle?: string;

    @Field({ nullable: true })
    citizenCode?: string;

    @Field({ nullable: true })
    doctorCode?: string;

    @Field({ nullable: true, deprecationReason: '由 `departmentId` 欄位取代' })
    dept?: string;

    @Field(() => ID, { nullable: true, description: '部門' })
    departmentId?: number;

    @Field({ nullable: true })
    phone?: string;

    @Field({ nullable: true })
    mobile?: string;

    @Field({ nullable: true })
    email?: string;

    @Field({ nullable: true })
    fax?: string;

    @Field({ nullable: true })
    address?: string;

    @Field({ nullable: true })
    website?: string;

    @Field({ nullable: true })
    hobby?: string;

    @Field({ nullable: true })
    skill?: string;

    @Field({ nullable: true })
    memo?: string;

    @Field({ nullable: true })
    bankBranch?: string;

    @Field({ nullable: true })
    bankCardNumber?: string;

    @Field(() => [ID], { nullable: true, description: '业务团队位置' })
    salesTeamUnitIds?: number[];

    @Field(() => [ID], { nullable: true })
    primaryUserIds?: number[];

    @Field(() => [ID], { nullable: true })
    userIds?: number[];

    deleted?: boolean;
}

@InputType('ContactPersonCreateInput')
export class CreateInput extends CommonInput {
    @Field(() => [CreateAttachmentInput], { nullable: true })
    attachments?: CreateAttachmentInput[];
}

@InputType('ContactPersonUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
