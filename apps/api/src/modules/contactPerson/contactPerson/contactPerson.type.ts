import type { <PERSON><PERSON><PERSON> } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPerson.model';
import { IContactPerson } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/contactPerson.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('ContactPerson', { implements: IContactPerson })
export class TContactPerson extends IContactPerson {}

@ObjectType('PaginatedContactPeople')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TContactPerson], { nullable: true })
    contactPeople: ContactPerson[];
}

@InputType('ContactPersonFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    businessId?: number;

    @Field(() => ID, { nullable: true })
    customerId?: number;

    @Field(() => ID, { nullable: true, description: '排除客戶' })
    notCustomerId?: number;

    @Field({ nullable: true })
    customerName?: string;

    @Field({ nullable: true })
    customerCode?: string;

    @Field({ nullable: true })
    customerBusinessCode?: string;

    @Field({ nullable: true })
    customerMedicalCode?: string;

    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    code?: string;

    @Field({ nullable: true })
    jobTitle?: string;

    @Field({ nullable: true, deprecationReason: '由 `departmentId` 欄位取代' })
    dept?: string;

    @Field(() => ID, { nullable: true, description: '部門' })
    departmentId?: number;

    @Field((type) => ID, { nullable: true, description: '聯絡人分級' })
    typeId?: number;

    @Field({ nullable: true })
    phone?: string;

    @Field({ nullable: true })
    mobile?: string;

    @Field({ nullable: true })
    skill?: string;

    @Field({ nullable: true })
    address?: string;

    @Field(() => ID, { nullable: true })
    salesTeamUnitId?: number;

    @Field(() => [ID], { nullable: true })
    salesTeamUnitIds?: number[];

    @Field(() => ID, { nullable: true })
    primaryUserId?: number;

    @Field(() => [ID], { nullable: true })
    primaryUserIds?: number[];

    @Field(() => ID, { nullable: true })
    userId?: number;

    @Field({ nullable: true })
    bankBranch?: string;

    @Field({ nullable: true })
    bankCardNumber?: string;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
