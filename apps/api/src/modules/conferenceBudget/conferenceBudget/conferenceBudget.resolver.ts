import { Arg, Args, Ctx, FieldResolver, ID, Info, Mutation, Query, Resolver } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { EnumPermissionCode } from '~/constants';
import { ConferenceBudget, SearchArgs } from './conferenceBudget.tye';
import { ConferenceBudgetService } from './conferenceBudget.service';

@Service()
@Resolver(() => ConferenceBudget)
export class ConferenceBudgetResolver {
    @Inject()
    private service: ConferenceBudgetService;

    @UserAuthInterceptor(EnumPermissionCode['conferenceBudget.read'])
    @Query(() => [ConferenceBudget], {
        name: 'conferenceBudgets',
    })
    async conferenceBudgets(
        @Args() { filters }: SearchArgs,
        @Ctx() ctx: Context
    ): Promise<ConferenceBudget[]> {
        const stat = await this.service.search({ ...filters }, { ctx });
        return stat;
    }
}