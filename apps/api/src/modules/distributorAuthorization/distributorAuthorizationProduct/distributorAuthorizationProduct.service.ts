import { DistributorAuthorizationProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/distributorAuthorizationProduct.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './distributorAuthorizationProduct.type';

@Service()
export class DistributorAuthorizationProductService extends BaseService<DistributorAuthorizationProduct> {
    protected entity = DistributorAuthorizationProduct;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: DistributorAuthorizationProduct[] }> {
        const em = this.em.fork();

        const where: FilterQuery<DistributorAuthorizationProduct>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        if (params?.distributorAuthorizationId) {
            where.push({ distributorAuthorizationId: params.distributorAuthorizationId });
        }
        if (params?.authorizationProductId) {
            where.push({ authorizationProductId: params.authorizationProductId });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['authorizationProduct', 'distributorAuthorization'],
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }
}
