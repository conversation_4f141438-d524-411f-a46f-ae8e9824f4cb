import { ArgsType, Field, ID, InputType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs } from '~/utils/types/base.type';

@InputType('DistributorAuthorizationProductFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field((type) => ID, { nullable: true })
    distributorAuthorizationId?: number;

    @Field((type) => ID, { nullable: true })
    authorizationProductId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
