import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    CreateDistributorAuthorizationAttachmentParams,
    DeleteDistributorAuthorizationAttachmentParams,
    RequestResult,
} from '@packages/erp-protobuf/generated/distributorAuthorization/distributorAuthorizationAttachment_pb';

@Service()
export class DistributorAuthorizationAttachmentGrpcRepo {
    serviceNodes: string[] = [
        'distributorAuthorizationAttachment',
        'DistributorAuthorizationAttachment',
    ];

    async create(params: CreateDistributorAuthorizationAttachmentParams): Promise<RequestResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<RequestResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.DistributorAuthorizationAttachment.create]', err.message].join(
                ' '
            );
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteDistributorAuthorizationAttachmentParams): Promise<RequestResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<RequestResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.DistributorAuthorizationAttachment.delete]', err.message].join(
                ' '
            );
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
