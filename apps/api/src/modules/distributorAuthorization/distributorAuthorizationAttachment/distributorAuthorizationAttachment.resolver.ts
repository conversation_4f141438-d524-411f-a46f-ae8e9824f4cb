import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import { DistributorAuthorizationAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/distributorAuthorizationAttachment.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { createFileBaseResolver } from '~/utils/providers/fileBase.resolver';

import { CreateInput } from './distributorAuthorizationAttachment.input';
import { DistributorAuthorizationAttachmentService } from './distributorAuthorizationAttachment.service';
import {
    PaginatedObjects,
    SearchArgs,
    TDistributorAuthorizationAttachment,
} from './distributorAuthorizationAttachment.type';

const FileBaseResolver = createFileBaseResolver(TDistributorAuthorizationAttachment);

@Service()
@Resolver(() => TDistributorAuthorizationAttachment)
export class DistributorAuthorizationAttachmentAttachFileResolver extends FileBaseResolver {
    @Inject()
    private service: DistributorAuthorizationAttachmentService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, {
        name: 'paginatedDistributorAuthorizationAttachments',
    })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{
        count: number;
        distributorAuthorizationAttachments: DistributorAuthorizationAttachment[];
    }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, distributorAuthorizationAttachments: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TDistributorAuthorizationAttachment], {
        name: 'distributorAuthorizationAttachments',
    })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<DistributorAuthorizationAttachment[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TDistributorAuthorizationAttachment, {
        name: 'distributorAuthorizationAttachment',
    })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<DistributorAuthorizationAttachment> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'createDistributorAuthorizationAttachment' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<boolean> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteDistributorAuthorizationAttachment' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }
}
