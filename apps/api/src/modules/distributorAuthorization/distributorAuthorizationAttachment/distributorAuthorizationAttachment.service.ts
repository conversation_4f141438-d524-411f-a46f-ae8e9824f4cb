import { DistributorAuthorizationAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/distributorAuthorizationAttachment.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import {
    CreateDistributorAuthorizationAttachmentParams,
    DeleteDistributorAuthorizationAttachmentParams,
    FileInfo,
} from '@packages/erp-protobuf/generated/distributorAuthorization/distributorAuthorizationAttachment_pb';

import { streamToBuffer } from '~/utils/helpers/fileHandler.helper';
import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { DistributorAuthorizationAttachmentGrpcRepo } from './distributorAuthorizationAttachment.grpc.repo';
import { CreateInput } from './distributorAuthorizationAttachment.input';
import { FilterInput } from './distributorAuthorizationAttachment.type';

@Service()
export class DistributorAuthorizationAttachmentService extends BaseService<DistributorAuthorizationAttachment> {
    protected entity = DistributorAuthorizationAttachment;
    private gRPCrepo = new DistributorAuthorizationAttachmentGrpcRepo();

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: DistributorAuthorizationAttachment[] }> {
        const em = this.em.fork();

        const where: FilterQuery<DistributorAuthorizationAttachment>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        if (params?.distributorAuthorizationId) {
            where.push({ distributorAuthorizationId: params.distributorAuthorizationId });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    async createByGrpc(params: CreateInput, options?: { ctx?: Context }): Promise<boolean> {
        const file = await params.file;
        const stream = file.createReadStream();
        const buffer = await streamToBuffer(stream);

        const inputs = new CreateDistributorAuthorizationAttachmentParams({
            distributorAuthorizationId: params.distributorAuthorizationId,
            name: params.name,
            memo: params.memo,
            file: new FileInfo({ ...file, content: buffer }),
            createdUserId: options?.ctx?.currentUser?.id,
        });
        const row = await this.gRPCrepo.create(inputs);

        return row.success;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteDistributorAuthorizationAttachmentParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });
        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }
}
