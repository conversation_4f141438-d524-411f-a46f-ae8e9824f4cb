import { DistributorAuthorizationAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/distributorAuthorizationAttachment.model';
import { IDistributorAuthorizationAttachment } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/distributorAuthorizationAttachment.model';
import { ArgsType, Field, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('DistributorAuthorizationAttachment', {
    implements: IDistributorAuthorizationAttachment,
})
export class TDistributorAuthorizationAttachment extends IDistributorAuthorizationAttachment {}

@ObjectType('PaginatedDistributorAuthorizationAttachments')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [IDistributorAuthorizationAttachment], { nullable: true })
    distributorAuthorizationAttachments: DistributorAuthorizationAttachment[];
}

@InputType('DistributorAuthorizationAttachmentFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field({ nullable: true })
    distributorAuthorizationId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}

