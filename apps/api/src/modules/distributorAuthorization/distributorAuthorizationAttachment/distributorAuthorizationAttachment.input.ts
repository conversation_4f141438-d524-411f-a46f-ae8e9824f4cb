import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { Field, ID, InputType } from 'type-graphql';

@InputType('DistributorAuthorizationAttachmentInput')
export class CommonInput {
    @Field(() => ID, { description: '經销商授權' })
    distributorAuthorizationId: number;

    @Field(() => String, { description: '名称' })
    name: string;

    @Field((type) => GraphQLUpload)
    file: Promise<FileUpload>;

    @Field(() => String, { nullable: true, description: '备注' })
    memo?: string;
}

@InputType('DistributorAuthorizationAttachmentCreateInput')
export class CreateInput extends CommonInput {}
