import { AuthorizationProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/authorizationProduct.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { AuthorizationProductService } from './authorizationProduct.service';
import { PaginatedObjects, SearchArgs, TAuthorizationProduct } from './authorizationProduct.type';

@Service()
@Resolver(() => TAuthorizationProduct)
export class AuthorizationProductResolver {
    @Inject()
    private service: AuthorizationProductService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedAuthorizationProducts' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; authorizationProducts: AuthorizationProduct[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, authorizationProducts: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TAuthorizationProduct], { name: 'authorizationProducts' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<AuthorizationProduct[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TAuthorizationProduct, { name: 'authorizationProduct' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<AuthorizationProduct> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }
}
