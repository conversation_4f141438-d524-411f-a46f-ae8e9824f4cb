import { AuthorizationProduct } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/authorizationProduct.model';
import { IAuthorizationProduct } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/authorizationProduct.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('AuthorizationProduct', { implements: IAuthorizationProduct })
export class TAuthorizationProduct extends IAuthorizationProduct {}

@ObjectType('PaginatedAuthorizationProducts')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TAuthorizationProduct], { nullable: true })
    authorizationProducts: AuthorizationProduct[];
}

@InputType('AuthorizationProductFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field({ nullable: true })
    code?: string;

    @Field({ nullable: true })
    name?: string;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
