import { Field, ID, InputType } from 'type-graphql';

@InputType('DistributorAuthorizationCreateInput')
export class CreateInput {
    @Field((type) => ID, { nullable: false, description: '公司' })
    companyId: number;

    @Field((type) => ID, { nullable: false, description: '客戶(非經銷商)' })
    customerId: number;

    @Field((type) => ID, { nullable: false, description: '客戶(經銷商)' })
    dealerId: number;

    @Field({ nullable: false, description: '項目名稱' })
    projectName: string;

    @Field({ nullable: true, description: '項目編號' })
    projectCode?: string;

    @Field({ nullable: true, description: '計畫編號' })
    planCode?: string;

    @Field({ nullable: true, description: '備註' })
    memo?: string;

    @Field({ description: '授權開始日期' })
    startDate: Date;

    @Field({ description: '授權結束日期' })
    endDate: Date;

    @Field((type) => [ID], { nullable: false, description: '授權商品' })
    authorizationProductIds: number[];
}

@InputType('DistributorAuthorizationUpdateInput')
export class UpdateInput extends CreateInput {
    @Field(() => ID)
    id: number;
}

@InputType('DistributorAuthorizationCancelInput')
export class CancelInput {
    @Field(() => ID)
    distributorAuthorizationId: number;
}

@InputType('DistributorAuthorizationCreateBPMInput')
export class CreateBPMInput {
    @Field(() => ID)
    distributorAuthorizationId: number;
}
