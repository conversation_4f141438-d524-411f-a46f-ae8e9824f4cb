import { PublicFinancialCompany } from '@clinico/mikro-orm-persistence/models/public/financialCompany.model';
import { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import { DistributorAuthorization } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/distributorAuthorization.model';
import { DistributorAuthorizationAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/distributorAuthorizationAttachment.model';
import { URLResolver } from 'graphql-scalars';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { configs } from '~/app.config';
import { EnumPermissionCode } from '~/constants';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { EnumBPMForm } from '~/modules/bpm/formInstance/formInstance.type';
import { CustomerService } from '~/modules/customer/customer/customer.service';
import { TCustomer } from '~/modules/customer/customer/customer.type';
import { FinancialCompanyService } from '~/modules/public/financialCompany/financialCompany.service';
import { TFinancialCompany } from '~/modules/public/financialCompany/financialCompany.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { TAuthorizationProduct } from '../authorizationProduct/authorizationProduct.type';
import { DistributorAuthorizationAttachmentService } from '../distributorAuthorizationAttachment/distributorAuthorizationAttachment.service';
import { TDistributorAuthorizationAttachment } from '../distributorAuthorizationAttachment/distributorAuthorizationAttachment.type';
import { DistributorAuthorizationProductService } from '../distributorAuthorizationProduct/distributorAuthorizationProduct.service';
import {
    CancelInput,
    CreateBPMInput,
    CreateInput,
    UpdateInput,
} from './distributorAuthorization.input';
import { DistributorAuthorizationService } from './distributorAuthorization.service';
import {
    PaginatedObjects,
    SearchArgs,
    TDistributorAuthorization,
} from './distributorAuthorization.type';

@Service()
@Resolver(() => TDistributorAuthorization)
export class DistributorAuthorizationResolver {
    @Inject()
    private service: DistributorAuthorizationService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedDistributorAuthorizations' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; distributorAuthorizations: DistributorAuthorization[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, distributorAuthorizations: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TDistributorAuthorization], { name: 'distributorAuthorizations' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<DistributorAuthorization[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TDistributorAuthorization, { name: 'distributorAuthorization' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<DistributorAuthorization> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['distributor_authorization.create'])
    @Mutation((returns) => TDistributorAuthorization)
    async createDistributorAuthorization(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<DistributorAuthorization> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['distributor_authorization.update'])
    @Mutation((returns) => TDistributorAuthorization)
    async updateDistributorAuthorization(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<DistributorAuthorization> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @Mutation((returns) => TDistributorAuthorization)
    async createDistributorAuthorizationBPM(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateBPMInput
    ): Promise<DistributorAuthorization> {
        return await this.service.createBPMByGrpc(input, { ctx });
    }

    @UserAuthInterceptor(EnumPermissionCode['distributor_authorization.cancel'])
    @Mutation((returns) => Boolean)
    async cancelDistributorAuthorization(
        @Ctx() ctx: Context,
        @Arg('input') input: CancelInput
    ): Promise<boolean> {
        const result = await this.service.cancelByGrpc(input, { ctx });
        return result;
    }

    @Inject()
    private distributorAuthorizationProductService: DistributorAuthorizationProductService;
    @FieldResolver(() => [TAuthorizationProduct])
    async products(@Root() model: DistributorAuthorization): Promise<TAuthorizationProduct[]> {
        const result = await this.distributorAuthorizationProductService.search({
            distributorAuthorizationId: model.id,
        });
        return result.rows.map((el) => el.authorizationProduct);
    }

    @Inject()
    private distributorAuthorizationAttachmentService: DistributorAuthorizationAttachmentService;
    @FieldResolver(() => [TDistributorAuthorizationAttachment], { description: '聯絡人附件' })
    async attachments(
        @Root() model: DistributorAuthorization
    ): Promise<DistributorAuthorizationAttachment[]> {
        const result = await this.distributorAuthorizationAttachmentService.search({
            distributorAuthorizationId: model.id,
        });
        return result.rows;
    }

    @Inject()
    private financialCompanyService: FinancialCompanyService;
    @FieldResolver(() => TFinancialCompany)
    async company(
        @Root() model: DistributorAuthorization
    ): Promise<PublicFinancialCompany | undefined> {
        return await this.financialCompanyService.findOne({ id: model.companyId });
    }
    @FieldResolver(() => URLResolver, { nullable: true, description: 'BPM 連結' })
    async bpmUrl(@Root() model: DistributorAuthorization): Promise<string | undefined> {
        if (!model.bpmInstanceId) return undefined;
        const financialCompany = await this.financialCompanyService.findOne({ id: model.companyId });
        if (!financialCompany) return undefined;

        const url = new URL('/formInstance', configs.bpm.web.host);
        url.searchParams.set(
            'code',
            financialCompany.regionId == 1
                ? EnumBPMForm.DistributorAuthorizationTW
                : EnumBPMForm.DistributorAuthorizationCN
        );
        url.searchParams.set('formInstanceId', model.bpmInstanceId);
        return url.toString();
    }

    @Inject()
    private customerService: CustomerService;
    @FieldResolver(() => TCustomer, { description: '客戶(非經銷商)' })
    async customer(@Root() model: DistributorAuthorization): Promise<Customer | undefined> {
        return await this.customerService.findOne({ id: model.customerId });
    }
    @FieldResolver(() => TCustomer, { description: '客戶(經銷商)' })
    async dealer(@Root() model: DistributorAuthorization): Promise<Customer | undefined> {
        return await this.customerService.findOne({ id: model.dealerId });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { nullable: true })
    async createdUser(@Root() model: DistributorAuthorization): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser, { nullable: true })
    async updatedUser(@Root() model: DistributorAuthorization): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }
}
