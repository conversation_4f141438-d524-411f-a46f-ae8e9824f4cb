import {
    DistributorAuthorization,
    EnumDistributorAuthorizationApprovalStatus,
} from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/distributorAuthorization.model';
import {
    IDistributorAuthorization,
    EnumDistributorAuthorizationApprovalStatus as IEnumDistributorAuthorizationApprovalStatus,
} from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/distributorAuthorization.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('DistributorAuthorization', { implements: IDistributorAuthorization })
export class TDistributorAuthorization extends IDistributorAuthorization {}

@ObjectType('PaginatedDistributorAuthorizations')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TDistributorAuthorization], { nullable: true })
    distributorAuthorizations: DistributorAuthorization[];
}

@InputType('DistributorAuthorizationFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field({ nullable: true, description: '項目名稱' })
    projectName?: string;

    @Field({ nullable: true, description: '項目編號' })
    projectCode?: string;

    @Field({ nullable: true, description: '計畫編號' })
    planCode?: string;

    @Field((type) => ID, { nullable: true, description: '公司' })
    companyId?: number;

    @Field((type) => ID, { nullable: true, description: '客戶(非經銷商)' })
    customerId?: number;

    @Field((type) => ID, { nullable: true, description: '客戶(經銷商)' })
    dealerId?: number;

    @Field({ nullable: true, description: '客戶名稱' })
    customerName?: string;

    @Field({ nullable: true, description: '客戶編號' })
    customerCode?: string;

    @Field({ nullable: true, description: '授權開始日期' })
    startDate1?: Date;

    @Field({ nullable: true, description: '授權開始日期' })
    startDate2?: Date;

    @Field({ nullable: true, description: '授權結束日期' })
    endDate1?: Date;

    @Field({ nullable: true, description: '授權結束日期' })
    endDate2?: Date;

    @Field((type) => IEnumDistributorAuthorizationApprovalStatus, {
        nullable: true,
        description: '簽核狀態(null:新建立)',
    })
    approvalStatus?: EnumDistributorAuthorizationApprovalStatus;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
