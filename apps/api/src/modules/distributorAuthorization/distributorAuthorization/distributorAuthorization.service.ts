import { DistributorAuthorization } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/distributorAuthorization.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import '@packages/erp-protobuf/generated/distributorAuthorization/distributorAuthorization_pb';
import {
    CancelDistributorAuthorizationParams,
    CreateDistributorAuthorizationBPMParams,
    CreateDistributorAuthorizationParams,
    UpdateDistributorAuthorizationParams,
} from '@packages/erp-protobuf/generated/distributorAuthorization/distributorAuthorization_pb';
import { DateOnlyHandler } from '@packages/utils/date';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { DistributorAuthorizationRepo } from './distributorAuthorization.grpc.repo';
import {
    CancelInput,
    CreateBPMInput,
    CreateInput,
    UpdateInput,
} from './distributorAuthorization.input';
import { FilterInput } from './distributorAuthorization.type';

@Service()
export class DistributorAuthorizationService extends BaseService<DistributorAuthorization> {
    protected entity = DistributorAuthorization;
    private gRPCrepo = new DistributorAuthorizationRepo();

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: DistributorAuthorization[] }> {
        const em = this.em.fork();

        const where: FilterQuery<DistributorAuthorization>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        // Filter input
        this.appendFilterByParams(where, params);

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    private appendFilterByParams(
        where: FilterQuery<DistributorAuthorization>[],
        params: FilterInput | undefined
    ) {
        if (params?.projectName) {
            where.push({ projectName: { $ilike: `%${params.projectName}%` } });
        }
        if (params?.projectCode) {
            where.push({ projectCode: { $ilike: `%${params.projectCode}%` } });
        }

        if (params?.customerId) {
            where.push({ customer: { id: params.customerId } });
        }
        if (params?.dealerId) {
            where.push({ customer: { id: params.dealerId } });
        }
        if (params?.customerCode) {
            where.push({ customer: { code: { $ilike: `%${params.customerCode}%` } } });
        }
        if (params?.customerName) {
            where.push({ customer: { name: { $ilike: `%${params.customerName}%` } } });
        }

        if (params?.startDate1) {
            const date = new DateOnlyHandler({ date: params.startDate1 }).toString();
            where.push({ startDate: { $gte: date } });
        }
        if (params?.startDate2) {
            const date = new DateOnlyHandler({ date: params.startDate2 }).toString();
            where.push({ startDate: { $lte: date } });
        }
        if (params?.endDate1) {
            const date = new DateOnlyHandler({ date: params.endDate1 }).toString();
            where.push({ endDate: { $gte: date } });
        }
        if (params?.endDate2) {
            const date = new DateOnlyHandler({ date: params.endDate2 }).toString();
            where.push({ endDate: { $lte: date } });
        }
    }

    async createByGrpc(
        params: CreateInput,
        options?: { ctx?: Context }
    ): Promise<DistributorAuthorization> {
        const inputs = new CreateDistributorAuthorizationParams({
            ...params,
            startDate: handler.toTimestamp({ input: params.startDate }),
            endDate: handler.toTimestamp({ input: params.endDate }),
            createdUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.create(inputs);

        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(
        params: UpdateInput,
        options?: { ctx?: Context }
    ): Promise<DistributorAuthorization> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateDistributorAuthorizationParams({
            ...params,
            id: row.id,
            startDate: handler.toTimestamp({ input: params.startDate }),
            endDate: handler.toTimestamp({ input: params.endDate }),
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async cancelByGrpc(input: CancelInput, options: { ctx: Context }): Promise<boolean> {
        const inputs = new CancelDistributorAuthorizationParams({
            distributorAuthorizationId: input.distributorAuthorizationId,
        });
        const res = await this.gRPCrepo.cancel(inputs);
        return res.success;
    }

    async createBPMByGrpc(
        input: CreateBPMInput,
        options: { ctx: Context }
    ): Promise<DistributorAuthorization> {
        const inputs = new CreateDistributorAuthorizationBPMParams({
            distributorAuthorizationId: input.distributorAuthorizationId,
        });
        const res = await this.gRPCrepo.createBPM(inputs);
        const createdRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return createdRow;
    }
}
