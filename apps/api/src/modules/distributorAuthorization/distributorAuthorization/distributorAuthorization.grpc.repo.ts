import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    CancelDistributorAuthorizationParams,
    CreateDistributorAuthorizationBPMParams,
    CreateDistributorAuthorizationParams,
    DistributorAuthorizationResult,
    RequestResult,
    UpdateDistributorAuthorizationParams,
} from '@packages/erp-protobuf/generated/distributorAuthorization/distributorAuthorization_pb';

@Service()
export class DistributorAuthorizationRepo {
    serviceNodes: string[] = ['distributorAuthorization', 'DistributorAuthorization'];

    async create(
        params: CreateDistributorAuthorizationParams
    ): Promise<DistributorAuthorizationResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<DistributorAuthorizationResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.DistributorAuthorization.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(
        params: UpdateDistributorAuthorizationParams
    ): Promise<DistributorAuthorizationResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<DistributorAuthorizationResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.DistributorAuthorization.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async cancel(params: CancelDistributorAuthorizationParams): Promise<RequestResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<RequestResult>('cancel', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.DistributorAuthorization.cancel]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async createBPM(
        params: CreateDistributorAuthorizationBPMParams
    ): Promise<DistributorAuthorizationResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<DistributorAuthorizationResult>('createBPM', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.DistributorAuthorization.createBPM]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
