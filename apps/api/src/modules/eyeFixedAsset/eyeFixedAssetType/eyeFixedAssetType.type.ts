import type { PublicEyeFixedAssetType } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetType.model';
import { IEyeFixedAssetType } from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeFixedAssetType.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeFixedAssetType', { implements: IEyeFixedAssetType })
export class TEyeFixedAssetType extends IEyeFixedAssetType {}

@ObjectType('PaginatedEyeFixedAssetTypes')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TEyeFixedAssetType], { nullable: true })
    eyeFixedAssetTypes: PublicEyeFixedAssetType[];
}

@InputType('EyeFixedAssetTypeFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true, description: '區域' })
    regionId?: number;

    @Field(() => ID, { nullable: true, description: '眼科固定資產' })
    eyeFixedAssetId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
