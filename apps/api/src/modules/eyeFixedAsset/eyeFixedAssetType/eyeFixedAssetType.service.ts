import { PublicEyeFixedAssetType } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetType.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './eyeFixedAssetType.type';

@Service()
export class EyeFixedAssetTypeService extends BaseService<PublicEyeFixedAssetType> {
    protected entity = PublicEyeFixedAssetType;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: PublicEyeFixedAssetType[] }> {
        const em = this.em.fork();

        const where: FilterQuery<PublicEyeFixedAssetType>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        if (params?.eyeFixedAssetId) {
            where.push({ eyeFixedAssetsTypes: { eyeFixedAsset: { id: params.eyeFixedAssetId } } });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }
}
