import type { PublicEyeFixedAssetType } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetType.model';
import type { PublicRegion } from '@clinico/mikro-orm-persistence/models/public/region.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { RegionService } from '~/modules/public/region/region.service';
import { TRegion } from '~/modules/public/region/region.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { EyeFixedAssetTypeService } from './eyeFixedAssetType.service';
import { PaginatedObjects, SearchArgs, TEyeFixedAssetType } from './eyeFixedAssetType.type';

@Service()
@Resolver(() => TEyeFixedAssetType)
export class EyeFixedAssetTypeResolver {
    @Inject()
    private service: EyeFixedAssetTypeService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeFixedAssetTypes' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeFixedAssetTypes: PublicEyeFixedAssetType[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeFixedAssetTypes: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeFixedAssetType], { name: 'eyeFixedAssetTypes' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeFixedAssetType[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeFixedAssetType, { name: 'eyeFixedAssetType' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeFixedAssetType> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private regionService: RegionService;
    @FieldResolver(() => TRegion)
    async region(@Root() model: PublicEyeFixedAssetType): Promise<PublicRegion | undefined> {
        return await this.regionService.findOne({ id: model.region.id });
    }
}
