import type { EyeServiceOrder } from '@clinico/mikro-orm-persistence/models/maintenance/eyeServiceOrder.model';
import type { PublicEyeFixedAssetRentalRecordLog } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalRecordLog.model';
import { URLResolver } from 'graphql-scalars';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';
import { configs } from '~/app.config';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { EnumBPMForm } from '~/modules/bpm/formInstance/formInstance.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import {
    PaginatedObjects,
    SearchArgs,
    TEyeFixedAssetRentalRecordLog,
} from './eyeFixedAssetRentalRecordLog.type';
import { EyeFixedAssetRentalRecordLogService } from './eyeFixedAssetRentalRecordLog.service';
import { EyeServiceOrderService } from '~/modules/eyeServiceOrder/eyeServiceOrder/eyeServiceOrder.service';
import { TEyeServiceOrder } from '~/modules/eyeServiceOrder/eyeServiceOrder/eyeServiceOrder.type';

@Service()
@Resolver(() => TEyeFixedAssetRentalRecordLog)
export class EyeFixedAssetRentalRecordLogResolver {
    @Inject()
    private service: EyeFixedAssetRentalRecordLogService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeFixedAssetRentalRecordLogs' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeFixedAssetRentalRecordLogs: PublicEyeFixedAssetRentalRecordLog[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeFixedAssetRentalRecordLogs: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeFixedAssetRentalRecordLog], { name: 'eyeFixedAssetRentalRecordLogs' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeFixedAssetRentalRecordLog[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeFixedAssetRentalRecordLog, { name: 'eyeFixedAssetRentalRecordLog' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeFixedAssetRentalRecordLog> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private eyeServiceOrderService: EyeServiceOrderService;
    @FieldResolver(() => TEyeServiceOrder)
    async eyeServiceOrder(
        @Root() model: PublicEyeFixedAssetRentalRecordLog
    ): Promise<EyeServiceOrder | undefined> {
        return await this.eyeServiceOrderService.findOne({ id: model?.eyeServiceOrder?.id });
    }

    @FieldResolver(() => URLResolver, { nullable: true, description: 'BPM 連結' })
    async bpmUrl(@Root() model: TEyeFixedAssetRentalRecordLog): Promise<string | undefined> {
        if (!model.bpmInstanceId) return undefined;

        const url = new URL('/formInstance', configs.bpm.web.host);
        url.searchParams.set('code', EnumBPMForm.FixedAssetRentalUpdateCN);
        url.searchParams.set('formInstanceId', model.bpmInstanceId);
        return url.toString();
    }
}
