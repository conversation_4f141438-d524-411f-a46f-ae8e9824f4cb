import type { PublicEyeFixedAssetRentalRecordLog } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalRecordLog.model';
import {
    EnumEyeFixedAssetRentalRecordLogApprovalStatus,
    IEyeFixedAssetRentalRecordLog,
} from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeFixedAssetRentalRecordLog.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeFixedAssetRentalRecordLog', { implements: IEyeFixedAssetRentalRecordLog })
export class TEyeFixedAssetRentalRecordLog extends IEyeFixedAssetRentalRecordLog {}

@ObjectType('PaginatedEyeFixedAssetRentalRecordLogs')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TEyeFixedAssetRentalRecordLog], { nullable: true })
    eyeFixedAssetRentalRecordLogs: PublicEyeFixedAssetRentalRecordLog[];
}

@InputType('EyeFixedAssetRentalRecordLogFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true, description: '服務單' })
    eyeServiceOrderId?: number;

    approvalStatus?: EnumEyeFixedAssetRentalRecordLogApprovalStatus;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
