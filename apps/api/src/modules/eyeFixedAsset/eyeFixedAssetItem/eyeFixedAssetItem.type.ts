import type { PublicEyeFixedAssetItem } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetItem.model';
import { IEyeFixedAssetItem } from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeFixedAssetItem.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeFixedAssetItem', { implements: IEyeFixedAssetItem })
export class TEyeFixedAssetItem extends IEyeFixedAssetItem {}

@ObjectType('PaginatedEyeFixedAssetItems')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TEyeFixedAssetItem], { nullable: true })
    eyeFixedAssetItems: PublicEyeFixedAssetItem[];
}

@InputType('EyeFixedAssetItemFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true, description: '眼科固定資產' })
    eyeFixedAssetId?: number;

    @Field(() => ID, { nullable: true, description: '產品料件' })
    materialId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
