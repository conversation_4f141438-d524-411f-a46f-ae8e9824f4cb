import type { InventoryMaterial } from '@clinico/mikro-orm-persistence/models/inventory/material.model';
import type { PublicEyeFixedAsset } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAsset.model';
import type { PublicEyeFixedAssetItem } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetItem.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { MaterialService } from '~/modules/material/material/material.service';
import { TMaterial } from '~/modules/material/material/material.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { EyeFixedAssetService } from '../eyeFixedAsset/eyeFixedAsset.service';
import { TEyeFixedAsset } from '../eyeFixedAsset/eyeFixedAsset.type';
import { EyeFixedAssetItemService } from './eyeFixedAssetItem.service';
import { PaginatedObjects, SearchArgs, TEyeFixedAssetItem } from './eyeFixedAssetItem.type';

@Service()
@Resolver(() => TEyeFixedAssetItem)
export class EyeFixedAssetItemResolver {
    @Inject()
    private service: EyeFixedAssetItemService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeFixedAssetItems' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeFixedAssetItems: PublicEyeFixedAssetItem[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeFixedAssetItems: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeFixedAssetItem], { name: 'eyeFixedAssetItems' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeFixedAssetItem[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeFixedAssetItem, { name: 'eyeFixedAssetItem' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeFixedAssetItem> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private eyeFixedAssetService: EyeFixedAssetService;
    @FieldResolver(() => TEyeFixedAsset)
    async eyeFixedAsset(
        @Root() model: PublicEyeFixedAssetItem
    ): Promise<PublicEyeFixedAsset | undefined> {
        return await this.eyeFixedAssetService.findOne({ id: model.eyeFixedAsset.id });
    }

    @Inject()
    private materialService: MaterialService;
    @FieldResolver(() => TMaterial)
    async material(@Root() model: PublicEyeFixedAssetItem): Promise<InventoryMaterial | undefined> {
        return await this.materialService.findOne({ id: model.material.id });
    }
}
