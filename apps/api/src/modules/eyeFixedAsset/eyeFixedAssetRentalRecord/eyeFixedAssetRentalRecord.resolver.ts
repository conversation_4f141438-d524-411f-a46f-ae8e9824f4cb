import type { PublicCompany } from '@clinico/mikro-orm-persistence/models/public/company.model';
import type { PublicEyeFixedAsset } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAsset.model';
import type { PublicEyeFixedAssetRentalRecord } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalRecord.model';
import type { PublicRegion } from '@clinico/mikro-orm-persistence/models/public/region.model';
import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { CompanyService } from '~/modules/public/company/company.service';
import { TCompany } from '~/modules/public/company/company.type';
import { RegionService } from '~/modules/public/region/region.service';
import { TRegion } from '~/modules/public/region/region.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { EyeFixedAssetService } from '../eyeFixedAsset/eyeFixedAsset.service';
import { TEyeFixedAsset } from '../eyeFixedAsset/eyeFixedAsset.type';
import { EyeFixedAssetRentalRecordService } from './eyeFixedAssetRentalRecord.service';
import {
    PaginatedObjects,
    SearchArgs,
    TEyeFixedAssetRentalRecord,
} from './eyeFixedAssetRentalRecord.type';
import { EyeServiceOrderService } from '~/modules/eyeServiceOrder/eyeServiceOrder/eyeServiceOrder.service';
import { TEyeServiceOrder } from '~/modules/eyeServiceOrder/eyeServiceOrder/eyeServiceOrder.type';
import { EyeServiceOrder } from '@clinico/mikro-orm-persistence/models/maintenance/eyeServiceOrder.model';

@Service()
@Resolver(() => TEyeFixedAssetRentalRecord)
export class EyeFixedAssetRentalRecordResolver {
    @Inject()
    private service: EyeFixedAssetRentalRecordService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeFixedAssetRentalRecords' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeFixedAssetRentalRecords: PublicEyeFixedAssetRentalRecord[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeFixedAssetRentalRecords: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeFixedAssetRentalRecord], { name: 'eyeFixedAssetRentalRecords' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeFixedAssetRentalRecord[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeFixedAssetRentalRecord, { name: 'eyeFixedAssetRentalRecord' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeFixedAssetRentalRecord> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private regionService: RegionService;
    @FieldResolver(() => TRegion)
    async region(
        @Root() model: PublicEyeFixedAssetRentalRecord
    ): Promise<PublicRegion | undefined> {
        return await this.regionService.findOne({ id: model.regionId });
    }

    @Inject()
    private companyService: CompanyService;
    @FieldResolver(() => TCompany)
    async company(
        @Root() model: PublicEyeFixedAssetRentalRecord
    ): Promise<PublicCompany | undefined> {
        return await this.companyService.findOne({ id: model.companyId });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { nullable: true })
    async createdUser(
        @Root() model: PublicEyeFixedAssetRentalRecord
    ): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser, { nullable: true })
    async updatedUser(
        @Root() model: PublicEyeFixedAssetRentalRecord
    ): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }

    @Inject()
    private eyeFixedAssetService: EyeFixedAssetService;
    @FieldResolver(() => [TEyeFixedAsset])
    async eyeFixedAssets(
        @Root() model: PublicEyeFixedAssetRentalRecord
    ): Promise<PublicEyeFixedAsset[]> {
        const { rows } = await this.eyeFixedAssetService.search({
            eyeFixedAssetRentalRecordId: model.id,
        });
        return rows;
    }

    @Inject()
    private eyeServiceOrderService: EyeServiceOrderService;
    @FieldResolver((returns) =>  TEyeServiceOrder, { nullable: true })
    async eyeServiceOrder(
        @Root() model: PublicEyeFixedAssetRentalRecord
    ): Promise<EyeServiceOrder |  undefined> {
        return await this.eyeServiceOrderService.findOne({            
            id: model.eyeServiceOrderId,
        });
    }
}
