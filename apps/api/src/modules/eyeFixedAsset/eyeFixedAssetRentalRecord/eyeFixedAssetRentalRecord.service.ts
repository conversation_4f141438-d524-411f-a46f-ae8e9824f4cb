import { EyeFixedAssetRentalRecord } from './../../../../../../packages/erp-protobuf/generated/eyeServiceOrder/eyeServiceOrder_pb';
import {
    EnumEyeFixedAssetsRentalStatus,
    PublicEyeFixedAssetRentalRecord,
} from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalRecord.model';
import { FilterQuery, QueryOrderMap } from '@mikro-orm/core';
import { Service } from 'typedi';

import { DateOnlyHandler } from '@packages/utils/date';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './eyeFixedAssetRentalRecord.type';

@Service()
export class EyeFixedAssetRentalRecordService extends BaseService<PublicEyeFixedAssetRentalRecord> {
    protected entity = PublicEyeFixedAssetRentalRecord;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean; orderBy?: QueryOrderMap<PublicEyeFixedAssetRentalRecord> }
    ): Promise<{ count: number | null; rows: PublicEyeFixedAssetRentalRecord[] }> {
        const em = this.em.fork();

        const where: FilterQuery<PublicEyeFixedAssetRentalRecord>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        if (params?.customerId) {
            where.push({ eyeServiceOrder: { customer: { id: params.customerId } } });
        }
        if (params?.eyeFixedAssetId) {
            where.push({
                eyeFixedAssetRentalRecordItems: { eyeFixedAsset: { id: params.eyeFixedAssetId } },
            });
        }
        if (params?.eyeFixedAssetIds) {
            where.push({
                eyeFixedAssetRentalRecordItems: { eyeFixedAsset: { id: { $in: params.eyeFixedAssetIds } } },
            });
        }
        if (params?.eyeServiceOrderId) {
            where.push({ eyeServiceOrder: { id: params.eyeServiceOrderId } });
        }
        if (params?.rentalDate1) {
            const date = new DateOnlyHandler({ date: params.rentalDate1 }).toString();
            where.push({ date2: { $gte: date } });
        }
        if (params?.rentalDate2) {
            const date = new DateOnlyHandler({ date: params.rentalDate2 }).toString();
            where.push({ date1: { $lte: date } });
        }
        if (params?.statuses) {
            where.push({ status: { $in: params.statuses } });
        }

        // 服务单当前租借日
        if (params?.currentDate1) {
            where.push({ date2: { $lt: params.currentDate1 } });
        }

        if (params?.currentDate2) {
            where.push({ date2: { $gt: params.currentDate2 } });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const defaultOrder: QueryOrderMap<PublicEyeFixedAssetRentalRecord> = {
            date1: 'DESC',
            date2: 'DESC',
            id: 'DESC',
        };

        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                // orderBy: [{ id: 'DESC' }],
                orderBy: options?.orderBy ?? defaultOrder,
                populate: ['createdUser', 'updatedUser', 'eyeFixedAssetRentalRecordItems'],
            }
        );

        return { rows, count };
    }

    /** 目前租借中的租借單 */
    async findOneByCurr(params: {
        eyeFixedAssetId: number;
    }): Promise<PublicEyeFixedAssetRentalRecord | null> {
        const { rows } = await this.search(
            {
                eyeFixedAssetIds: [params.eyeFixedAssetId],
                status: EnumEyeFixedAssetsRentalStatus.Booking,
            },
            { pagination: { limit: 1 }, orderBy: { date1: 'ASC', id: 'DESC' } },
        );
        return rows[0];
    }

    /** 上一個租借單 */
    async findOneByPrev(params: {
        eyeFixedAssetId: number;
        currentRentalDate: Date;
    }): Promise<PublicEyeFixedAssetRentalRecord | null> {
        const { rows } = await this.search(
            {
                eyeFixedAssetIds: [params.eyeFixedAssetId],
                currentDate1: params.currentRentalDate,
            },
            { pagination: { limit: 1 }, orderBy: { date1: 'DESC', id: 'DESC' } },
        );
        return rows[0];
    }

    /** 下一個租借單 */
    async findOneByNext(params: {
        eyeFixedAssetId: number;
        currentRentalDate: Date;
    }): Promise<PublicEyeFixedAssetRentalRecord | null> {
        const { rows } = await this.search(
            {
                eyeFixedAssetIds: [params.eyeFixedAssetId],
                currentDate2: params.currentRentalDate,
            },
            { pagination: { limit: 1 }, orderBy: { date1: 'ASC', id: 'DESC' } },
        );
        return rows[0];
    }

    /** 取得最近可借日期 */
    async getCurrentAvailableRentalDate(params: {
        eyeFixedAssetId: number;
    }): Promise<Date> {
        const { rows } = await this.search(
            {
                eyeFixedAssetIds: [params.eyeFixedAssetId],
                statuses: [
                    EnumEyeFixedAssetsRentalStatus.Waiting,
                    EnumEyeFixedAssetsRentalStatus.Booking,
                    EnumEyeFixedAssetsRentalStatus.Returned,
                ],
            },
            { pagination: { limit: 1 }, orderBy: { date2: 'DESC', id: 'DESC' } },
        );
        // 如果数据未找到，则返回1970-01-01
        let nearestAvailableDate = rows[0]?.date2 ?? new Date(0);
        nearestAvailableDate.setDate(nearestAvailableDate.getDate() + 1);

        // 如果最近可借日期小于当前日期，则将其设置为当前日期
        if (nearestAvailableDate < new Date()) {
            nearestAvailableDate = new Date();
        }

        return nearestAvailableDate;
    }

    async isBooking(params: { id: number }): Promise<boolean> {
        const { count } = await this.search(
            { eyeFixedAssetId: params.id, status: EnumEyeFixedAssetsRentalStatus.Booking },
            { pagination: { limit: 1 }, count: true }
        );
        return (count ?? 0) > 0 ? true : false;
    }
}
