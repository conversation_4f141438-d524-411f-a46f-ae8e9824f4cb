import type { PublicEyeFixedAssetRentalRecord } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalRecord.model';
import {
    EnumEyeFixedAssetsRentalStatus,
    IEyeFixedAssetRentalRecord,
} from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeFixedAssetRentalRecord.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeFixedAssetRentalRecord', { implements: IEyeFixedAssetRentalRecord })
export class TEyeFixedAssetRentalRecord extends IEyeFixedAssetRentalRecord {}

@ObjectType('PaginatedEyeFixedAssetRentalRecords')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TEyeFixedAssetRentalRecord], { nullable: true })
    eyeFixedAssetRentalRecords: PublicEyeFixedAssetRentalRecord[];
}

@InputType('EyeFixedAssetRentalRecordFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    customerId?: number;

    @Field(() => ID, { nullable: true, description: '眼科服務單' })
    eyeServiceOrderId?: number;

    @Field(() => ID, { nullable: true, description: '眼科固定資產' })
    eyeFixedAssetId?: number;

    @Field(() => [ID], { nullable: true, description: '眼科固定資產(复数)' })
    eyeFixedAssetIds?: number[];

    @Field(() => EnumEyeFixedAssetsRentalStatus, { nullable: true, description: '狀態' })
    status?: EnumEyeFixedAssetsRentalStatus;

    @Field(() => String, { nullable: true, description: '租借日期（起）' })
    rentalDate1?: string;

    @Field(() => String, { nullable: true, description: '租借日期（迄）' })
    rentalDate2?: string;

    @Field(() => Date, { nullable: true, description: '当前租借日期（起）' })
    currentDate1?: Date;

    @Field(() => Date, { nullable: true, description: '当前租借日期（迄）' })
    currentDate2?: Date;

    @Field((type) => [EnumEyeFixedAssetsRentalStatus], { nullable: true })
    statuses?: EnumEyeFixedAssetsRentalStatus[];

}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
