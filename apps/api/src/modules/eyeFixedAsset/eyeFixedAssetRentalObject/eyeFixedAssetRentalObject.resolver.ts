import type { PublicEyeFixedAssetRentalObject } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalObject.model';
import type { PublicRegion } from '@clinico/mikro-orm-persistence/models/public/region.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { RegionService } from '~/modules/public/region/region.service';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { TEyeFixedAsset } from '../eyeFixedAsset/eyeFixedAsset.type';
import { EyeFixedAssetRentalObjectService } from './eyeFixedAssetRentalObject.service';
import {
    PaginatedObjects,
    SearchArgs,
    TEyeFixedAssetRentalObject,
} from './eyeFixedAssetRentalObject.type';

@Service()
@Resolver(() => TEyeFixedAssetRentalObject)
export class EyeFixedAssetRentalObjectResolver {
    @Inject()
    private service: EyeFixedAssetRentalObjectService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeFixedAssetRentalObjects' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeFixedAssetRentalObjects: PublicEyeFixedAssetRentalObject[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeFixedAssetRentalObjects: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeFixedAssetRentalObject], { name: 'eyeFixedAssetRentalObjects' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeFixedAssetRentalObject[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeFixedAssetRentalObject, { name: 'eyeFixedAssetRentalObject' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeFixedAssetRentalObject> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private regionService: RegionService;
    @FieldResolver(() => TEyeFixedAsset)
    async region(
        @Root() model: PublicEyeFixedAssetRentalObject
    ): Promise<PublicRegion | undefined> {
        return await this.regionService.findOne({ id: model.regionId });
    }
}
