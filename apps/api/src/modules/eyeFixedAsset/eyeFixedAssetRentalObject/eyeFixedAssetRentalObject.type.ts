import type { PublicEyeFixedAssetRentalObject } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalObject.model';
import { IEyeFixedAssetRentalObject } from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeFixedAssetRentalObject.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeFixedAssetRentalObject', { implements: IEyeFixedAssetRentalObject })
export class TEyeFixedAssetRentalObject extends IEyeFixedAssetRentalObject {}

@ObjectType('PaginatedEyeFixedAssetRentalObjects')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TEyeFixedAssetRentalObject], { nullable: true })
    eyeFixedAssetRentalObjects: PublicEyeFixedAssetRentalObject[];
}

@InputType('EyeFixedAssetRentalObjectFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
