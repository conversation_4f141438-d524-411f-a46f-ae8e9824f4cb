import { PublicEyeFixedAsset } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAsset.model';
import { PublicEyeFixedAssetRentalRecord } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalRecord.model';
import { FilterQuery } from '@mikro-orm/core';
import _ from 'lodash';
import { Service } from 'typedi';

import { DateOnlyHandler } from '@packages/utils/date';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './eyeFixedAsset.type';
import { EnumEyeFixedAssetTypeCode } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetType.model';

@Service()
export class EyeFixedAssetService extends BaseService<PublicEyeFixedAsset> {
    protected entity = PublicEyeFixedAsset;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: PublicEyeFixedAsset[] }> {
        const em = this.em.fork();
        const where: FilterQuery<PublicEyeFixedAsset>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
       
        where.push({ deleted: false });

        // Access control
        const accesses: FilterQuery<PublicEyeFixedAsset> = [];

        // For permissions
        const allowEyeFixedAssetsServiceProvidersIds =
            options?.ctx?.currentPermission?.allowEyeFixedAssetsServiceProvidersIds || [];
        if (allowEyeFixedAssetsServiceProvidersIds.length) {
            accesses.push({
                eyeFixedAssetsServiceProviders: {
                    eyeFixedAssetServiceProviderId: { $in: allowEyeFixedAssetsServiceProvidersIds },
                },
            });
        }
        where.push({ $and: accesses });

        // Filter input
        this.appendFilterByParams(where, params);
         if (params?.departmentIds && params.departmentIds.length) {
            const ids = await this.district(params.departmentIds);
            where.push({
                id: { $in:ids }
            });
        }
        // Exclude rented
        await this.appendFilterToExcludeRented(where, params);

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
                populate: ['dept', 'createdUser', 'updatedUser'],
            }
        );

        return { rows, count };
    }

    private appendFilterByParams(
        where: FilterQuery<PublicEyeFixedAsset>[],
        params: FilterInput | undefined
    ) {
        if (params?.eyeFixedAssetRentalRecordId) {
            where.push({
                eyeFixedAssetRentalRecordItems: {
                    eyeFixedAssetRentalRecord: { id: params.eyeFixedAssetRentalRecordId },
                },
            });
        }
        if (params?.typeIds) {
            where.push({
                eyeFixedAssetsTypes: {
                    eyeFixedAssetType: { id: { $in: params.typeIds } },
                },
            });
        }
        if (params?.sn) {
            where.push({ eyeFixedAssetItems: { sn: { $ilike: `%${params.sn}%` } } });
        }
        if (params?.name) {
            where.push({ name: { $ilike: `%${params.name}%` } });
        }
        if (params?.materialName) {
            where.push({
                eyeFixedAssetItems: { materialName: { $ilike: `%${params.materialName}%` } },
            });
        }
        if (params?.materialCode) {
            where.push({
                eyeFixedAssetItems: { materialCode: { $ilike: `%${params.materialCode}%` } },
            });
        }
        
    }

    async appendFilterToExcludeRented(
        where: FilterQuery<PublicEyeFixedAsset>[],
        params?: FilterInput
    ) {
        /**
         * ISSUE: 使用 Array 型態的 filters，會產生多次 `LEFT JOIN` 而得到非預期的結果。
         *
         * 範例：
         * SELECT "p0"."id"
         * FROM      "eye_fixed_assets"                    AS "p0"
         * LEFT JOIN "eye_fixed_asset_rental_record_items" AS "p1" ON "p0"."id" = "p1"."eye_fixed_asset_id"
         * LEFT JOIN "eye_fixed_asset_rental_records"      AS "p2" ON "p1"."eye_fixed_asset_rental_record_id" = "p2"."id"
         * LEFT JOIN "eye_fixed_asset_rental_record_items" AS "p3" ON "p0"."id" = "p3"."eye_fixed_asset_id"
         * LEFT JOIN "eye_fixed_asset_rental_records"      AS "p4" ON "p3"."eye_fixed_asset_rental_record_id" = "p4"."id"
         * LEFT JOIN "eye_fixed_asset_rental_record_items" AS "p5" ON "p0"."id" = "p5"."eye_fixed_asset_id"
         * LEFT JOIN "eye_fixed_asset_rental_records"      AS "p6" ON "p5"."eye_fixed_asset_rental_record_id" = "p6"."id"
         * WHERE "p2"."date2" >= '2023-08-25'
         *   AND "p4"."date1" <= '2023-08-25'
         *   AND "p6"."deleted" = FALSE;
         */
        const filters: FilterQuery<PublicEyeFixedAssetRentalRecord> = {};

        // Append params to the filters
        if (params?.availableRentalDate1) {
            const date = new DateOnlyHandler({ date: params.availableRentalDate1 }).toString();
            filters.date1 = { $lte: date };
        }
        if (params?.availableRentalDate2) {
            const date = new DateOnlyHandler({ date: params.availableRentalDate2 }).toString();
            filters.date2 = { $gte: date };
        }

        // Terminate the method if no filter is added
        if (_.isEmpty(filters)) {
            return;
        }

        // Add default params to the filters
        filters.deleted = false;

        const em = this.em.fork();
        const repo = em.getRepository(this.entity);
        const rows = await repo.find(
            { eyeFixedAssetRentalRecordItems: { eyeFixedAssetRentalRecord: filters } },
            { fields: ['id'] }
        );

        where.push({ id: { $nin: rows.map((row) => row.id) } });
    }


    async district(deptIds: number[]): Promise<number[]> {
        const em = this.em.fork();
        const repo = em.getRepository(this.entity);
        const results =  repo.createQueryBuilder('efa')
            .select(['efa.id'])
            .leftJoinAndSelect(
                'efa.eyeFixedAssetDepartments',
                'efad',
            )
            .innerJoinAndSelect(
                'efa.eyeFixedAssetsTypes',
                'efast',
            )
            .innerJoinAndSelect(
                'efast.eyeFixedAssetType',
                'efat',
            )
            .where({ 'efat.code': EnumEyeFixedAssetTypeCode.DemoMachines })
            .andWhere({ 'efad.department_id': { $in: deptIds } })
            .orWhere({ 'efat.code': EnumEyeFixedAssetTypeCode.GeneralFixedAssets })
            .getResultList();
        return (await results).map((el) => el.id);
    }

}
