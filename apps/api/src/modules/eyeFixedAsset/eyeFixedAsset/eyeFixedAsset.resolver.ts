import type { PublicCompany } from '@clinico/mikro-orm-persistence/models/public/company.model';
import type { PublicDepartment } from '@clinico/mikro-orm-persistence/models/public/department.model';
import type { PublicEyeFixedAsset } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAsset.model';
import type { PublicEyeFixedAssetItem } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetItem.model';
import type { PublicEyeFixedAssetRentalRecord } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalRecord.model';
import type { PublicRegion } from '@clinico/mikro-orm-persistence/models/public/region.model';
import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import { DateResolver } from 'graphql-scalars';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { CompanyService } from '~/modules/public/company/company.service';
import { TCompany } from '~/modules/public/company/company.type';
import { RegionService } from '~/modules/public/region/region.service';
import { TRegion } from '~/modules/public/region/region.type';
import { DepartmentService } from '~/modules/user/department/department.service';
import { TDepartment } from '~/modules/user/department/department.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { EyeFixedAssetItemService } from '../eyeFixedAssetItem/eyeFixedAssetItem.service';
import { TEyeFixedAssetItem } from '../eyeFixedAssetItem/eyeFixedAssetItem.type';
import { EyeFixedAssetRentalRecordService } from '../eyeFixedAssetRentalRecord/eyeFixedAssetRentalRecord.service';
import { TEyeFixedAssetRentalRecord } from '../eyeFixedAssetRentalRecord/eyeFixedAssetRentalRecord.type';
import { TEyeFixedAssetServiceProvider } from '../eyeFixedAssetServiceProvider/eyeFixedAssetServiceProvider.type';
import { EyeFixedAssetTypeService } from '../eyeFixedAssetType/eyeFixedAssetType.service';
import { TEyeFixedAssetType } from '../eyeFixedAssetType/eyeFixedAssetType.type';
import { EyeFixedAssetService } from './eyeFixedAsset.service';
import { PaginatedObjects, SearchArgs, TEyeFixedAsset } from './eyeFixedAsset.type';
import { PublicEyeFixedAssetServiceProvider } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetServiceProvider.model';

@Service()
@Resolver(() => TEyeFixedAsset)
export class EyeFixedAssetResolver {
    @Inject()
    private service: EyeFixedAssetService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeFixedAssets' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeFixedAssets: PublicEyeFixedAsset[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeFixedAssets: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeFixedAsset], { name: 'eyeFixedAssets' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeFixedAsset[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeFixedAsset, { name: 'eyeFixedAsset' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeFixedAsset> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private regionService: RegionService;
    @FieldResolver(() => TRegion)
    async region(@Root() model: PublicEyeFixedAsset): Promise<PublicRegion | undefined> {
        return await this.regionService.findOne({ id: model.regionId });
    }

    @Inject()
    private companyService: CompanyService;
    @FieldResolver(() => TCompany)
    async company(@Root() model: PublicEyeFixedAsset): Promise<PublicCompany | undefined> {
        return await this.companyService.findOne({ id: model.companyId });
    }

    @Inject()
    private departmentService: DepartmentService;
    @FieldResolver(() => TDepartment, {
        nullable: true,
        description: '保管部門（於 issues/101545 棄用）',
    })
    async dept(@Root() model: PublicEyeFixedAsset): Promise<PublicDepartment | undefined> {
        return await this.departmentService.findOne({ id: model.deptId });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, {
        nullable: true,
        description: '保管人員（於 issues/101545 棄用）',
    })
    async user(@Root() model: PublicEyeFixedAsset): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.userId });
    }
    @FieldResolver(() => TUser, { nullable: true, description: 'PM 負責人員' })
    async pmUser(@Root() model: PublicEyeFixedAsset): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.pmUserId });
    }
    @FieldResolver(() => TUser, { nullable: true })
    async createdUser(@Root() model: PublicEyeFixedAsset): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser, { nullable: true })
    async updatedUser(@Root() model: PublicEyeFixedAsset): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }

    @Inject()
    private eyeFixedAssetRentalRecordService: EyeFixedAssetRentalRecordService;
    @FieldResolver(() => [TEyeFixedAssetRentalRecord])
    async eyeFixedAssetRentalRecords(
        @Root() model: PublicEyeFixedAsset
    ): Promise<PublicEyeFixedAssetRentalRecord[]> {
        const { rows } = await this.eyeFixedAssetRentalRecordService.search({
            eyeFixedAssetId: model.id,
        });
        return rows;
    }

    @FieldResolver((returns) => TEyeFixedAssetRentalRecord, {
        nullable: true,
        description: '目前「租借中」的租借單',
    })
    async currEyeFixedAssetRentalRecord(
        @Root() model: PublicEyeFixedAsset
    ): Promise<PublicEyeFixedAssetRentalRecord | null> {
        const row = await this.eyeFixedAssetRentalRecordService.findOneByCurr({
            eyeFixedAssetId: model.id,
        });
        return row;
    }

    @FieldResolver((returns) => DateResolver, { description: '最近可借日期' })
    async currentAvailableRentalDate(@Root() model: PublicEyeFixedAsset): Promise<Date> {
        const row = await this.eyeFixedAssetRentalRecordService.getCurrentAvailableRentalDate({
            eyeFixedAssetId: model.id,
        });
        return row;
    }

    @FieldResolver((returns) => TEyeFixedAssetRentalRecord, {
        nullable: true,
        description: '上一個的租借單',
    })
    async prevEyeFixedAssetRentalRecord(
        @Root() model: PublicEyeFixedAsset
    ): Promise<PublicEyeFixedAssetRentalRecord | null> {
        const currentAvailableRentalDate =
            await this.eyeFixedAssetRentalRecordService.getCurrentAvailableRentalDate({
                eyeFixedAssetId: model.id,
            });
        const row = await this.eyeFixedAssetRentalRecordService.findOneByPrev({
            eyeFixedAssetId: model.id,
            currentRentalDate: currentAvailableRentalDate,
        });
        return row;
    }

    @FieldResolver(() => Boolean, { description: '是否被預約' })
    async isBooking(@Root() model: PublicEyeFixedAsset): Promise<boolean> {
        const has = await this.eyeFixedAssetRentalRecordService.isBooking({ id: model.id });
        return has;
    }

    @Inject()
    private eyeFixedAssetItemService: EyeFixedAssetItemService;
    @FieldResolver(() => [TEyeFixedAssetItem])
    async eyeFixedAssetItems(
        @Root() model: PublicEyeFixedAsset
    ): Promise<PublicEyeFixedAssetItem[]> {
        const { rows } = await this.eyeFixedAssetItemService.search({ eyeFixedAssetId: model.id });
        return rows;
    }

    @Inject()
    private eyeFixedAssetTypeService: EyeFixedAssetTypeService;
    @FieldResolver(() => [TEyeFixedAssetType], { description: '類別' })
    async types(@Root() model: PublicEyeFixedAsset): Promise<TEyeFixedAssetType[]> {
        const { rows } = await this.eyeFixedAssetTypeService.search({ eyeFixedAssetId: model.id });
        return rows;
    }

    @FieldResolver(() => [TDepartment], { nullable: true, description: '区域' })
    async departments(@Root() model: PublicEyeFixedAsset): Promise<PublicDepartment[]> {
        if (!model.eyeFixedAssetDepartments.isInitialized()) {
            await model.eyeFixedAssetDepartments.init({
                populate: ['department'],
                where: { department: { isActive: true } },
            });
        }
        return model.eyeFixedAssetDepartments.getItems().map((v) => v.department);
    }

    @FieldResolver((returns) => [TEyeFixedAssetServiceProvider], {
        nullable: true,
        description: '服務單位',
    })
    async eyeFixedAssetServiceProvider(
        @Root() model: PublicEyeFixedAsset
    ): Promise<PublicEyeFixedAssetServiceProvider[]> {
        if (!model.eyeFixedAssetsServiceProviders.isInitialized()) {
            await model.eyeFixedAssetsServiceProviders.init({
                populate: ['eyeFixedAssetServiceProvider'],
                where: { eyeFixedAssetId: model.id },
            });
        }
        return model.eyeFixedAssetsServiceProviders.getItems().map((v) => v.eyeFixedAssetServiceProvider);
    }
}
