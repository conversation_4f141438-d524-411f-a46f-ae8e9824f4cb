import type { PublicEyeFixedAsset } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAsset.model';
import {
    EnumEyeFixedAssetsStatus,
    IEyeFixedAsset,
} from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeFixedAsset.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeFixedAsset', { implements: IEyeFixedAsset })
export class TEyeFixedAsset extends IEyeFixedAsset {}

@ObjectType('PaginatedEyeFixedAssets')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TEyeFixedAsset], { nullable: true })
    eyeFixedAssets: PublicEyeFixedAsset[];
}

@InputType('EyeFixedAssetFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true, description: '眼科固定資產租借紀錄' })
    eyeFixedAssetRentalRecordId?: number;

    @Field(() => [ID], { nullable: true, description: '類型' })
    typeIds?: number[];

    @Field(() => String, { nullable: true, description: '名稱' })
    name?: string;

    @Field(() => String, { nullable: true, description: '料件序號' })
    sn?: string;

    @Field(() => String, { nullable: true, description: '料件名稱' })
    materialName?: string;

    @Field(() => String, { nullable: true, description: '料件編號' })
    materialCode?: string;

    @Field(() => EnumEyeFixedAssetsStatus, { nullable: true, description: '狀態' })
    status?: EnumEyeFixedAssetsStatus;

    @Field(() => String, { nullable: true, description: '可租借日期（起）' })
    availableRentalDate1?: string;

    @Field(() => String, { nullable: true, description: '可租借日期（迄）' })
    availableRentalDate2?: string;

    @Field(() => [ID], { nullable: true, description: '眼科固定資產服務單位' })
    eyeFixedAssetsServiceProviderIds?: number[];

    @Field(() => [ID], { nullable: true, description: '区域' })
    departmentIds?: number[];
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
