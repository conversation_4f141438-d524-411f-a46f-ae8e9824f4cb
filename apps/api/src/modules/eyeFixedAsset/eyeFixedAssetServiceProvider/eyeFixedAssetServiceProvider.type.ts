import { IEyeFixedAssetServiceProvider } from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeFixedAssetServiceProvider.model';
import { ArgsType, Field, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs } from '~/utils/types/base.type';

@ObjectType('EyeFixedAssetServiceProvider', { implements: IEyeFixedAssetServiceProvider })
export class TEyeFixedAssetServiceProvider extends IEyeFixedAssetServiceProvider {}

@InputType('EyeFixedAssetServiceProviderFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
