
import type { PublicRegion } from '@clinico/mikro-orm-persistence/models/public/region.model';
import { Arg, Args, Ctx, FieldResolver, ID, Info, Query, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { RegionService } from '~/modules/public/region/region.service';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { TEyeFixedAsset } from '../eyeFixedAsset/eyeFixedAsset.type';
import { EyeFixedAssetRentalGoalService } from './eyeFixedAssetRentalGoal.service';
import { PaginatedObjects, SearchArgs, TEyeFixedAssetRentalGoal } from './eyeFixedAssetRentalGoal.type';
import { PublicEyeFixedAssetRentalGoal } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalGoal.model';

@Service()
@Resolver(() => TEyeFixedAssetRentalGoal)
export class EyeFixedAssetRentalGoalResolver {
    @Inject()
    private service: EyeFixedAssetRentalGoalService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedEyeFixedAssetRentalGoals' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; eyeFixedAssetRentalGoals: PublicEyeFixedAssetRentalGoal[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, eyeFixedAssetRentalGoals: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TEyeFixedAssetRentalGoal], { name: 'eyeFixedAssetRentalGoals' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<PublicEyeFixedAssetRentalGoal[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TEyeFixedAssetRentalGoal, { name: 'eyeFixedAssetRentalGoal' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<PublicEyeFixedAssetRentalGoal> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @Inject()
    private regionService: RegionService;
    @FieldResolver(() => TEyeFixedAsset)
    async region(
        @Root() model: PublicEyeFixedAssetRentalGoal
    ): Promise<PublicRegion | undefined> {
        return await this.regionService.findOne({ id: model.regionId });
    }
}
