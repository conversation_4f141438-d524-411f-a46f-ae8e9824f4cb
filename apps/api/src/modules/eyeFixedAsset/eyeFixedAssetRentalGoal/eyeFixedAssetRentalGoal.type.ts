import type { PublicEyeFixedAssetRentalGoal } from '@clinico/mikro-orm-persistence/models/public/eyeFixedAssetRentalGoal.model';
import { IEyeFixedAssetRentalGoal } from '@clinico/mikro-orm-type-graphql-persistence/models/public/eyeFixedAssetRentalGoal.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('EyeFixedAssetRentalGoal', { implements: IEyeFixedAssetRentalGoal })
export class TEyeFixedAssetRentalGoal extends IEyeFixedAssetRentalGoal {}

@ObjectType('PaginatedEyeFixedAssetRentalGoals')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TEyeFixedAssetRentalGoal], { nullable: true })
    eyeFixedAssetRentalGoals: PublicEyeFixedAssetRentalGoal[];
}

@InputType('EyeFixedAssetRentalGoalFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}