import { CustomerEquipmentAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipmentAttachment.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import {
    CreateCustomerEquipmentAttachmentParams,
    DeleteCustomerEquipmentAttachmentParams,
    FileInfo,
    UpdateCustomerEquipmentAttachmentParams,
} from '@packages/erp-protobuf/generated/customer/customerEquipmentAttachment_pb';

import { streamToBuffer } from '~/utils/helpers/fileHandler.helper';
import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CustomerEquipmentAttachmentGrpcRepo } from './customerEquipmentAttachment.grpc.repo';
import { CreateInput, UpdateInput } from './customerEquipmentAttachment.input';
import { FilterInput } from './customerEquipmentAttachment.type';

@Service()
export class CustomerEquipmentAttachmentService extends BaseService<CustomerEquipmentAttachment> {
    protected entity = CustomerEquipmentAttachment;
    private gRPCrepo = new CustomerEquipmentAttachmentGrpcRepo();

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: CustomerEquipmentAttachment[] }> {
        const em = this.em.fork();

        const where: FilterQuery<CustomerEquipmentAttachment>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        if (params?.customerId) {
            where.push({ equipment: { customer: { id: params.customerId } } });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    async createByGrpc(
        params: CreateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerEquipmentAttachment> {
        const file = await params.file;
        const stream = file.createReadStream();
        const buffer = await streamToBuffer(stream);

        const inputs = new CreateCustomerEquipmentAttachmentParams({
            equipmentId: params.equipmentId,
            name: params.name || file.filename,
            file: new FileInfo({ ...file, content: buffer }),
            createdUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.create(inputs);
        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(
        params: UpdateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerEquipmentAttachment> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateCustomerEquipmentAttachmentParams({
            id: row.id,
            name: handler.toString({ input: params.name, default: row.name }),
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteCustomerEquipmentAttachmentParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }
}
