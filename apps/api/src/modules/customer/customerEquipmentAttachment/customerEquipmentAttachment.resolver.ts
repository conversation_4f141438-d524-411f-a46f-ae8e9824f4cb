import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { CustomerEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipment.model';
import type { CustomerEquipmentAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipmentAttachment.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { createFileBaseResolver } from '~/utils/providers/fileBase.resolver';

import { CustomerEquipmentService } from '../customerEquipment/customerEquipment.service';
import { TCustomerEquipment } from '../customerEquipment/customerEquipment.type';
import { CreateInput, UpdateInput } from './customerEquipmentAttachment.input';
import { CustomerEquipmentAttachmentService } from './customerEquipmentAttachment.service';
import {
    PaginatedObjects,
    SearchArgs,
    TCustomerEquipmentAttachment,
} from './customerEquipmentAttachment.type';

const FileBaseResolver = createFileBaseResolver(TCustomerEquipmentAttachment);

@Service()
@Resolver(() => TCustomerEquipmentAttachment)
export class CustomerEquipmentAttachmentResolver extends FileBaseResolver {
    @Inject()
    private service: CustomerEquipmentAttachmentService;
    @Inject()
    private customerEquipmentService: CustomerEquipmentService;
    @Inject()
    private userService: UserService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomerEquipmentAttachments' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerEquipmentAttachments: CustomerEquipmentAttachment[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerEquipmentAttachments: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomerEquipmentAttachment], { name: 'customerEquipmentAttachments' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<CustomerEquipmentAttachment[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomerEquipmentAttachment, { name: 'customerEquipmentAttachment' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<CustomerEquipmentAttachment> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerEquipmentAttachment, { name: 'createCustomerEquipmentAttachment' })
    async create(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<CustomerEquipmentAttachment> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerEquipmentAttachment, { name: 'updateCustomerEquipmentAttachment' })
    async update(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<CustomerEquipmentAttachment> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteCustomerEquipmentAttachment' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @FieldResolver(() => TCustomerEquipment, { description: '設備', nullable: true })
    async equipment(
        @Root() model: CustomerEquipmentAttachment
    ): Promise<CustomerEquipment | undefined> {
        return this.customerEquipmentService.findOne({ id: model.equipmentId });
    }

    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: CustomerEquipmentAttachment): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUser?.id });
    }

    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: CustomerEquipmentAttachment): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUser?.id });
    }
}
