import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    CreateCustomerEquipmentAttachmentParams,
    CustomerEquipmentAttachmentResult,
    DeleteCustomerEquipmentAttachmentParams,
    UpdateCustomerEquipmentAttachmentParams,
} from '@packages/erp-protobuf/generated/customer/customerEquipmentAttachment_pb';

@Service()
export class CustomerEquipmentAttachmentGrpcRepo {
    serviceNodes: string[] = ['customerEquipmentAttachment', 'CustomerEquipmentAttachment'];

    async create(
        params: CreateCustomerEquipmentAttachmentParams
    ): Promise<CustomerEquipmentAttachmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerEquipmentAttachmentResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerEquipmentAttachment.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(
        params: UpdateCustomerEquipmentAttachmentParams
    ): Promise<CustomerEquipmentAttachmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerEquipmentAttachmentResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerEquipmentAttachment.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(
        params: DeleteCustomerEquipmentAttachmentParams
    ): Promise<CustomerEquipmentAttachmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerEquipmentAttachmentResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerEquipmentAttachment.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
