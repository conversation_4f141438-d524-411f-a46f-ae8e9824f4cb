import type { CustomerEquipmentAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipmentAttachment.model';
import { ICustomerEquipmentAttachment } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerEquipmentAttachment.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerEquipmentAttachment', { implements: ICustomerEquipmentAttachment })
export class TCustomerEquipmentAttachment extends ICustomerEquipmentAttachment {}

@ObjectType('PaginatedCustomerEquipmentAttachments')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerEquipmentAttachment], { nullable: true })
    customerEquipmentAttachments: CustomerEquipmentAttachment[];
}

@InputType('CustomerEquipmentAttachmentFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    equipmentId?: number;

    @Field(() => ID, { nullable: true })
    customerId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
