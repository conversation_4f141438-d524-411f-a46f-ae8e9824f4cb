import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('CustomerEquipmentAttachmentInput')
export class CommonInput {
    @Field({ nullable: true })
    name?: string;

    deleted?: boolean;
}

@InputType('CustomerEquipmentAttachmentRelationInput')
export class RelationInput extends CommonInput {
    @Field(() => GraphQLUpload)
    file: Promise<FileUpload>;
}

@InputType('CustomerEquipmentAttachmentCreateInput')
export class CreateInput extends RelationInput {
    @Field(() => ID)
    equipmentId: number;
}

@InputType('CustomerEquipmentAttachmentUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
