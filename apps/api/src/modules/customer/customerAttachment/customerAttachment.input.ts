import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('CustomerAttachmentInput')
export class CommonInput {
    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    memo?: string;

    deleted?: boolean;
}

@InputType('CustomerAttachmentCreateInput')
export class CreateInput extends CommonInput {
    @Field(() => ID)
    customerId: number;

    @Field(() => GraphQLUpload)
    file: Promise<FileUpload>;
}

@InputType('CustomerAttachmentBulkCreateInput')
export class BulkCreateInput {
    @Field(() => ID)
    customerId: number;

    @Field(() => [CreateAttachmentInput])
    attachments: CreateAttachmentInput[];
}

@InputType('CustomerAttachmentCreateAttachmentInput')
export class CreateAttachmentInput extends CommonInput {
    @Field(() => GraphQLUpload)
    file: Promise<FileUpload>;
}

@InputType('CustomerAttachmentUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
