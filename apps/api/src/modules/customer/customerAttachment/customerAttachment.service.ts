import { CustomerAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerAttachment.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import {
    AttachmentParams,
    BulkCreateCustomerAttachmentParams,
    DeleteCustomerAttachmentParams,
    UpdateCustomerAttachmentParams,
} from '@packages/erp-protobuf/generated/customer/customerAttachment_pb';
import { FileInfo } from '@packages/erp-protobuf/generated/customer/customerCertificateAttachment_pb';

import { streamToBuffer } from '~/utils/helpers/fileHandler.helper';
import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CustomerAttachmentGrpcRepo } from './customerAttachment.grpc.repo';
import { BulkCreateInput, CreateInput, UpdateInput } from './customerAttachment.input';
import { FilterInput } from './customerAttachment.type';

@Service()
export class CustomerAttachmentService extends BaseService<CustomerAttachment> {
    protected entity = CustomerAttachment;
    private gRPCrepo = new CustomerAttachmentGrpcRepo();

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: CustomerAttachment[] }> {
        const em = this.em.fork();

        const where: FilterQuery<CustomerAttachment>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
                populate: ['customer'],
            }
        );

        return { rows, count };
    }

    async createByGrpc(
        params: CreateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerAttachment> {
        const file = await params.file;
        const stream = file.createReadStream();
        const buffer = await streamToBuffer(stream);

        const inputs = new BulkCreateCustomerAttachmentParams({
            customerId: params.customerId,
            attachments: [
                {
                    name: params.name || file.filename,
                    file: new FileInfo({ ...file, content: buffer }),
                    memo: params.memo,
                },
            ],
            createdUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.bulkCreate(inputs);
        const createdRow = await this.findOneOrError({ id: row.result?.data?.ids?.[0] ?? 0 });
        return createdRow;
    }

    async bulkCreateByGrpc(
        params: BulkCreateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerAttachment[]> {
        const inputs = new BulkCreateCustomerAttachmentParams({
            customerId: params.customerId,
            createdUserId: options?.ctx?.currentUser?.id,
        });

        const attachments: AttachmentParams[] = [];
        for (const attachment of params.attachments) {
            const file = await attachment.file;
            const stream = file.createReadStream();
            const buffer = await streamToBuffer(stream);

            const next = new AttachmentParams({
                name: attachment.name || file.filename,
                file: new FileInfo({ ...file, content: buffer }),
                memo: attachment.memo,
            });
            attachments.push(next);
        }
        inputs.attachments = attachments;

        const row = await this.gRPCrepo.bulkCreate(inputs);
        const { rows } = await this.search({ ids: row.result?.data?.ids ?? [] });
        return rows;
    }

    async updateByGrpc(
        params: UpdateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerAttachment> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateCustomerAttachmentParams({
            id: row.id,
            name: params.name,
            memo: params.memo,
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteCustomerAttachmentParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.delete(inputs);
        return res.success;
    }
}
