import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    BulkCreateCustomerAttachmentParams,
    CustomerAttachmentUpdateResult,
    CustomerBulkAttachmentCreateResult,
    DeleteCustomerAttachmentParams,
    UpdateCustomerAttachmentParams,
} from '@packages/erp-protobuf/generated/customer/customerAttachment_pb';

@Service()
export class CustomerAttachmentGrpcRepo {
    serviceNodes: string[] = ['customerAttachment', 'CustomerAttachment'];

    async bulkCreate(
        params: BulkCreateCustomerAttachmentParams
    ): Promise<CustomerBulkAttachmentCreateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerBulkAttachmentCreateResult>('bulkCreate', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerAttachment.bulkCreate]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateCustomerAttachmentParams): Promise<CustomerAttachmentUpdateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerAttachmentUpdateResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerAttachment.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteCustomerAttachmentParams): Promise<CustomerAttachmentUpdateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerAttachmentUpdateResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerAttachment.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
