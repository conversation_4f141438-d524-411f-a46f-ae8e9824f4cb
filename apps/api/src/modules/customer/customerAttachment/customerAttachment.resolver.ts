import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import type { CustomerAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerAttachment.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { createFileBaseResolver } from '~/utils/providers/fileBase.resolver';

import { CustomerService } from '../customer/customer.service';
import { TCustomer } from '../customer/customer.type';
import { BulkCreateInput, CreateInput, UpdateInput } from './customerAttachment.input';
import { CustomerAttachmentService } from './customerAttachment.service';
import { PaginatedObjects, SearchArgs, TCustomerAttachment } from './customerAttachment.type';

const FileBaseResolver = createFileBaseResolver(TCustomerAttachment);

@Service()
@Resolver(() => TCustomerAttachment)
export class CustomerAttachmentResolver extends FileBaseResolver {
    @Inject()
    private service: CustomerAttachmentService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomerAttachments' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerAttachments: CustomerAttachment[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerAttachments: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomerAttachment], { name: 'customerAttachments' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<CustomerAttachment[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomerAttachment, { name: 'customerAttachment' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<CustomerAttachment> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerAttachment, { name: 'createCustomerAttachment' })
    async create(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<CustomerAttachment> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => [TCustomerAttachment], { name: 'bulkCreateCustomerAttachment' })
    async bulkCreate(
        @Ctx() ctx: Context,
        @Arg('input') input: BulkCreateInput
    ): Promise<CustomerAttachment[]> {
        const rows = await this.service.bulkCreateByGrpc(input, { ctx });
        return rows;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerAttachment, { name: 'updateCustomerAttachment' })
    async update(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<CustomerAttachment> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteCustomerAttachment' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private customerService: CustomerService;
    @FieldResolver(() => TCustomer, { description: '客戶' })
    async customer(@Root() model: CustomerAttachment): Promise<Customer | undefined> {
        return await this.customerService.findOne({ id: model.customer.id });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: CustomerAttachment): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUserId });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: CustomerAttachment): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUserId });
    }
}
