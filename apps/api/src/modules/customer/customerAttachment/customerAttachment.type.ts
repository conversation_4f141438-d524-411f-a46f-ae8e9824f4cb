import type { CustomerAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerAttachment.model';
import { ICustomerAttachment } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerAttachment.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerAttachment', { implements: ICustomerAttachment })
export class TCustomerAttachment extends ICustomerAttachment {}

@ObjectType('PaginatedCustomerAttachments')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerAttachment], { nullable: true })
    customerAttachments: CustomerAttachment[];
}

@InputType('CustomerAttachmentFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    customerId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
