import type { CustomerCertificateType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificateType.model';
import { ICustomerCertificateType } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerCertificateType.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerCertificateType', { implements: ICustomerCertificateType })
export class TCustomerCertificateType extends ICustomerCertificateType {}

@ObjectType('PaginatedCustomerCertificateTypes')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerCertificateType], { nullable: true })
    customerCertificateTypes: CustomerCertificateType[];
}

@InputType('CustomerCertificateTypeFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
