import type { CustomerCertificateType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificateType.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { CreateInput, UpdateInput } from './customerCertificateType.input';
import { CustomerCertificateTypeService } from './customerCertificateType.service';
import {
    PaginatedObjects,
    SearchArgs,
    TCustomerCertificateType,
} from './customerCertificateType.type';

@Service()
@Resolver(() => TCustomerCertificateType)
export class CustomerCertificateTypeResolver {
    @Inject()
    private service: CustomerCertificateTypeService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomerCertificateTypes' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerCertificateTypes: CustomerCertificateType[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerCertificateTypes: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomerCertificateType], { name: 'customerCertificateTypes' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<CustomerCertificateType[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomerCertificateType, { name: 'customerCertificateType' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<CustomerCertificateType> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerCertificateType, { name: 'createCustomerCertificateType' })
    async create(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<CustomerCertificateType> {
        const row = await this.service.create(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerCertificateType, { name: 'updateCustomerCertificateType' })
    async update(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<CustomerCertificateType> {
        const row = await this.service.update(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteCustomerCertificateType' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        await this.service.update({ id, deleted: true });
        return true;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(
        @Root() model: CustomerCertificateType
    ): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }
}
