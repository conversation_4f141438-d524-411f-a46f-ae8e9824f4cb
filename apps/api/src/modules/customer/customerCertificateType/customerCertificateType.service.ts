import { CustomerCertificateType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificateType.model';
import { EntityManager, FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { EntityUpdater } from '~/utils/providers/entityUpdater.provider';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CommonInput, CreateInput, UpdateInput } from './customerCertificateType.input';
import { FilterInput } from './customerCertificateType.type';

@Service()
export class CustomerCertificateTypeService extends BaseService<CustomerCertificateType> {
    protected entity = CustomerCertificateType;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: CustomerCertificateType[] }> {
        const em = this.em.fork();

        const where: FilterQuery<CustomerCertificateType>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ viewOrder: 'ASC' }, { id: 'DESC' }],
                populate: ['salesTeamGroup'],
            }
        );

        return { rows, count };
    }

    async create(
        params: CreateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerCertificateType> {
        const em = this.em.fork();

        const row = new this.entity();
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const createdRow = await this.findOneOrError({ id: row.id });
        return createdRow;
    }

    async update(
        params: UpdateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerCertificateType> {
        const em = this.em.fork();

        const row = await this.findOneOrError({ id: params.id });
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const updatedRow = await this.findOneOrError({ id: row.id });
        return updatedRow;
    }

    private async mutate(
        params: { em: EntityManager; ent: CustomerCertificateType; input: CommonInput },
        options?: { ctx?: Context }
    ): Promise<CustomerCertificateType> {
        const { em, ent, input } = params;

        const updater = new EntityUpdater<CustomerCertificateType>({ ent, em });
        updater.updateSimpleColumns(input);

        return ent;
    }
}
