import { ICustomersConsumablePrice } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customersConsumablePrice.model';
import { InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput } from '~/utils/types/base.type';

@ObjectType('CustomersConsumablePrice', { implements: ICustomersConsumablePrice })
export class TCustomersConsumablePrice extends ICustomersConsumablePrice {
    deleted: boolean;
}

@InputType('CustomersConsumablePriceFilterInput')
export class FilterInput extends BaseFilterInput {}
