import { CustomersConsumablePrice } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customersConsumablePrice.model';
import { Connection, EntityManager, FilterQuery, IDatabaseDriver } from '@mikro-orm/core';
import _ from 'lodash';
import { Inject, Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './customersConsumablePrice.type';

@Service()
export class CustomersConsumablePriceService extends BaseService<CustomersConsumablePrice> {
    protected entity = CustomersConsumablePrice;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: CustomersConsumablePrice[] }> {
        const em = this.em.fork();

        const where: FilterQuery<CustomersConsumablePrice>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    getFirst(params: {
        customerId: number;
        invoicingCustomerId: number;
        customersConsumablePrices: CustomersConsumablePrice[];
    }): CustomersConsumablePrice | null {
        const today = new Date().toISOString().split('T')[0];
        const now = new Date(today);

        // 過濾出已生效的價格（但未過期）
        const validPrices = params.customersConsumablePrices.filter(
            (data) =>
                !data.deleted &&
                data.customerId == params.customerId &&
                data.invoicingCustomerId == params.invoicingCustomerId &&
                new Date(data.effectiveStartDate).getTime() <= now.getTime() && // 已開始生效
                new Date(data.effectiveEndDate).getTime() >= now.getTime() // 尚未過期
        );

        const latestActivePrice =
            _.orderBy(validPrices, ['effectiveStartDate'], ['desc'])[0] || null;

        return latestActivePrice;
    }
}
