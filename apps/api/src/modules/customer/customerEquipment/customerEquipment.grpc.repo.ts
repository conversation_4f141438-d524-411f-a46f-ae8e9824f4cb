import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import type { DeleteCustomerEquipmentAttachmentParams } from '@packages/erp-protobuf/generated/customer/customerEquipmentAttachment_pb';
import {
    CreateCustomerEquipmentParams,
    CustomerEquipmentResult,
    UpdateCustomerEquipmentParams,
} from '@packages/erp-protobuf/generated/customer/customerEquipment_pb';

@Service()
export class CustomerEquipmentGrpcRepo {
    serviceNodes: string[] = ['customerEquipment', 'CustomerEquipment'];

    async create(params: CreateCustomerEquipmentParams): Promise<CustomerEquipmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerEquipmentResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerEquipment.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateCustomerEquipmentParams): Promise<CustomerEquipmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerEquipmentResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerEquipment.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }


    async delete(
        params: DeleteCustomerEquipmentAttachmentParams
    ): Promise<CustomerEquipmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerEquipmentResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerEquipment.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
