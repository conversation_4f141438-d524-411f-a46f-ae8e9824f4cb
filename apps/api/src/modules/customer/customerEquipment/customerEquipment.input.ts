import { Field, ID, InputType, Int } from 'type-graphql';

import { RelationInput as AttachmentRelationInput } from '../customerEquipmentAttachment/customerEquipmentAttachment.input';

@InputType('CustomerEquipmentInput')
export class CommonInput {
    @Field((type) => ID)
    customerId: number;

    @Field((type) => ID)
    competitorId: number;

    @Field((type) => Int,{ nullable: true })
    manufactureYear: number;

    @Field((type) => Int, { nullable: true })
    competitorPrice?: number;

    @Field({ nullable: true, description: '備註' })
    notes?: string;

    deleted?: boolean;
}

@InputType('CustomerEquipmentCreateInput')
export class CreateInput extends CommonInput {
    @Field(() => [AttachmentRelationInput], { nullable: true })
    attachments?: AttachmentRelationInput[];
}

@InputType('CustomerEquipmentUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
