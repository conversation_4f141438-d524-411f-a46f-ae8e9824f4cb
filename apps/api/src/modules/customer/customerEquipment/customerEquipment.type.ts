import type { CustomerEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipment.model';
import { ICustomerEquipment } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerEquipment.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerEquipment', { implements: ICustomerEquipment })
export class TCustomerEquipment extends ICustomerEquipment {}

@ObjectType('PaginatedCustomerEquipments')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerEquipment], { nullable: true })
    customerEquipments: CustomerEquipment[];
}

@InputType('CustomerEquipmentFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    customerId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
