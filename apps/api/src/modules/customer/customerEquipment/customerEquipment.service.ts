import { CustomerEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipment.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import { FileInfo } from '@packages/erp-protobuf/generated/customer/customerEquipmentAttachment_pb';
import {
    AttachmentParams,
    CreateCustomerEquipmentParams,
    DeleteCustomerEquipmentParams,
    UpdateCustomerEquipmentParams,
} from '@packages/erp-protobuf/generated/customer/customerEquipment_pb';

import { streamToBuffer } from '~/utils/helpers/fileHandler.helper';
import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CustomerEquipmentGrpcRepo } from './customerEquipment.grpc.repo';
import { CreateInput, UpdateInput } from './customerEquipment.input';
import { BuildPopulatingParams, PopulateType } from './customerEquipment.service.type';
import { FilterInput } from './customerEquipment.type';

@Service()
export class CustomerEquipmentService extends BaseService<CustomerEquipment> {
    protected entity = CustomerEquipment;
    private gRPCrepo = new CustomerEquipmentGrpcRepo();

    private buildPopulating(params: BuildPopulatingParams): PopulateType {
        let populating: PopulateType = ['manufactureYear', 'competitorPrice', 'notes'];
        if (params?.populating === 'Full') {
            const inserted: PopulateType = ['competitor', 'createdUser', 'updatedUser'];
            populating.push(...inserted);
        }
        return populating;
    }

    async search(
        params?: FilterInput,
        options?: {
            ctx?: Context;
            pagination?: PaginationInput;
            count?: boolean;
            populate?: 'Default' | 'Full';
        }
    ): Promise<{ count: number | null; rows: CustomerEquipment[] }> {
        const em = this.em.fork();

        const where: FilterQuery<CustomerEquipment>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        const populating = this.buildPopulating({ populating: options?.populate });
        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
                populate: ['competitor', 'customer', 'createdUser', 'updatedUser'],
            }
        );

        return { rows, count };
    }

    async createByGrpc(
        params: CreateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerEquipment> {
        const inputs = new CreateCustomerEquipmentParams({
            customerId: params.customerId,
            competitorId: params.competitorId,
            manufactureYear: params.manufactureYear,
            competitorPrice: params.competitorPrice,
            notes: params.notes,
            createdUserId: options?.ctx?.currentUser?.id,
        });
        if (params.attachments) {
            inputs.attachments = [];
            for (const attachment of params.attachments) {
                const file = await attachment.file;
                const stream = file.createReadStream();
                const buffer = await streamToBuffer(stream);

                const params = new AttachmentParams({
                    name: attachment.name || file.filename,
                    file: new FileInfo({ ...file, content: buffer }),
                });
                inputs.attachments.push(params);
            }
        }

        const row = await this.gRPCrepo.create(inputs);
        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(
        params: UpdateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerEquipment> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateCustomerEquipmentParams({
            id: row.id,
            competitorId: handler.toNumber({
                input: params.competitorId,
                default: row.competitorId,
            }),
            manufactureYear: handler.toNumber({
                input: params.manufactureYear,
                default: row.manufactureYear,
            }),
            competitorPrice: handler.toNumber({
                input: params.competitorPrice,
                default: row.competitorPrice,
            }),
            notes: handler.toString({ input: params.notes, default: row.notes }),
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteCustomerEquipmentParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }
}
