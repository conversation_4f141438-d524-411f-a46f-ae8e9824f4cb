import { EnumSalesTeamGroupCode } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { RouteOptions } from 'fastify';
import { DateTime } from 'luxon';
import mime from 'mime-types';
import Container from 'typedi';

import { EnumPermissionCode } from '~/constants';
import { RestUserAuthInterceptor } from '~/middlewares/auth.middleware';
import { CustomerEquipmentService } from '~/modules/customer/customerEquipment/customerEquipment.service';
import { downloadSchema } from './customerEquipment.swagger';
import { CustomerEquipmentTemplateCnService } from './sheets/customerEquipment.template-cn.service';
import { CustomerEquipmentTemplateTwService } from './sheets/customerEquipment.template-tw.service';

const routes: Record<string, RouteOptions> = {
    download: {
        method: 'POST',
        url: '/download',
        preHandler: [RestUserAuthInterceptor(EnumPermissionCode['customerEquipment.export'])],
        schema: downloadSchema,
        handler: async (req, reply) => {
            const body = req.body || {};

            const defaultLimit = 10_000;
            const customerEquipmentService = Container.get(CustomerEquipmentService);
            const { count, rows } = await customerEquipmentService.search(body, {
                ctx: { ...req.ctx, ctx: { request: req, reply } },
                pagination: { limit: defaultLimit },
                populate: 'Full',
                count: true,
            });

            // START: Create Excel
            let template: CustomerEquipmentTemplateCnService | CustomerEquipmentTemplateTwService;
            switch (req.ctx.currentSalesTeamGroup?.code) {
                case EnumSalesTeamGroupCode.TWN_EYE: {
                    template = Container.get(CustomerEquipmentTemplateTwService);
                    break;
                }
                case EnumSalesTeamGroupCode.CHN_EYE:
                default: {
                    template = Container.get(CustomerEquipmentTemplateCnService);
                    break;
                }
            }
            await template.load();
            await template.render({ customerEquipments: rows, count, limit: defaultLimit });
            // END: Create Excel

            const content = await template.template.output();
            const now = DateTime.now();
            const filename = `customerEquipments_${now.toFormat('yyyyMMdd_HHmmss')}.xlsx`;
            const mimeType = mime.lookup(filename);

            reply.header('Content-Type', mimeType);
            reply.header('Content-Disposition', `attachment; filename="${filename}"`);
            reply.send(content);
        },
    },
};

export default Object.values(routes);
