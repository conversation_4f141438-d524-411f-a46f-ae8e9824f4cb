import { Utils } from '@clinico/clinico-node-framework';
import { CustomerEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipment.model';
import path from 'node:path';
import Container, { Service } from 'typedi';

import { configs } from '~/app.config';

import { CustomerEquipmentSheetTwService } from './customerEquipment.sheet-tw.service';

@Service()
export class CustomerEquipmentTemplateTwService {
    public template = new Utils.ExcelTemplater();

    private filepath = path.join(configs.dir.template, 'excel',  'customer',
        'customerEquipment_tw.xlsx');
    async load() {
        await this.template.load(this.filepath);
        return this;
    }

    async render(params: {
        customerEquipments: CustomerEquipment[];
        count: number | null;
        limit: number;
    }) {
        const startRowIndex = 2;
        this.template.sheet('顧客設備清單');

        const sheetService = Container.get(CustomerEquipmentSheetTwService);
        this.template.fillRows({
            startRowIndex: startRowIndex,
            rows: sheetService.bulkRender({
                customerEquipments: params.customerEquipments,
            }),
        });

        // Add warning message to footer if count exceeds limit
        if (params.count && params.count > params.limit) {
            const message = '#### 已達到匯出筆數上限，請嘗試縮小查詢範圍後重試。 ####';
            this.template.fillRows({
                startRowIndex: startRowIndex + params.limit,
                rows: [{ 設備廠牌名稱: message }],
                rowStyle: { horizontalAlignment: 'left' },
            });
        }

        return this;
    }
}
