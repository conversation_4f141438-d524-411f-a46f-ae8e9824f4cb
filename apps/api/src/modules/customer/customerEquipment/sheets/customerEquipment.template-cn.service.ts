import { Utils } from '@clinico/clinico-node-framework';
import { CustomerEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipment.model';
import path from 'node:path';
import Container, { Service } from 'typedi';

import { configs } from '~/app.config';

import { CustomerEquipmentSheetCnService } from './customerEquipment.sheet-cn.service';

@Service()
export class CustomerEquipmentTemplateCnService {
    public template = new Utils.ExcelTemplater();

    private filepath = path.join(
        configs.dir.template,
        'excel',
        'customer',
        'customerEquipment_cn.xlsx'
    );
    async load() {
        await this.template.load(this.filepath);
        return this;
    }

    async render(params: {
        customerEquipments: CustomerEquipment[];
        count: number | null;
        limit: number;
    }) {
        const startRowIndex = 2;
        this.template.sheet('顾客设备清单');

        const sheetService = Container.get(CustomerEquipmentSheetCnService);
        this.template.fillRows({
            startRowIndex: startRowIndex,
            rows: sheetService.bulkRender({
                customerEquipments: params.customerEquipments,
            }),
        });

        // Add warning message to footer if count exceeds limit
        if (params.count && params.count > params.limit) {
            const message = '#### 已达到汇出笔数上限，请尝试缩小查询范围后重试。 ####';
            this.template.fillRows({
                startRowIndex: startRowIndex + params.limit,
                rows: [{ 设备厂牌名称: message }],
                rowStyle: { horizontalAlignment: 'left' },
            });
        }

        return this;
    }
}
