import { CustomerEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipment.model';
import _ from 'lodash';
import { DateTime } from 'luxon';
import { Service } from 'typedi';

@Service()
export class CustomerEquipmentSheetCnService {
    render(params: { customerEquipment: CustomerEquipment }): any {
        const { customerEquipment } = params;
        const data = {
            competitor: (() => {
                const item: string[] = [];
                if (customerEquipment.competitor?.name && customerEquipment.competitor?.brand) {
                    item.push(customerEquipment.competitor.name+ customerEquipment.competitor.brand);
                }
                return item.length ? item.join('') : '-';
            })(),
            manufactureYear: customerEquipment.manufactureYear ?? '-',
            competitorPrice: customerEquipment.competitorPrice ?? '-',
            notes: customerEquipment.notes ?? '-',
            createdUser: (() => {
                const item: string[] = [];
                if (customerEquipment.createdUser?.name) {
                    item.push(customerEquipment.createdUser.name);
                }
                if (customerEquipment.createdUser?.code) {
                    item.push(`（${customerEquipment.createdUser.code}）`);
                }
                return item.length ? item.join('') : '-';
            })(),
            createdAt: (() => {
                if (customerEquipment.createdAt) {
                    const val = DateTime.fromJSDate(customerEquipment.createdAt);
                    return val.toFormat('yyyy-MM-dd HH:mm:ss');
                }
                return '-';
            })(),
            updatedUser: (() => {
                const item: string[] = [];
                if (customerEquipment.updatedUser?.name) {
                    item.push(customerEquipment.updatedUser.name);
                }
                if (customerEquipment.updatedUser?.code) {
                    item.push(`（${customerEquipment.updatedUser.code}）`);
                }
                return item.length ? item.join('') : '-';
            })(),
            updatedAt: (() => {
                if (customerEquipment.updatedAt) {
                    const val = DateTime.fromJSDate(customerEquipment.updatedAt);
                    return val.toFormat('yyyy-MM-dd HH:mm:ss');
                }
                return '-';
            })(),
        };
        return data;
    }

    bulkRender(params: { customerEquipments: CustomerEquipment[] }): any[] {
        const { customerEquipments } = params;
        return customerEquipments.map((customerEquipment) => this.render({ customerEquipment }));
    }
}
