import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import type { CustomerEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipment.model';
import type { CustomerEquipmentAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipmentAttachment.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { CustomerService } from '../customer/customer.service';
import { TCustomer } from '../customer/customer.type';
import { TCustomerEquipmentAttachment } from '../customerEquipmentAttachment/customerEquipmentAttachment.type';
import { CreateInput, UpdateInput } from './customerEquipment.input';
import { CustomerEquipmentService } from './customerEquipment.service';
import { PaginatedObjects, SearchArgs, TCustomerEquipment } from './customerEquipment.type';
import { CompetitorService } from '~/modules/competitor/competitor.service';
import { TCompetitor } from '~/modules/competitor/competitor.type';
import { Competitor } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/competitor.model';

@Service()
@Resolver(() => TCustomerEquipment)
export class CustomerEquipmentResolver {
    @Inject()
    private service: CustomerEquipmentService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomerEquipments' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerEquipments: CustomerEquipment[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerEquipments: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomerEquipment], { name: 'customerEquipments' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<CustomerEquipment[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomerEquipment, { name: 'customerEquipment' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<CustomerEquipment> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerEquipment, { name: 'createCustomerEquipment' })
    async create(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<CustomerEquipment> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerEquipment, { name: 'updateCustomerEquipment' })
    async update(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<CustomerEquipment> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteCustomerEquipment' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private customerService: CustomerService;
    @FieldResolver(() => TCustomer, { description: '客戶' ,nullable: true} )
    async customer(@Root() model: CustomerEquipment): Promise<Customer | undefined> {
        return await this.customerService.findOne({ id: model.customer.id });
    }

    @Inject()
    private competitorService: CompetitorService;
    @FieldResolver(() => TCompetitor, { description: '競爭對手' ,nullable: true} )
    async competitor(@Root() model: CustomerEquipment): Promise<Competitor | undefined> {
        return await this.competitorService.findOne({ id: model.competitor.id });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: CustomerEquipment): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUser?.id });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: CustomerEquipment): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUser?.id });
    }

    @FieldResolver(() => [TCustomerEquipmentAttachment], { description: '附件' })
    async attachments(@Root() model: CustomerEquipment): Promise<CustomerEquipmentAttachment[]> {
        if (!model.customerEquipmentAttachmentsByEquipment.isInitialized()) {
            await model.customerEquipmentAttachmentsByEquipment.init({
                where: { deleted: false },
            });
        }
        return model.customerEquipmentAttachmentsByEquipment.getItems();
    }
}
