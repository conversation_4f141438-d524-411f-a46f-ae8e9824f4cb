import { FastifySchema } from 'fastify';

export const downloadSchema: FastifySchema = {
    summary: '設備資料清單',
    tags: ['設備', '資料'],
    body: {
        type: 'object',
        properties: {
            competitor: {
                type: 'string',
                nullable: false,
                description: '設備廠牌名稱',
            },
            manufactureYear: {
                type: 'number',
                nullable: false,
                description: '出廠年份',
            },
            competitorPrice: {
                type: 'number',
                nullable: true,
                description: '競品售價',
            },
            notes: {
                type: 'string',
                nullable: true,
                description: '備註',
            },
            createdUser: {
                type: 'string',
                nullable: false,
                description: '建立人員',
            },
            createdAt: {
                type: 'string',
                format: 'date-time',
                nullable: false,
                description: '建立時間',
            },
            updatedUser: {
                type: 'string',
                nullable: true,
                description: '最後編輯人員',
            },
            updatedAt: {
                type: 'string',
                format: 'date-time',
                nullable: true,
                description: '最後編輯時間',
            },
        },
    },
};
