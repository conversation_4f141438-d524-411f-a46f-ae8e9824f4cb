import type { CustomerProperty } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerProperty.model';
import type { CustomerPropertyType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerPropertyType.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { CustomerPropertyTypeService } from '../customerPropertyType/customerPropertyType.service';
import { TCustomerPropertyType } from '../customerPropertyType/customerPropertyType.type';
import { CreateInput, UpdateInput } from './customerProperty.input';
import { CustomerPropertyService } from './customerProperty.service';
import { PaginatedObjects, SearchArgs, TCustomerProperty } from './customerProperty.type';

@Service()
@Resolver(() => TCustomerProperty)
export class CustomerPropertyResolver {
    @Inject()
    private service: CustomerPropertyService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomerProperties' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerProperties: CustomerProperty[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerProperties: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomerProperty], { name: 'customerProperties' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<CustomerProperty[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomerProperty, { name: 'customerProperty' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<CustomerProperty> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerProperty, { name: 'createCustomerProperty' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<CustomerProperty> {
        const row = await this.service.create(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerProperty, { name: 'updateCustomerProperty' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<CustomerProperty> {
        const row = await this.service.update(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteCustomerProperty' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        await this.service.update({ id, deleted: true });
        return true;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: CustomerProperty): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }

    @Inject()
    private customerPropertyTypeService: CustomerPropertyTypeService;
    @FieldResolver(() => TCustomerPropertyType, { description: '類型' })
    async type(@Root() model: CustomerProperty): Promise<CustomerPropertyType | undefined> {
        return await this.customerPropertyTypeService.findOne({ id: model.type.id });
    }
}
