import type { CustomerProperty } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerProperty.model';
import type { CustomerPropertyType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerPropertyType.model';
import { ICustomerProperty } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerProperty.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

import { TCustomerPropertyType } from '../customerPropertyType/customerPropertyType.type';

@ObjectType('CustomerProperty', { implements: ICustomerProperty })
export class TCustomerProperty extends ICustomerProperty {}

@ObjectType('PaginatedCustomerProperties')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerProperty], { nullable: true })
    customerProperties: CustomerProperty[];
}

@ObjectType('FormattedCustomerProperties')
export class FormattedProperty {
    @Field(() => TCustomerPropertyType, { nullable: true })
    propertyType: CustomerPropertyType;

    @Field(() => [TCustomerProperty], { nullable: true })
    properties: CustomerProperty[];
}

@InputType('CustomerPropertyFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    customerId?: number;

    @Field(() => [ID], { nullable: true })
    typeIds?: number[];
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
