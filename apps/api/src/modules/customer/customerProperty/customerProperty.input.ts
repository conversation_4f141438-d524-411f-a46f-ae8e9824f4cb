import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('CustomerPropertyInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field(() => ID, { nullable: true })
    typeId?: number;

    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    code?: string;

    @Field(() => Int, { nullable: true })
    viewOrder?: number;

    deleted?: boolean;
}

@InputType('CustomerPropertyCreateInput')
export class CreateInput extends CommonInput {}

@InputType('CustomerPropertyUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
