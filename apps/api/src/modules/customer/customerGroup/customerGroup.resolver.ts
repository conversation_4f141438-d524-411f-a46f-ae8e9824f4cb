import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { CustomerGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerGroup.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { CreateInput, UpdateInput } from './customerGroup.input';
import { CustomerGroupService } from './customerGroup.service';
import { PaginatedObjects, SearchArgs, TCustomerGroup } from './customerGroup.type';

@Service()
@Resolver(() => TCustomerGroup)
export class CustomerGroupResolver {
    @Inject()
    private service: CustomerGroupService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomerGroups' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerGroups: CustomerGroup[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerGroups: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomerGroup], { name: 'customerGroups' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<CustomerGroup[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomerGroup, { name: 'customerGroup' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<CustomerGroup> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerGroup, { name: 'createCustomerGroup' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<CustomerGroup> {
        const row = await this.service.create(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerGroup, { name: 'updateCustomerGroup' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<CustomerGroup> {
        const row = await this.service.update(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteCustomerGroup' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        await this.service.update({ id, deleted: true });
        return true;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: CustomerGroup): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: CustomerGroup): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUser?.id });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: CustomerGroup): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUser?.id });
    }
}
