import { Field, ID, InputType } from 'type-graphql';

@InputType('CustomerGroupInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field({ nullable: true })
    name?: string;

    deleted?: boolean;
}

@InputType('CustomerGroupCreateInput')
export class CreateInput extends CommonInput {}

@InputType('CustomerGroupUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
