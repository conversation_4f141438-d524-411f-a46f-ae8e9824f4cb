import type { CustomerGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerGroup.model';
import { ICustomerGroup } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerGroup.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerGroup', { implements: ICustomerGroup })
export class TCustomerGroup extends ICustomerGroup {}

@ObjectType('PaginatedCustomerGroups')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerGroup], { nullable: true })
    customerGroups: CustomerGroup[];
}

@InputType('CustomerGroupFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
