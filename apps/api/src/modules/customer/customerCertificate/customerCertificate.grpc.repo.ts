import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import type { DeleteCustomerCertificateAttachmentParams } from '@packages/erp-protobuf/generated/customer/customerCertificateAttachment_pb';
import {
    CreateCustomerCertificateParams,
    CustomerCertificateResult,
    UpdateCustomerCertificateParams,
} from '@packages/erp-protobuf/generated/customer/customerCertificate_pb';

@Service()
export class CustomerCertificateGrpcRepo {
    serviceNodes: string[] = ['customerCertificate', 'CustomerCertificate'];

    async create(params: CreateCustomerCertificateParams): Promise<CustomerCertificateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerCertificateResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerCertificate.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateCustomerCertificateParams): Promise<CustomerCertificateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerCertificateResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerCertificate.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(
        params: DeleteCustomerCertificateAttachmentParams
    ): Promise<CustomerCertificateResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerCertificateResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerCertificate.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
