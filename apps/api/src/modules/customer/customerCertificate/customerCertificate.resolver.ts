import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import type { CustomerCertificate } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificate.model';
import type { CustomerCertificateAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificateAttachment.model';
import type { CustomerCertificateType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificateType.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { CustomerService } from '../customer/customer.service';
import { TCustomer } from '../customer/customer.type';
import { TCustomerCertificateAttachment } from '../customerCertificateAttachment/customerCertificateAttachment.type';
import { CustomerCertificateTypeService } from '../customerCertificateType/customerCertificateType.service';
import { TCustomerCertificateType } from '../customerCertificateType/customerCertificateType.type';
import { CreateInput, UpdateInput } from './customerCertificate.input';
import { CustomerCertificateService } from './customerCertificate.service';
import { PaginatedObjects, SearchArgs, TCustomerCertificate } from './customerCertificate.type';

@Service()
@Resolver(() => TCustomerCertificate)
export class CustomerCertificateResolver {
    @Inject()
    private service: CustomerCertificateService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomerCertificates' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerCertificates: CustomerCertificate[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerCertificates: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomerCertificate], { name: 'customerCertificates' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<CustomerCertificate[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomerCertificate, { name: 'customerCertificate' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<CustomerCertificate> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerCertificate, { name: 'createCustomerCertificate' })
    async create(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<CustomerCertificate> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerCertificate, { name: 'updateCustomerCertificate' })
    async update(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<CustomerCertificate> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteCustomerCertificate' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private customerCertificateTypeService: CustomerCertificateTypeService;
    @FieldResolver(() => TCustomerCertificateType, { description: '類型' })
    async type(@Root() model: CustomerCertificate): Promise<CustomerCertificateType | undefined> {
        return await this.customerCertificateTypeService.findOne({ id: model.type.id });
    }

    @Inject()
    private customerService: CustomerService;
    @FieldResolver(() => TCustomer, { description: '客戶' })
    async customer(@Root() model: CustomerCertificate): Promise<Customer | undefined> {
        return await this.customerService.findOne({ id: model.customer.id });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: CustomerCertificate): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUser?.id });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: CustomerCertificate): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUser?.id });
    }

    @FieldResolver(() => [TCustomerCertificateAttachment], { description: '附件' })
    async attachments(
        @Root() model: CustomerCertificate
    ): Promise<CustomerCertificateAttachment[]> {
        if (!model.customerCertificateAttachmentsByCertificate.isInitialized()) {
            await model.customerCertificateAttachmentsByCertificate.init({
                where: { deleted: false },
            });
        }
        return model.customerCertificateAttachmentsByCertificate.getItems();
    }
}
