import type { CustomerCertificate } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificate.model';
import { ICustomerCertificate } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerCertificate.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerCertificate', { implements: ICustomerCertificate })
export class TCustomerCertificate extends ICustomerCertificate {}

@ObjectType('PaginatedCustomerCertificates')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerCertificate], { nullable: true })
    customerCertificates: CustomerCertificate[];
}

@InputType('CustomerCertificateFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    customerId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
