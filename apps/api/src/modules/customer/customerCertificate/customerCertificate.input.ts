import { Field, ID, InputType, Int } from 'type-graphql';

import { RelationInput as AttachmentRelationInput } from '../customerCertificateAttachment/customerCertificateAttachment.input';

@InputType('CustomerCertificateInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    typeId?: number;

    @Field(() => ID, { nullable: true })
    customerId?: number;

    @Field({ nullable: true, description: '證號' })
    code?: string;

    @Field({ nullable: true, description: '經營範圍' })
    scope?: string;

    @Field({ nullable: true })
    effectiveDate?: Date;

    @Field({ nullable: true })
    expiryDate?: Date;

    deleted?: boolean;
}

@InputType('CustomerCertificateCreateInput')
export class CreateInput extends CommonInput {
    @Field(() => [AttachmentRelationInput], { nullable: true })
    attachments?: AttachmentRelationInput[];
}

@InputType('CustomerCertificateUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
