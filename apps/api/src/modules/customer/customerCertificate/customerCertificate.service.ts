import { CustomerCertificate } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificate.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import { FileInfo } from '@packages/erp-protobuf/generated/customer/customerCertificateAttachment_pb';
import {
    AttachmentParams,
    CreateCustomerCertificateParams,
    DeleteCustomerCertificateParams,
    UpdateCustomerCertificateParams,
} from '@packages/erp-protobuf/generated/customer/customerCertificate_pb';

import { streamToBuffer } from '~/utils/helpers/fileHandler.helper';
import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CustomerCertificateGrpcRepo } from './customerCertificate.grpc.repo';
import { CreateInput, UpdateInput } from './customerCertificate.input';
import { FilterInput } from './customerCertificate.type';

@Service()
export class CustomerCertificateService extends BaseService<CustomerCertificate> {
    protected entity = CustomerCertificate;
    private gRPCrepo = new CustomerCertificateGrpcRepo();

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: CustomerCertificate[] }> {
        const em = this.em.fork();

        const where: FilterQuery<CustomerCertificate>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                orderBy: [{ id: 'DESC' }],
                populate: ['customer', 'createdUser', 'updatedUser'],
            }
        );

        return { rows, count };
    }

    async createByGrpc(
        params: CreateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerCertificate> {
        const inputs = new CreateCustomerCertificateParams({
            customerId: params.customerId,
            typeId: params.typeId,
            code: params.code,
            scope: params.scope,
            effectiveDate: handler.toTimestamp({ input: params.effectiveDate }),
            expiryDate: handler.toTimestamp({ input: params.expiryDate }),
            createdUserId: options?.ctx?.currentUser?.id,
        });
        if (params.attachments) {
            inputs.attachments = [];
            for (const attachment of params.attachments) {
                const file = await attachment.file;
                const stream = file.createReadStream();
                const buffer = await streamToBuffer(stream);

                const params = new AttachmentParams({
                    name: attachment.name || file.filename,
                    file: new FileInfo({ ...file, content: buffer }),
                    memo: attachment.memo,
                });
                inputs.attachments.push(params);
            }
        }

        const row = await this.gRPCrepo.create(inputs);
        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(
        params: UpdateInput,
        options?: { ctx?: Context }
    ): Promise<CustomerCertificate> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateCustomerCertificateParams({
            id: row.id,
            typeId: handler.toNumber({ input: params.typeId, default: row.typeId }),
            code: handler.toString({ input: params.code, default: row.code }),
            scope: handler.toString({ input: params.scope, default: row.scope }),
            effectiveDate: handler.toTimestamp({
                input: params.effectiveDate,
                default: row.effectiveDate,
            }),
            expiryDate: handler.toTimestamp({ input: params.expiryDate, default: row.expiryDate }),
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteCustomerCertificateParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }
}
