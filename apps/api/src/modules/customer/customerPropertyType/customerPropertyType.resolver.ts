import type { CustomerProperty } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerProperty.model';
import type { CustomerPropertyType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerPropertyType.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { CustomerPropertyService } from '../customerProperty/customerProperty.service';
import { TCustomerProperty } from '../customerProperty/customerProperty.type';
import { CreateInput, UpdateInput } from './customerPropertyType.input';
import { CustomerPropertyTypeService } from './customerPropertyType.service';
import { PaginatedObjects, SearchArgs, TCustomerPropertyType } from './customerPropertyType.type';

@Service()
@Resolver(() => TCustomerPropertyType)
export class CustomerPropertyTypeResolver {
    @Inject()
    private service: CustomerPropertyTypeService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomerPropertyTypes' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerPropertyTypes: CustomerPropertyType[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerPropertyTypes: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomerPropertyType], { name: 'customerPropertyTypes' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<CustomerPropertyType[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomerPropertyType, { name: 'customerPropertyType' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<CustomerPropertyType> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerPropertyType, { name: 'createCustomerPropertyType' })
    async create(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<CustomerPropertyType> {
        const row = await this.service.create(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerPropertyType, { name: 'updateCustomerPropertyType' })
    async update(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<CustomerPropertyType> {
        const row = await this.service.update(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteCustomerPropertyType' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        await this.service.update({ id, deleted: true });
        return true;
    }

    @Inject()
    private customerPropertyService: CustomerPropertyService;
    @FieldResolver(() => [TCustomerProperty], { description: '客戶屬性' })
    async properties(@Root() model: CustomerPropertyType): Promise<CustomerProperty[]> {
        return this.customerPropertyService.findByTypeId(model.id);
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: CustomerPropertyType): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }
}
