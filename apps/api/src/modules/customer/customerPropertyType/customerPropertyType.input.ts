import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('CustomerPropertyTypeInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field({ nullable: true })
    name?: string;

    @Field(() => Int, { nullable: true })
    viewOrder?: number;

    deleted?: boolean;
}

@InputType('CustomerPropertyTypeCreateInput')
export class CreateInput extends CommonInput {}

@InputType('CustomerPropertyTypeUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
