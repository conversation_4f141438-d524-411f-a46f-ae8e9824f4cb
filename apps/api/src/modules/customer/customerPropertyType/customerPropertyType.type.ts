import type { CustomerPropertyType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerPropertyType.model';
import { ICustomerPropertyType } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerPropertyType.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerPropertyType', { implements: ICustomerPropertyType })
export class TCustomerPropertyType extends ICustomerPropertyType {}

@ObjectType('PaginatedCustomerPropertyTypes')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerPropertyType], { nullable: true })
    customerPropertyTypes: CustomerPropertyType[];
}

@InputType('CustomerPropertyTypeFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
