import type { CustomerType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerType.model';
import { ICustomerType } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerType.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerType', { implements: ICustomerType })
export class TCustomerType extends ICustomerType {}

@ObjectType('PaginatedCustomerTypes')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerType], { nullable: true })
    customerTypes: CustomerType[];
}

@InputType('CustomerTypeFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
