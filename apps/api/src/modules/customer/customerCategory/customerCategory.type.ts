import type { CustomerCategory } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCategory.model';
import { ICustomerCategory } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerCategory.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerCategory', { implements: ICustomerCategory })
export class TCustomerCategory extends ICustomerCategory {}

@ObjectType('PaginatedCustomerCategories')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerCategory], { nullable: true })
    customerCategories: CustomerCategory[];
}

@InputType('CustomerCategoryFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
