import { CustomerCategory } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCategory.model';
import { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { EntityManager, FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { EntityUpdater } from '~/utils/providers/entityUpdater.provider';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { CommonInput, CreateInput, UpdateInput } from './customerCategory.input';
import { FilterInput } from './customerCategory.type';

@Service()
export class CustomerCategoryService extends BaseService<CustomerCategory> {
    protected entity = CustomerCategory;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: CustomerCategory[] }> {
        const em = this.em.fork();

        const where: FilterQuery<CustomerCategory>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();
        where.push({ deleted: false });

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['salesTeamGroup'],
                orderBy: [{ viewOrder: 'ASC' }, { id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    async create(params: CreateInput, options?: { ctx?: Context }): Promise<CustomerCategory> {
        const em = this.em.fork();

        const row = new this.entity();
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const createdRow = await this.findOneOrError({ id: row.id });
        return createdRow;
    }

    async update(params: UpdateInput, options?: { ctx?: Context }): Promise<CustomerCategory> {
        const em = this.em.fork();

        const row = await this.findOneOrError({ id: params.id });
        await this.mutate({ em, ent: row, input: params }, { ctx: options?.ctx });
        await em.persist(row).flush();

        const updatedRow = await this.findOneOrError({ id: row.id });
        return updatedRow;
    }

    private async mutate(
        params: { em: EntityManager; ent: CustomerCategory; input: CommonInput },
        options?: { ctx?: Context }
    ): Promise<CustomerCategory> {
        const { em, ent, input } = params;

        const updater = new EntityUpdater<CustomerCategory>({ ent, em });
        updater.updateSimpleColumns(input);
        updater.updateToOne({
            key: 'salesTeamGroup',
            ref: SalesTeamGroup,
            id: input.salesTeamGroupId ?? options?.ctx?.currentSalesTeamGroup?.id,
        });

        return ent;
    }
}
