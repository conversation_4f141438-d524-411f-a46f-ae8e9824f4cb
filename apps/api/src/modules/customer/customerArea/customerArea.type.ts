import type { CustomerArea } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerArea.model';
import { ICustomerArea } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerArea.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerArea', { implements: ICustomerArea })
export class TCustomerArea extends ICustomerArea {}

@ObjectType('PaginatedCustomerAreas')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerArea], { nullable: true })
    customerAreas: CustomerArea[];
}

@InputType('CustomerAreaFilterInput')
export class FilterInput extends BaseFilterInput {}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
