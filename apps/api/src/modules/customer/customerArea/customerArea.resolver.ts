import type { CustomerArea } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerArea.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { CreateInput, UpdateInput } from './customerArea.input';
import { CustomerAreaService } from './customerArea.service';
import { PaginatedObjects, SearchArgs, TCustomerArea } from './customerArea.type';

@Service()
@Resolver(() => TCustomerArea)
export class CustomerAreaResolver {
    @Inject()
    private service: CustomerAreaService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomerAreas' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerAreas: CustomerArea[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerAreas: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomerArea], { name: 'customerAreas' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<CustomerArea[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomerArea, { name: 'customerArea' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<CustomerArea> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerArea, { name: 'createCustomerArea' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<CustomerArea> {
        const row = await this.service.create(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerArea, { name: 'updateCustomerArea' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<CustomerArea> {
        const row = await this.service.update(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteCustomerArea' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        await this.service.update({ id, deleted: true });
        return true;
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: CustomerArea): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }
}
