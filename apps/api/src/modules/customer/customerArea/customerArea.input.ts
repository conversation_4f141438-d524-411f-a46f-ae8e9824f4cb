import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('CustomerAreaInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field({ nullable: true })
    name?: string;

    @Field(() => Int, { nullable: true })
    viewOrder?: number;

    deleted?: boolean;
}

@InputType('CustomerAreaCreateInput')
export class CreateInput extends CommonInput {}

@InputType('CustomerAreaUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
