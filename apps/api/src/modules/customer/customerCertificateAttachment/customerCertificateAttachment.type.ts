import type { CustomerCertificateAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificateAttachment.model';
import { ICustomerCertificateAttachment } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerCertificateAttachment.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';

import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('CustomerCertificateAttachment', { implements: ICustomerCertificateAttachment })
export class TCustomerCertificateAttachment extends ICustomerCertificateAttachment {}

@ObjectType('PaginatedCustomerCertificateAttachments')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomerCertificateAttachment], { nullable: true })
    customerCertificateAttachments: CustomerCertificateAttachment[];
}

@InputType('CustomerCertificateAttachmentFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    certificateId?: number;

    @Field(() => ID, { nullable: true })
    customerId?: number;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
