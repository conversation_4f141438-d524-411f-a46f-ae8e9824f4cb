import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { Field, ID, InputType, Int } from 'type-graphql';

@InputType('CustomerCertificateAttachmentInput')
export class CommonInput {
    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    memo?: string;

    deleted?: boolean;
}

@InputType('CustomerCertificateAttachmentRelationInput')
export class RelationInput extends CommonInput {
    @Field(() => GraphQLUpload)
    file: Promise<FileUpload>;
}

@InputType('CustomerCertificateAttachmentCreateInput')
export class CreateInput extends RelationInput {
    @Field(() => ID)
    certificateId: number;
}

@InputType('CustomerCertificateAttachmentUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}
