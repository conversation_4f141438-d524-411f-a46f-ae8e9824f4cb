import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    CreateCustomerCertificateAttachmentParams,
    CustomerCertificateAttachmentResult,
    DeleteCustomerCertificateAttachmentParams,
    UpdateCustomerCertificateAttachmentParams,
} from '@packages/erp-protobuf/generated/customer/customerCertificateAttachment_pb';

@Service()
export class CustomerCertificateAttachmentGrpcRepo {
    serviceNodes: string[] = ['customerCertificateAttachment', 'CustomerCertificateAttachment'];

    async create(
        params: CreateCustomerCertificateAttachmentParams
    ): Promise<CustomerCertificateAttachmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerCertificateAttachmentResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerCertificateAttachment.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(
        params: UpdateCustomerCertificateAttachmentParams
    ): Promise<CustomerCertificateAttachmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerCertificateAttachmentResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerCertificateAttachment.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(
        params: DeleteCustomerCertificateAttachmentParams
    ): Promise<CustomerCertificateAttachmentResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerCertificateAttachmentResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.CustomerCertificateAttachment.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
