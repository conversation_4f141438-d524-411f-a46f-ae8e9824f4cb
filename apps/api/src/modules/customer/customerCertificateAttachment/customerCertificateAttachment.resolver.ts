import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { CustomerCertificate } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificate.model';
import type { CustomerCertificateAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificateAttachment.model';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { createFileBaseResolver } from '~/utils/providers/fileBase.resolver';

import { CustomerCertificateService } from '../customerCertificate/customerCertificate.service';
import { TCustomerCertificate } from '../customerCertificate/customerCertificate.type';
import { CreateInput, UpdateInput } from './customerCertificateAttachment.input';
import { CustomerCertificateAttachmentService } from './customerCertificateAttachment.service';
import {
    PaginatedObjects,
    SearchArgs,
    TCustomerCertificateAttachment,
} from './customerCertificateAttachment.type';

const FileBaseResolver = createFileBaseResolver(TCustomerCertificateAttachment);

@Service()
@Resolver(() => TCustomerCertificateAttachment)
export class CustomerCertificateAttachmentResolver extends FileBaseResolver {
    @Inject()
    private service: CustomerCertificateAttachmentService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomerCertificateAttachments' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customerCertificateAttachments: CustomerCertificateAttachment[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customerCertificateAttachments: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomerCertificateAttachment], { name: 'customerCertificateAttachments' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<CustomerCertificateAttachment[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomerCertificateAttachment, { name: 'customerCertificateAttachment' })
    async searchRow(
        @Ctx() ctx: Context,
        @Arg('id', () => ID) id: number
    ): Promise<CustomerCertificateAttachment> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerCertificateAttachment, { name: 'createCustomerCertificateAttachment' })
    async create(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateInput
    ): Promise<CustomerCertificateAttachment> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => TCustomerCertificateAttachment, { name: 'updateCustomerCertificateAttachment' })
    async update(
        @Ctx() ctx: Context,
        @Arg('input') input: UpdateInput
    ): Promise<CustomerCertificateAttachment> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor()
    @Mutation(() => Boolean, { name: 'deleteCustomerCertificateAttachment' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    @Inject()
    private customerCertificateService: CustomerCertificateService;
    @FieldResolver(() => TCustomerCertificate, { description: '證照' })
    async certificate(
        @Root() model: CustomerCertificateAttachment
    ): Promise<CustomerCertificate | undefined> {
        return await this.customerCertificateService.findOne({ id: model.certificate?.id });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(
        @Root() model: CustomerCertificateAttachment
    ): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUser?.id });
    }

    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(
        @Root() model: CustomerCertificateAttachment
    ): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUser?.id });
    }
}
