import type { EyeServiceOrder } from '@clinico/mikro-orm-persistence/models/maintenance/eyeServiceOrder.model';
import type { PublicCity } from '@clinico/mikro-orm-persistence/models/public/city.model';
import type { PublicCreditPeriod } from '@clinico/mikro-orm-persistence/models/public/creditPeriod.model';
import type { PublicDistrict } from '@clinico/mikro-orm-persistence/models/public/district.model';
import type { PublicProvince } from '@clinico/mikro-orm-persistence/models/public/province.model';
import type { PublicRegion } from '@clinico/mikro-orm-persistence/models/public/region.model';
import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { Bid } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/bid.model';
import type { ContactPerson } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/contactPerson.model';
import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import type { CustomerArea } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerArea.model';
import type { CustomerAttachment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerAttachment.model';
import type { CustomerCategory } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCategory.model';
import type { CustomerCertificate } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerCertificate.model';
import { CustomerEquipment } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerEquipment.model';
import type { CustomerGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerGroup.model';
import type { CustomerProperty } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerProperty.model';
import type { CustomerType } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customerType.model';
import type { CustomersPrimaryUser } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customersPrimaryUser.model';
import type { SalesTeamGroup } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import type { SalesTeamUnit } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamUnit.model';
import { URLResolver } from 'graphql-scalars';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    ID,
    Info,
    Mutation,
    Query,
    Resolver,
    Root,
} from 'type-graphql';
import { Inject, Service } from 'typedi';

import { configs } from '~/app.config';
import { EnumPermissionCode } from '~/constants';
import { UserAuthInterceptor } from '~/middlewares/auth.middleware';
import { TBid } from '~/modules/bid/bid/bid.type';
import { EnumBPMForm } from '~/modules/bpm/formInstance/formInstance.type';
import { TContactPerson } from '~/modules/contactPerson/contactPerson/contactPerson.type';
import { TEyeServiceOrder } from '~/modules/eyeServiceOrder/eyeServiceOrder/eyeServiceOrder.type';
import { CityService } from '~/modules/public/city/city.service';
import { TCity } from '~/modules/public/city/city.type';
import { CreditPeriodService } from '~/modules/public/creditPeriod/creditPeriod.service';
import { TCreditPeriod } from '~/modules/public/creditPeriod/creditPeriod.type';
import { DistrictService } from '~/modules/public/district/district.service';
import { TDistrict } from '~/modules/public/district/district.type';
import { ProvinceService } from '~/modules/public/province/province.service';
import { TProvince } from '~/modules/public/province/province.type';
import { RegionService } from '~/modules/public/region/region.service';
import { TRegion } from '~/modules/public/region/region.type';
import { SalesTeamGroupService } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.service';
import { TSalesTeamGroup } from '~/modules/salesTeam/salesTeamGroup/salesTeamGroup.type';
import { TSalesTeamUnit } from '~/modules/salesTeam/salesTeamUnit/salesTeamUnit.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';
import type { Context } from '~/utils/interfaces/apolloContext.interface';

import { CustomerAreaService } from '../customerArea/customerArea.service';
import { TCustomerArea } from '../customerArea/customerArea.type';
import { TCustomerAttachment } from '../customerAttachment/customerAttachment.type';
import { CustomerCategoryService } from '../customerCategory/customerCategory.service';
import { TCustomerCategory } from '../customerCategory/customerCategory.type';
import { TCustomerCertificate } from '../customerCertificate/customerCertificate.type';
import { TCustomerEquipment } from '../customerEquipment/customerEquipment.type';
import { CustomerGroupService } from '../customerGroup/customerGroup.service';
import { TCustomerGroup } from '../customerGroup/customerGroup.type';
import { TCustomersPrimaryUser } from '../customerPrimaryUser/customerPrimaryUser.type';
import { FormattedProperty, TCustomerProperty } from '../customerProperty/customerProperty.type';
import { CustomerPropertyTypeService } from '../customerPropertyType/customerPropertyType.service';
import { CustomerTypeService } from '../customerType/customerType.service';
import { TCustomerType } from '../customerType/customerType.type';
import {
    CreateCustomerToContactPeopleInput,
    CreateInput,
    DeleteCustomerToContactPeopleInput,
    UpdateInput,
} from './customer.input';
import { CustomerService } from './customer.service';
import { PaginatedObjects, SearchArgs, TCustomer } from './customer.type';

@Service()
@Resolver(() => TCustomer)
export class CustomerResolver {
    @Inject()
    private service: CustomerService;

    @UserAuthInterceptor()
    @Query(() => PaginatedObjects, { name: 'paginatedCustomers' })
    async searchPaginatedRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<{ count: number; customers: Customer[] }> {
        const options = { pagination, ctx, count: true };
        const { rows, count } = await this.service.search(filters, options);
        return { count: count ?? 0, customers: rows };
    }

    @UserAuthInterceptor()
    @Query(() => [TCustomer], { name: 'customers' })
    async searchAllRows(
        @Ctx() ctx: Context,
        @Info() info,
        @Args() { filters, pagination }: SearchArgs
    ): Promise<Customer[]> {
        const options = { pagination, ctx };
        const { rows } = await this.service.search(filters, options);
        return rows;
    }

    @UserAuthInterceptor()
    @Query(() => TCustomer, { name: 'customer' })
    async searchRow(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<Customer> {
        const row = await this.service.findOneOrError({ id });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['customer.create'])
    @Mutation(() => TCustomer, { name: 'createCustomer' })
    async create(@Ctx() ctx: Context, @Arg('input') input: CreateInput): Promise<Customer> {
        const row = await this.service.createByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['customer.update'])
    @Mutation(() => TCustomer, { name: 'updateCustomer' })
    async update(@Ctx() ctx: Context, @Arg('input') input: UpdateInput): Promise<Customer> {
        const row = await this.service.updateByGrpc(input, { ctx });
        return row;
    }

    @UserAuthInterceptor(EnumPermissionCode['customer.delete'])
    @Mutation(() => Boolean, { name: 'deleteCustomer' })
    async delete(@Ctx() ctx: Context, @Arg('id', () => ID) id: number): Promise<boolean> {
        const res = await this.service.deleteByGrpc({ id }, { ctx });
        return res;
    }

    /**
     * ISSUE:
     * 1. ERP 主要負責人員認為 `createCustomer.contactPersonIds` 跟這段功能重疊。
     * 2. 而這個設計是為了解決前端的「聯絡人的分頁機制」增刪問題。
     * 3. 如果前端已全數切換到 `createCustomer`，再拔掉此功能。
     **/
    @UserAuthInterceptor([EnumPermissionCode['contact_people.update']])
    @Mutation(() => TCustomer, { name: 'createCustomerToContactPeople' })
    async createCustomerToContactPeople(
        @Ctx() ctx: Context,
        @Arg('input') input: CreateCustomerToContactPeopleInput
    ): Promise<Customer> {
        const row = await this.service.createContactPersonRelationsByGrpc(input, { ctx });
        return row;
    }

    /**
     * ISSUE:
     * 1. ERP 主要負責人員認為 `update.contactPersonIds` 跟這段功能重疊。
     * 2. 而這個設計是為了解決前端的「聯絡人的分頁機制」增刪問題。
     * 3. 如果前端已全數切換到 `createCustomer`，再拔掉此功能。
     **/
    @UserAuthInterceptor([EnumPermissionCode['contact_people.update']])
    @Mutation(() => TCustomer, { name: 'deleteCustomerToContactPeople' })
    async deleteCustomerToContactPeople(
        @Ctx() ctx: Context,
        @Arg('input') input: DeleteCustomerToContactPeopleInput
    ): Promise<Customer> {
        const row = await this.service.deleteContactPersonRelationsByGrpc(input, { ctx });
        return row;
    }

    @Inject()
    private regionService: RegionService;
    @FieldResolver(() => TRegion, { description: '區域' })
    async region(@Root() model: Customer): Promise<PublicRegion | undefined> {
        return await this.regionService.findOne({ id: model.salesTeamGroup.region?.id });
    }

    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @FieldResolver(() => TSalesTeamGroup, { description: '業務團隊組織' })
    async salesTeamGroup(@Root() model: Customer): Promise<SalesTeamGroup | undefined> {
        return await this.salesTeamGroupService.findOne({ id: model.salesTeamGroup.id });
    }

    @Inject()
    private customerGroupService: CustomerGroupService;
    @FieldResolver(() => TCustomerGroup, { description: '集團', nullable: true })
    async group(@Root() model: Customer): Promise<CustomerGroup | undefined> {
        return await this.customerGroupService.findOne({ id: model.group?.id });
    }

    @FieldResolver(() => TCustomer, { description: '上層客戶', nullable: true })
    async parent(@Root() model: Customer): Promise<Customer | undefined> {
        return await this.service.findOne({ id: model.parent?.id });
    }

    @Inject()
    private customerTypeService: CustomerTypeService;
    @FieldResolver(() => TCustomerType, { description: '類型', nullable: true })
    async type(@Root() model: Customer): Promise<CustomerType | undefined> {
        return await this.customerTypeService.findOne({ id: model.type?.id });
    }

    @Inject()
    private customerCategoryService: CustomerCategoryService;
    @FieldResolver(() => TCustomerCategory, { description: '類別', nullable: true })
    async category(@Root() model: Customer): Promise<CustomerCategory | undefined> {
        return await this.customerCategoryService.findOne({ id: model.category?.id });
    }

    @Inject()
    private customerAreaService: CustomerAreaService;
    @FieldResolver(() => TCustomerArea, { description: '分區', nullable: true })
    async area(@Root() model: Customer): Promise<CustomerArea | undefined> {
        return await this.customerAreaService.findOne({ id: model.area?.id });
    }

    @Inject()
    private provinceService: ProvinceService;
    @FieldResolver(() => TProvince, { description: '省級、一級行政區', nullable: true })
    async province(@Root() model: Customer): Promise<PublicProvince | undefined> {
        return await this.provinceService.findOne({ id: model.province?.id });
    }

    @Inject()
    private cityService: CityService;
    @FieldResolver(() => TCity, { description: '市級、二級行政區', nullable: true })
    async city(@Root() model: Customer): Promise<PublicCity | undefined> {
        return await this.cityService.findOne({ id: model.city?.id });
    }

    @Inject()
    private districtService: DistrictService;
    @FieldResolver(() => TDistrict, { description: '縣級、三級行政區', nullable: true })
    async district(@Root() model: Customer): Promise<PublicDistrict | undefined> {
        return await this.districtService.findOne({ id: model.district?.id });
    }

    @Inject()
    private creditPeriodService: CreditPeriodService;
    @FieldResolver(() => TCreditPeriod, { description: '信用期限（科林評定）', nullable: true })
    async creditPeriod(@Root() model: Customer): Promise<PublicCreditPeriod | undefined> {
        return await this.creditPeriodService.findOne({ id: model.creditPeriodId });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '資料建立人員', nullable: true })
    async createdUser(@Root() model: Customer): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.createdUser?.id });
    }
    @FieldResolver(() => TUser, { description: '資料修改人員', nullable: true })
    async updatedUser(@Root() model: Customer): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.updatedUser?.id });
    }

    @FieldResolver(() => [TContactPerson], { description: '聯絡人' })
    async contactPeople(@Root() model: Customer): Promise<ContactPerson[]> {
        if (!model.customersContactPeople.isInitialized()) {
            await model.customersContactPeople.init({
                populate: ['contactPerson'],
                where: { contactPerson: { deleted: false } },
            });
        }
        return model.customersContactPeople.getItems().map((item) => item.contactPerson);
    }

    @Inject()
    private customerPropertyTypeService: CustomerPropertyTypeService;
    @FieldResolver(() => [FormattedProperty], { description: '調整格式後的屬性' })
    async formattedProperties(
        @Ctx() ctx: Context,
        @Root() model: Customer
    ): Promise<FormattedProperty[]> {
        const { rows: types } = await this.customerPropertyTypeService.search({}, { ctx });

        if (!model.customersProperties.isInitialized()) {
            await model.customersProperties.init({ populate: ['customerProperty.type'] });
        }
        const items = model.customersProperties.getItems();

        const map = new Map<number, CustomerProperty[]>();
        items.forEach((item) => {
            const prev = map.get(item.customerProperty.typeId);
            map.set(item.customerProperty.typeId, [...(prev ?? []), item.customerProperty]);
        });
        return types.map((t) => ({ propertyType: t, properties: map.get(t.id) ?? [] }));
    }

    @FieldResolver(() => [TCustomerProperty], { description: '屬性' })
    async properties(@Root() model: Customer): Promise<CustomerProperty[]> {
        if (!model.customersProperties.isInitialized()) {
            await model.customersProperties.init({ populate: ['customerProperty.type'] });
        }
        const items = model.customersProperties.getItems().map((item) => item.customerProperty);
        return items;
    }

    @FieldResolver(() => [TCustomersPrimaryUser], { description: '主要負責業務' })
    async primaryUsers(@Root() model: Customer): Promise<CustomersPrimaryUser[]> {
        if (!model.customersPrimaryUsers.isInitialized()) {
            await model.customersPrimaryUsers.init();
        }
        return model.customersPrimaryUsers.getItems();
    }

    @FieldResolver(() => [TUser], { description: '負責業務' })
    async users(@Root() model: Customer): Promise<PublicUser[]> {
        if (!model.customersUsers.isInitialized()) {
            await model.customersUsers.init({ populate: ['user'] });
        }
        return model.customersUsers.getItems().map((item) => item.user);
    }

    @FieldResolver(() => [TCustomerCertificate], { description: '證照' })
    async certificates(@Root() model: Customer): Promise<CustomerCertificate[]> {
        if (!model.customerCertificates.isInitialized()) {
            await model.customerCertificates.init({ where: { deleted: false } });
        }
        return model.customerCertificates.getItems();
    }

    @FieldResolver(() => [TCustomerEquipment], { description: '設備' })
    async equipments(@Root() model: Customer): Promise<CustomerEquipment[]> {
        if (!model.customerEquipments.isInitialized()) {
            await model.customerEquipments.init({ where: { deleted: false } });
        }
        return model.customerEquipments.getItems();
    }

    @FieldResolver(() => [TCustomerAttachment], { description: '附件' })
    async attachments(@Root() model: Customer): Promise<CustomerAttachment[]> {
        if (!model.customerAttachments.isInitialized()) {
            await model.customerAttachments.init({ where: { deleted: false } });
        }
        return model.customerAttachments.getItems();
    }

    @FieldResolver(() => [TCustomer], { description: '下層客戶' })
    async children(@Root() model: Customer): Promise<Customer[]> {
        const { rows } = await this.service.search({ parentId: model.id });
        return rows;
    }

    @FieldResolver(() => [TEyeServiceOrder])
    async eyeServiceOrders(@Root() model: Customer): Promise<EyeServiceOrder[]> {
        if (!model.eyeServiceOrders.isInitialized()) {
            await model.eyeServiceOrders.init({ where: { deleted: false } });
        }
        return model.eyeServiceOrders.getItems();
    }

    @FieldResolver(() => [TSalesTeamUnit])
    async salesTeamUnits(@Root() model: Customer): Promise<SalesTeamUnit[]> {
        if (!model.customersSalesTeamUnits.isInitialized()) {
            await model.customersSalesTeamUnits.init({
                populate: ['salesTeamUnit'],
                where: { salesTeamUnit: { deleted: false } },
            });
        }
        return model.customersSalesTeamUnits.getItems().map((item) => item.salesTeamUnit);
    }
    // TODO: Bid
    @FieldResolver(() => [TBid], { description: '投招標' })
    async bids(@Root() model: Customer): Promise<Bid[]> {
        if (!model.bids.isInitialized()) {
            await model.bids.init({ where: { deleted: false } });
        }
        return model.bids.getItems();
    }
}
