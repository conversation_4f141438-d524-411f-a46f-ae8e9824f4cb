import { BaseError } from '@clinico/base-error';
import { INTERNAL_SERVER_ERROR } from 'http-status';
import { Service } from 'typedi';

import { Client } from '@packages/erp-protobuf';
import {
    CreateContactPersonRelationParams,
    CreateCustomerParams,
    CustomerResult,
    DeleteContactPersonRelationParams,
    DeleteCustomerParams,
    UpdateCustomerParams,
} from '@packages/erp-protobuf/generated/customer/customer_pb';

@Service()
export class CustomerGrpcRepo {
    serviceNodes: string[] = ['customer', 'Customer'];

    async create(params: CreateCustomerParams): Promise<CustomerResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerResult>('create', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Customer.create]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async update(params: UpdateCustomerParams): Promise<CustomerResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerResult>('update', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Customer.update]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async delete(params: DeleteCustomerParams): Promise<CustomerResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerResult>('delete', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Customer.delete]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async createContactPersonRelations(
        params: CreateContactPersonRelationParams
    ): Promise<CustomerResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerResult>('createContactPersonRelations', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Customer.createContactPersonRelations]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }

    async deleteContactPersonRelations(
        params: DeleteContactPersonRelationParams
    ): Promise<CustomerResult> {
        try {
            const client = new Client(this.serviceNodes);
            const res = await client.call<CustomerResult>('deleteContactPersonRelations', params);

            return res;
        } catch (err: any) {
            const message = ['[gRPC.Customer.deleteContactPersonRelations]', err.message].join(' ');
            throw new BaseError(message, INTERNAL_SERVER_ERROR);
        }
    }
}
