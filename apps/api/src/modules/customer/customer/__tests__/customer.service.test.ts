import { EnumSalesTeamGroupCode } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';

import { CustomerService } from '../customer.service';

describe('appendFilterByDefault', () => {
    const appendFilterByDefault = CustomerService.prototype['appendFilterByDefault'];

    test('should build correct filters when no param is provided', async () => {
        const filters = [];
        const params = {};
        appendFilterByDefault(filters, params);

        expect(filters).toEqual([{ deleted: false }, { isActive: true }]);
    });

    test('should build correct filters when params.id is provided', async () => {
        const filters = [];
        const params = { id: 1 };
        appendFilterByDefault(filters, params);

        expect(filters).toEqual([{ deleted: false }]);
    });

    test('should build correct filters when params.ids is provided', async () => {
        const filters = [];
        const params = { ids: [1, 2] };
        appendFilterByDefault(filters, params);

        expect(filters).toEqual([{ deleted: false }]);
    });

    test('should build correct filters when params.ids with empty array is provided', async () => {
        const filters = [];
        const params = { ids: [] };
        appendFilterByDefault(filters, params);

        expect(filters).toEqual([{ deleted: false }]);
    });

    test('should build correct filters when params.isActive=undefined is provided', async () => {
        const filters = [];
        const params = { isActive: undefined };
        appendFilterByDefault(filters, params);

        expect(filters).toEqual([{ deleted: false }, { isActive: true }]);
    });

    test('should build correct filters when params.isActive=true is provided', async () => {
        const filters = [];
        const params = { isActive: true };
        appendFilterByDefault(filters, params);

        expect(filters).toEqual([{ deleted: false }]);
    });

    test('should build correct filters when params.isActive=false is provided', async () => {
        const filters = [];
        const params = { isActive: false };
        appendFilterByDefault(filters, params);

        expect(filters).toEqual([{ deleted: false }]);
    });

    test('should build correct filters when params.isActive=null is provided', async () => {
        const filters = [];
        const params = { isActive: null };
        appendFilterByDefault(filters, params as any);

        expect(filters).toEqual([{ deleted: false }]);
    });
});

describe('appendFilterByAccessControl', () => {
    const appendFilterByAccessControl = CustomerService.prototype['appendFilterByAccessControl'];

    test('should append filter by empty array when no param is provided', async () => {
        const filters = [];
        const params = {};
        const options = {};
        await appendFilterByAccessControl(filters, params, options);

        expect(filters).toEqual([
            {
                $or: [
                    {
                        customersSalesTeamUnits: {
                            salesTeamUnit: {
                                salesTeam: { id: { $in: [] } },
                            },
                        },
                    },
                ],
            },
        ]);
    });

    test('should append filter by id when params.id is provided', async () => {
        const filters = [];
        const params = { id: 1 };
        const options = {};
        await appendFilterByAccessControl(filters, params, options);

        expect(filters).toEqual([
            {
                $or: [
                    { id: 1 },
                    {
                        customersSalesTeamUnits: {
                            salesTeamUnit: {
                                salesTeam: { id: { $in: [] } },
                            },
                        },
                    },
                ],
            },
        ]);
    });

    test('should append filter by ids when params.ids is provided', async () => {
        const filters = [];
        const params = { id: 1 };
        const options = {};
        await appendFilterByAccessControl(filters, params, options);

        expect(filters).toEqual([
            {
                $or: [
                    { id: 1 },
                    {
                        customersSalesTeamUnits: {
                            salesTeamUnit: {
                                salesTeam: { id: { $in: [] } },
                            },
                        },
                    },
                ],
            },
        ]);
    });

    test('should append filter by currentUser when currentUser.id is provided', async () => {
        const filters = [];
        const params = {};
        const options: any = { ctx: { currentUser: { id: 1 } } };
        await appendFilterByAccessControl(filters, params, options);

        expect(filters).toEqual([
            {
                $or: [
                    {
                        customersSalesTeamUnits: {
                            salesTeamUnit: {
                                user: { id: 1 },
                            },
                        },
                    },
                    {
                        customersSalesTeamUnits: {
                            salesTeamUnit: {
                                salesTeam: { id: { $in: [] } },
                            },
                        },
                    },
                ],
            },
        ]);
    });

    test('should append filter by currentPermission when allowUserIds is provided', async () => {
        const filters = [];
        const params = {};
        const options: any = { ctx: { currentPermission: { allowUserIds: [1, 2] } } };
        await appendFilterByAccessControl(filters, params, options);

        expect(filters).toEqual([
            {
                $or: [
                    {
                        customersSalesTeamUnits: {
                            salesTeamUnit: {
                                user: { id: { $in: [1, 2] } },
                            },
                        },
                    },
                    {
                        customersSalesTeamUnits: {
                            salesTeamUnit: {
                                salesTeam: { id: { $in: [] } },
                            },
                        },
                    },
                ],
            },
        ]);
    });

    test('should append filter by currentPermission when allowSalesTeamIds is provided', async () => {
        const filters = [];
        const params = {};
        const options: any = { ctx: { currentPermission: { allowSalesTeamIds: [1, 2] } } };
        await appendFilterByAccessControl(filters, params, options);

        expect(filters).toEqual([
            {
                $or: [
                    {
                        customersSalesTeamUnits: {
                            salesTeamUnit: {
                                salesTeam: { id: { $in: [1, 2] } },
                            },
                        },
                    },
                ],
            },
        ]);
    });

    test('should append filter by customer "Dealer" when currentSalesTeamGroup.code is CHN_EYE', async () => {
        const filters = [];
        const params = {};
        const options: any = {
            ctx: { currentSalesTeamGroup: { code: EnumSalesTeamGroupCode.CHN_EYE } },
        };
        await appendFilterByAccessControl(filters, params, options);

        expect(filters).toEqual([
            {
                $or: [
                    {
                        customersSalesTeamUnits: {
                            salesTeamUnit: {
                                salesTeam: { id: { $in: [] } },
                            },
                        },
                    },
                    { type: { code: 'Dealer' } },
                ],
            },
        ]);
    });

    test('should append filter by all customers when salesTeamsUsers.salesTeams include E-K-E0JK', async () => {
        const filters = [];
        const params = {};
        const options: any = {
            ctx: {
                currentSalesTeamGroup: { code: EnumSalesTeamGroupCode.CHN_EYE },
                currentUser: {
                    salesTeamsUsers: {
                        isInitialized: () => true,
                        getItems: () => [{ salesTeam: { id: 1, code: 'E-K-E0JK' } }],
                    },
                },
            },
        };
        await appendFilterByAccessControl(filters, params, options);

        expect(filters).toEqual([
            {
                $or: [
                    {
                        customersSalesTeamUnits: {
                            salesTeamUnit: {
                                salesTeam: { id: { $in: [] } },
                            },
                        },
                    },
                    { type: { code: 'Dealer' } },
                    { salesTeamGroup: { code: 'CHN_EYE' } },
                ],
            },
        ]);
    });
});
