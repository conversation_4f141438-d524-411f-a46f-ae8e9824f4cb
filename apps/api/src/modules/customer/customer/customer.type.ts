import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import type { EnumLastGmpBpmStatus } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import type { EnumSalesTeamGroupCode } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { ICustomer } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customer.model';
import { EnumLastGmpBpmStatus as IEnumLastGmpBpmStatus } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customer.model';
import { EnumCustomerTypeCode } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customerType.model';
import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';
import { EnumCustomerClassification } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customer.model';
import { FilterInput as VisitFilterInput } from '~/modules/visit/visit/visit.type';
import { BaseFilterInput, PaginatedArgs, PaginatedObject } from '~/utils/types/base.type';

@ObjectType('Customer', { implements: ICustomer })
export class TCustomer extends ICustomer {}

@ObjectType('PaginatedCustomers')
export class PaginatedObjects extends PaginatedObject {
    @Field(() => [TCustomer], { nullable: true })
    customers: Customer[];
}

@InputType('CustomerFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true, description: '區域' })
    regionId?: number;

    @Field({
        nullable: true,
        description: '關鍵字（同時查詢「名稱、內部編號、統一編號、醫事編號」欄位）',
    })
    keyword?: string;

    @Field(() => ID, { nullable: true, description: '業務團隊組織' })
    salesTeamGroupId?: number;

    @Field(() => ID, { nullable: true, description: '集團' })
    groupId?: number;

    @Field(() => ID, { nullable: true, description: '上層客戶' })
    parentId?: number;

    @Field(() => ID, { nullable: true, description: '類型' })
    typeId?: number;

    @Field(() => [ID], { nullable: true, description: '類型' })
    typeIds?: number[];

    @Field(() => EnumCustomerTypeCode, { nullable: true, description: '類型編號' })
    typeCode?: EnumCustomerTypeCode;

    @Field(() => ID, { nullable: true, description: '分類' })
    categoryId?: number;

    @Field({ nullable: true, description: '編號（鼎新／內部通用）' })
    code?: string;

    @Field({ nullable: true, description: '編號（用友）' })
    yonyouCode?: string;

    @Field({ nullable: true, description: '營業統一編號' })
    businessCode?: string;

    @Field({ nullable: true, description: '醫事機構代碼' })
    medicalCode?: string;

    @Field({ nullable: true, description: '名稱' })
    name?: string;

    @Field({ nullable: true, description: '是否為「首營」客戶' })
    isGmp?: boolean;

    @Field({ nullable: true, description: '是否有生效的「首營資料」證照' })
    hasEffectiveGmpCertificate?: boolean;

    @Field(() => ID, { nullable: true, description: '分區' })
    areaId?: number;

    @Field(() => ID, { nullable: true, description: '省級、一級行政區' })
    provinceId?: number;

    @Field(() => ID, { nullable: true, description: '市級、二級行政區' })
    cityId?: number;

    @Field(() => ID, { nullable: true, description: '縣級、三級行政區' })
    districtId?: number;

    @Field(() => ID, { nullable: true, description: '聯絡人' })
    contactPersonId?: number;

    @Field(() => ID, { nullable: true, description: '主要負責業務' })
    primaryUserId?: number;

    @Field(() => [ID], { nullable: true, description: '主要負責業務（複選）' })
    primaryUserIds?: number[];

    @Field(() => ID, { nullable: true, description: '負責業務' })
    userId?: number;

    @Field(() => IEnumLastGmpBpmStatus, {
        nullable: true,
        description: '最近 BPM 首營資料申請狀態',
    })
    lastGmpBpmStatus?: EnumLastGmpBpmStatus;

    @Field(() => [ID], { nullable: true, description: '負責業務（複選）' })
    userIds?: number[];

    @Field(() => ID, { nullable: true, description: '负责业务位置' })
    salesTeamUnitId?: number;

    @Field(() => [ID], { nullable: true, description: '负责业务位置（複選）' })
    salesTeamUnitIds?: number[];

    @Field(() => [ID], { nullable: true, description: '屬性類型（複選）' })
    propertyTypeIds?: number[];

    @Field(() => [ID], { nullable: true, description: '屬性（複選）' })
    propertyIds?: number[];

    @Field({
        nullable: true,
        defaultValue: true,
        description: '是否啟用（查詢條件設置為 Null 時，同時取得「啟用」及「禁用」的資料）',
    })
    isActive?: boolean;

    @Field(() => Boolean, { nullable: true, description: '是否有「已拜訪」的拜訪資料' })
    hasVisitedVisit?: boolean;

    @Field(() => VisitFilterInput, { nullable: true, description: '拜訪查詢項' })
    visit?: VisitFilterInput;

    /** 業務團隊組織編號（複選） */
    salesTeamGroupCodes?: EnumSalesTeamGroupCode[];

    /** 編號（NAV）（複選） */
    navCodes?: string[];

    @Field((type) => EnumCustomerClassification, {
        nullable: true,
        description: '分级',
    })
    classification?: EnumCustomerClassification;
}

@ArgsType()
export class SearchArgs extends PaginatedArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
