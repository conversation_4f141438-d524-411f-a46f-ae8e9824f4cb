import { Field, Float, ID, InputType } from 'type-graphql';
import { EnumCustomerClassification } from '@clinico/mikro-orm-type-graphql-persistence/models/salesRepWorkstation/customer.model';
@InputType('CustomerInput')
export class CommonInput {
    @Field(() => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field(() => ID, { nullable: true, description: '上層客戶' })
    parentId?: number;

    @Field(() => ID, { nullable: true, description: '集團' })
    groupId?: number;

    @Field(() => ID, { nullable: true, description: '類型' })
    typeId?: number;

    @Field(() => ID, { nullable: true, description: '分類' })
    categoryId?: number;

    @Field(() => ID, { nullable: true, description: '分區' })
    areaId?: number;

    @Field({ nullable: true, description: '名稱' })
    name?: string;

    @Field({ nullable: true, description: '簡稱' })
    shortName?: string;

    @Field({ nullable: true, description: '編號（鼎新／內部通用）' })
    code?: string;

    @Field({ nullable: true, description: '編號（用友）', deprecationReason: '禁止新增／更新' })
    yonyouCode?: string;

    @Field({ nullable: true, description: '編號（NAV）' })
    navCode?: string;

    @Field({ nullable: true, description: '營業統一編號' })
    businessCode?: string;

    @Field({ nullable: true, description: '醫事機構代碼' })
    medicalCode?: string;

    @Field({ nullable: true, description: '轉介編號' })
    referenceCode?: string;

    @Field({ nullable: true, description: '座機電話' })
    phone?: string;

    @Field({ nullable: true, description: '行動電話' })
    mobile?: string;

    @Field({ nullable: true, description: '電子信箱' })
    email?: string;

    @Field({ nullable: true, description: '傳真電話' })
    fax?: string;

    @Field(() => ID, { nullable: true, description: '省級、一級行政區' })
    provinceId?: number;

    @Field(() => ID, { nullable: true, description: '市級、二級行政區' })
    cityId?: number;

    @Field(() => ID, { nullable: true, description: '縣級、三級行政區' })
    districtId?: number;

    @Field({ nullable: true, description: '地址' })
    address?: string;

    @Field({ nullable: true, description: '網站' })
    website?: string;

    @Field({ nullable: true, description: '備註' })
    memo?: string;

    @Field(() => Float, { nullable: true, description: '信用額度（科林評定）' })
    creditQuota?: string;

    @Field(() => ID, { nullable: true, description: '信用期限（科林評定）' })
    creditPeriodId?: number;

    @Field({ nullable: true, description: '收貨地址' })
    shippingAddress?: string;

    @Field({ nullable: true, description: '總負責聯絡人姓名' })
    contactPersonName?: string;

    @Field({ nullable: true, description: '總負責聯絡人電話' })
    contactPersonPhone?: string;

    @Field({ nullable: true, description: '銀行戶頭帳號' })
    bankAccountCode?: string;

    @Field({ nullable: true, description: '銀行戶頭名稱' })
    bankAccountName?: string;

    @Field({ nullable: true, description: '法人姓名' })
    legalPersonName?: string;

    @Field({ nullable: true, description: '開票單位名稱' })
    billingUnitName?: string;

    @Field({ nullable: true, description: '預設付款方式' })
    defaultPaymentMethodId?: number;

    @Field({ nullable: true, description: '預設配送方式' })
    defaultShippingMethodId?: number;

    @Field(() => [ID], { nullable: true, description: '屬性' })
    propertyIds?: number[];

    @Field(() => [ID], { nullable: true, description: '聯絡人' })
    contactPersonIds?: number[];

    @Field(() => [ID], { nullable: true, description: '業務團隊位置' })
    salesTeamUnitIds?: number[];

    @Field(() => [PrimaryUserInput], { nullable: true, defaultValue: '主要負責業務' })
    primaryUsers?: PrimaryUserInput[];

    deleted?: boolean;

    @Field((type) => EnumCustomerClassification, {
        nullable: true,
        description: '分级',
    })
    classification?: EnumCustomerClassification;
}

@InputType('CustomerCreateInput')
export class CreateInput extends CommonInput {}

@InputType('CustomerUpdateInput')
export class UpdateInput extends CommonInput {
    @Field(() => ID)
    id: number;
}


@InputType('CreateCustomerToContactPeopleInput')
export class CreateCustomerToContactPeopleInput {
    @Field(() => ID, { defaultValue: '客戶' })
    id: number;

    @Field(() => [ID], { defaultValue: '聯絡人' })
    contactPersonIds: number[];
}

@InputType('DeleteCustomerToContactPeopleInput')
export class DeleteCustomerToContactPeopleInput {
    @Field(() => ID, { defaultValue: '客戶' })
    id: number;

    @Field(() => [ID], { defaultValue: '聯絡人' })
    contactPersonIds: number[];
}

@InputType('CustomerPrimaryUserInput')
export class PrimaryUserInput {
    @Field(() => ID, { description: '主要負責業務' })
    userId: number;

    @Field(() => ID, { description: '業務團隊' })
    salesTeamId: number;
}
