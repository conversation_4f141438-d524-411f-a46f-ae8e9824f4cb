import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import type { AutoPath } from '@mikro-orm/core/typings';

import type { Context } from '~/utils/interfaces/apolloContext.interface';
import { PaginationInput } from '~/utils/types/base.type';

export interface CountOptions {
    ctx?: Context;
    skipAccessControl?: boolean;
}

export interface SearchOptions {
    ctx?: Context;
    pagination?: PaginationInput;
    count?: boolean;
    fields?: ['id'];
    skipAccessControl?: boolean;
}

export type PopulateType = AutoPath<Customer, '*' | 'salesTeamGroup.*'>[];
