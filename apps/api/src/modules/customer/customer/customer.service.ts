import { BaseError } from '@clinico/base-error';
import {
    Customer,
    EnumLastGmpBpmStatus,
} from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import { EnumSalesTeamGroupCode } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import { EnumVisitStatus } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/visit.model';
import { FilterQuery } from '@mikro-orm/core';
import _ from 'lodash';
import { Inject, Service } from 'typedi';

import { handler } from '@packages/erp-protobuf';
import {
    CreateContactPersonRelationParams,
    CreateCustomerParams,
    CreateCustomerParams_PrimaryUserParams,
    DeleteContactPersonRelationParams,
    DeleteCustomerParams,
    UpdateCustomerParams,
} from '@packages/erp-protobuf/generated/customer/customer_pb';

import { VisitService } from '~/modules/visit/visit/visit.service';
import { SearchOptions as VisitSearchOptions } from '~/modules/visit/visit/visit.service.type';
import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';

import { EnumCustomerTypeCode } from '../customerType/customerType.enum';
import { CustomerGrpcRepo } from './customer.grpc.repo';
import {
    CreateCustomerToContactPeopleInput,
    CreateInput,
    DeleteCustomerToContactPeopleInput,
    UpdateInput,
} from './customer.input';
import { CountOptions, PopulateType, SearchOptions } from './customer.service.type';
import { FilterInput } from './customer.type';

@Service()
export class CustomerService extends BaseService<Customer> {
    protected entity = Customer;
    private gRPCrepo = new CustomerGrpcRepo();

    @Inject()
    private visitService: VisitService;

    async count(params?: FilterInput, options?: CountOptions): Promise<number> {
        const em = this.em.fork();

        const where: FilterQuery<Customer>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();

        // Default
        this.appendFilterByDefault(where, params);
        // Access control
        if (!options?.skipAccessControl) {
            await this.appendFilterByAccessControl(where, params, options);
        }
        // Filter input
        await this.appendFilterByParams(where, params, options);

        const repo = em.getRepository(this.entity);
        const count = await repo.count({ $and: where });

        return count;
    }

    async search(
        params?: FilterInput,
        options?: SearchOptions
    ): Promise<{ count: number | null; rows: Customer[] }> {
        const em = this.em.fork();

        const where: FilterQuery<Customer>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();

        // Default
        this.appendFilterByDefault(where, params);
        // Access control
        if (!options?.skipAccessControl) {
            await this.appendFilterByAccessControl(where, params, options);
        }
        // Filter input
        await this.appendFilterByParams(where, params, options);

        const populating = this.buildPopulating();

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                fields: options?.fields ?? undefined,
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: options?.fields ? undefined : populating,
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }

    private appendFilterByDefault(where: FilterQuery<Customer>[], params?: FilterInput) {
        where.push({ deleted: false });

        // For use cases
        if (!(params?.id || params?.ids || !_.isUndefined(params?.isActive))) {
            // http://asking.clinico.com.tw/issues/65756
            // 預設：業務系統不可查詢到「停用」的客戶
            where.push({ isActive: true });
        }
    }

    private async appendFilterByAccessControl(
        where: FilterQuery<Customer>[],
        params?: FilterInput,
        options?: { ctx?: Context }
    ): Promise<void> {
        const accesses: FilterQuery<Customer> = [];

        // For field/mutation resolvers
        if (params?.id) {
            // field/mutation resolvers
            accesses.push({ id: params.id });
        }
        if (params?.ids) {
            // Data loader
            accesses.push({ id: { $in: params.ids } });
        }

        // For permissions
        const currentUser = options?.ctx?.currentUser;
        if (currentUser?.id) {
            accesses.push({
                customersSalesTeamUnits: {
                    salesTeamUnit: {
                        user: { id: currentUser.id },
                    },
                },
            });
        }
        const allowUserIds = options?.ctx?.currentPermission?.allowUserIds || [];
        if (allowUserIds.length) {
            accesses.push({
                customersSalesTeamUnits: {
                    salesTeamUnit: {
                        user: { id: { $in: allowUserIds } },
                    },
                },
            });
        }
        const allowSalesTeamIds = options?.ctx?.currentPermission?.allowSalesTeamIds || [];
        if (allowSalesTeamIds) {
            accesses.push({
                customersSalesTeamUnits: {
                    salesTeamUnit: {
                        salesTeam: { id: { $in: allowSalesTeamIds } },
                    },
                },
            });
        }

        // For use cases
        switch (options?.ctx?.currentSalesTeamGroup?.code) {
            case EnumSalesTeamGroupCode.CHN_EYE: {
                // START: http://asking.clinico.com.tw/issues/65765
                // 類型為「經銷商」的客戶是可以顯示給「所有業務」看到
                accesses.push({ type: { code: EnumCustomerTypeCode.Dealer } });
                // END: http://asking.clinico.com.tw/issues/65765

                // START: http://asking.clinico.com.tw/issues/68833
                // 「眼健康團隊 E-K-E0JK」的業務可以檢視「所有客戶」
                const currentUser = options?.ctx?.currentUser;
                if (currentUser) {
                    if (!currentUser.salesTeamsUsers.isInitialized()) {
                        await currentUser.salesTeamsUsers.init({ populate: ['salesTeam'] });
                    }
                    const has = currentUser.salesTeamsUsers.getItems().some((item) => {
                        return item.salesTeam.code === 'E-K-E0JK';
                    });
                    if (has) {
                        accesses.push({ salesTeamGroup: { code: EnumSalesTeamGroupCode.CHN_EYE } });
                    }
                }
                // END: http://asking.clinico.com.tw/issues/68833

                break;
            }
            case EnumSalesTeamGroupCode.TWN_EYE: {
                // START: http://asking.clinico.com.tw/issues/87249
                // 所有業務都可以檢視所有客戶資訊
                accesses.push({ salesTeamGroup: { code: EnumSalesTeamGroupCode.TWN_EYE } });
                // END: http://asking.clinico.com.tw/issues/87249
                break;
            }
        }

        where.push({ $or: accesses });
    }

    private async appendFilterByParams(
        where: FilterQuery<Customer>[],
        params?: FilterInput,
        options?: SearchOptions
    ): Promise<void> {
        if (params?.regionId) {
            where.push({ salesTeamGroup: { region: { id: params.regionId } } });
        }
        if (params?.salesTeamGroupCodes) {
            where.push({ salesTeamGroup: { code: { $in: params.salesTeamGroupCodes } } });
        }
        if (params?.keyword) {
            // 透過不同欄位搜索客戶
            where.push({
                $or: [
                    { businessCode: { $ilike: `%${params.keyword}%` } },
                    { medicalCode: { $ilike: `%${params.keyword}%` } },
                    { code: { $ilike: `%${params.keyword}%` } },
                    { name: { $ilike: `%${params.keyword}%` } },
                ],
            });
        }
        if (params?.typeCode) {
            where.push({ type: { code: params.typeCode } });
        }
        if (params?.typeIds) {
            where.push({ type: { id: { $in: params.typeIds } } });
        }
        if (params?.primaryUserId) {
            where.push({
                customersSalesTeamUnits: {
                    salesTeamUnit: {
                        user: { id: params.primaryUserId },
                    },
                },
            });
        }
        if (params?.primaryUserIds) {
            where.push({
                customersSalesTeamUnits: {
                    salesTeamUnit: {
                        user: { $in: params.primaryUserIds },
                    },
                },
            });
        }
        if (params?.userId) {
            where.push({ customersUsers: { user: { id: params.userId } } });
        }
        if (params?.userIds) {
            where.push({ customersUsers: { user: { id: { $in: params.userIds } } } });
        }
        if (params?.salesTeamUnitId) {
            where.push({ customersSalesTeamUnits: { salesTeamUnit: { id: params.salesTeamUnitId } } });
        }
        if (params?.salesTeamUnitIds) {
            where.push({ customersSalesTeamUnits: { salesTeamUnit: { id: { $in: params.salesTeamUnitIds } } } });
        }
        if (params?.propertyTypeIds) {
            where.push({
                customersProperties: {
                    customerProperty: { type: { id: { $in: params.propertyTypeIds } } },
                },
            });
        }
        if (params?.propertyIds) {
            where.push({
                customersProperties: {
                    customerProperty: { id: { $in: params.propertyIds } },
                },
            });
        }
        if (params?.contactPersonId) {
            where.push({
                customersContactPeople: { contactPerson: { id: params.contactPersonId } },
            });
        }
        if (params?.navCodes) {
            where.push({ navCode: { $in: params.navCodes } });
        }
        if (params?.classification) {
            where.push({ classification: params.classification });
        }
        if (!_.isUndefined(params?.hasVisitedVisit)) {
            if (params?.hasVisitedVisit) {
                where.push({ visits: { status: EnumVisitStatus.Visited } });
            } else {
                const idOptions: SearchOptions = {
                    ...options,
                    fields: ['id'],
                    count: false,
                    pagination: undefined,
                };
                const idFilters: FilterInput = { ...params, hasVisitedVisit: true };
                const customers = await this.search(idFilters, idOptions);

                const ids = customers.rows.map((r) => r.id);
                where.push({ id: { $nin: ids } });
            }
        }
        const hasVisitParams = Object.entries(params?.visit ?? {}).some(([, v]) => {
            return !_.isUndefined(v);
        });
        if (hasVisitParams) {
            const visitSearchOptions: VisitSearchOptions = {
                ...options,
                fields: ['id'],
                count: false,
                pagination: undefined,
            };
            const { rows } = await this.visitService.search(params?.visit, visitSearchOptions);

            const ids = rows.map((r) => r.id);
            where.push({ visits: { id: { $in: ids } } });
        }
    }

    private buildPopulating(): PopulateType {
        const populating: PopulateType = [
            'salesTeamGroup.region',
            'group',
            'parent',
            'type',
            'area',
            'city',
            'district',
            'province',
            'createdUser',
            'updatedUser',
            'bids',
        ];
        return populating;
    }

    async createByGrpc(params: CreateInput, options?: { ctx?: Context }): Promise<Customer> {
        const inputs = new CreateCustomerParams({
            salesTeamGroupId: options?.ctx?.currentSalesTeamGroup?.id,
            regionId: options?.ctx?.currentSalesTeamGroup?.region?.id,
            parentId: params.parentId,
            groupId: params.groupId,
            typeId: params.typeId,
            categoryId: params.categoryId,
            areaId: params.areaId,
            medicalCode: params.medicalCode,
            businessCode: params.businessCode,
            navCode: params.navCode,
            referenceCode: params.referenceCode,
            provinceId: params.provinceId,
            cityId: params.cityId,
            districtId: params.districtId,
            address: params.address,
            email: params.email,
            fax: params.fax,
            name: params.name,
            shortName: params.shortName,
            phone: params.phone,
            mobile: params.mobile,
            website: params.website,
            memo: params.memo,
            shippingAddress: params.shippingAddress,
            bankAccountCode: params.bankAccountCode,
            bankAccountName: params.bankAccountName,
            billingUnitName: params.billingUnitName,
            contactPersonName: params.contactPersonName,
            contactPersonPhone: params.contactPersonPhone,
            creditPeriodId: params.creditPeriodId,
            creditQuota: params.creditQuota,
            legalPersonName: params.legalPersonName,
            defaultPaymentMethodId: params.defaultPaymentMethodId,
            defaultShippingMethodId: params.defaultShippingMethodId,
            propertyIds: params.propertyIds,
            contactPersonIds: params.contactPersonIds,
            salesTeamUnitIds: params.salesTeamUnitIds,
            classification: params.classification,
            createdUserId: options?.ctx?.currentUser?.id,
        });
        if (params.primaryUsers) {
            inputs.primaryUsers = params.primaryUsers.map((v) => {
                return new CreateCustomerParams_PrimaryUserParams({
                    salesTeamId: v.salesTeamId,
                    userId: v.userId,
                });
            });
        }
        const row = await this.gRPCrepo.create(inputs);

        const createdRow = await this.findOneOrError({ id: row.result?.data?.id ?? 0 });
        return createdRow;
    }

    async updateByGrpc(params: UpdateInput, options?: { ctx?: Context }): Promise<Customer> {
        const row = await this.findOneOrError({ id: params.id });
        const inputs = new UpdateCustomerParams({
            id: row.id,
            salesTeamGroupId: row.salesTeamGroup.id,
            isGmp: row.isGmp,
            parentId: handler.toNumber({ input: params.parentId, default: row.parent?.id }),
            groupId: handler.toNumber({ input: params.groupId, default: row.group?.id }),
            typeId: handler.toNumber({ input: params.typeId, default: row.type?.id }),
            categoryId: handler.toNumber({ input: params.categoryId, default: row.category?.id }),
            areaId: handler.toNumber({ input: params.areaId, default: row.area?.id }),
            medicalCode: handler.toString({ input: params.medicalCode, default: row.medicalCode }),
            businessCode: handler.toString({
                input: params.businessCode,
                default: row.businessCode,
            }),
            referenceCode: handler.toString({
                input: params.referenceCode,
                default: row.referenceCode,
            }),
            navCode: handler.toString({ input: params.navCode, default: row.navCode }),
            provinceId: handler.toNumber({ input: params.provinceId, default: row.province?.id }),
            cityId: handler.toNumber({ input: params.cityId, default: row.city?.id }),
            districtId: handler.toNumber({ input: params.districtId, default: row.district?.id }),
            address: handler.toString({ input: params.address, default: row.address }),
            email: handler.toString({ input: params.email, default: row.email }),
            fax: handler.toString({ input: params.fax, default: row.fax }),
            name: handler.toString({ input: params.name, default: row.name }),
            shortName: handler.toString({ input: params.shortName, default: row.shortName }),
            phone: handler.toString({ input: params.phone, default: row.phone }),
            mobile: handler.toString({ input: params.mobile, default: row.mobile }),
            website: handler.toString({ input: params.website, default: row.website }),
            memo: handler.toString({ input: params.memo, default: row.memo }),
            shippingAddress: handler.toString({
                input: params.shippingAddress,
                default: row.shippingAddress,
            }),
            bankAccountCode: handler.toString({
                input: params.bankAccountCode,
                default: row.bankAccountCode,
            }),
            bankAccountName: handler.toString({
                input: params.bankAccountName,
                default: row.bankAccountName,
            }),
            billingUnitName: handler.toString({
                input: params.billingUnitName,
                default: row.billingUnitName,
            }),
            creditPeriodId: handler.toNumber({
                input: params.creditPeriodId,
                default: row.creditPeriodId,
            }),
            creditQuota: handler.toDecimal({ input: params.creditQuota, default: row.creditQuota }),
            legalPersonName: handler.toString({
                input: params.legalPersonName,
                default: row.legalPersonName,
            }),
            contactPersonName: handler.toString({
                input: params.contactPersonName,
                default: row.contactPersonName,
            }),
            contactPersonPhone: handler.toString({
                input: params.contactPersonPhone,
                default: row.contactPersonPhone,
            }),
            defaultPaymentMethodId: handler.toNumber({
                input: params.defaultPaymentMethodId,
                default: row.defaultPaymentMethodId,
            }),
            defaultShippingMethodId: handler.toNumber({
                input: params.defaultShippingMethodId,
                default: row.defaultShippingMethodId,
            }),
            classification: handler.toNumber({
                input: params.classification,
            }),
            updatedUserId: options?.ctx?.currentUser?.id,
        });

        if (params.propertyIds) {
            inputs.propertyIds = params.propertyIds;
        } else {
            if (!row.customersProperties.isInitialized()) {
                await row.customersProperties.init();
            }
            const items = row.customersProperties.getItems();
            inputs.propertyIds = items.map((v) => v.customerPropertyId);
        }

        if (params.contactPersonIds) {
            inputs.contactPersonIds = params.contactPersonIds;
        } else {
            if (!row.customersContactPeople.isInitialized()) {
                await row.customersContactPeople.init();
            }
            const items = row.customersContactPeople.getItems();
            inputs.contactPersonIds = items.map((v) => v.contactPersonId);
        }

        if (params.primaryUsers) {
            inputs.primaryUsers = params.primaryUsers.map((v) => {
                return new CreateCustomerParams_PrimaryUserParams({
                    salesTeamId: v.salesTeamId,
                    userId: v.userId,
                });
            });
        } else {
            if (!row.customersPrimaryUsers.isInitialized()) {
                await row.customersPrimaryUsers.init();
            }
            const items = row.customersPrimaryUsers.getItems();
            inputs.primaryUsers = items.map((v) => {
                return new CreateCustomerParams_PrimaryUserParams({
                    salesTeamId: v.salesTeamId,
                    userId: v.userId,
                });
            });
        }
        if (params.salesTeamUnitIds) {
            inputs.salesTeamUnitIds = params.salesTeamUnitIds;
        } else {
            if (!row.customersSalesTeamUnits.isInitialized()) {
                await row.customersSalesTeamUnits.init();
            }
            const items = row.customersSalesTeamUnits.getItems();
            inputs.salesTeamUnitIds = items.map((v) => v.salesTeamUnitId);
        }

        const res = await this.gRPCrepo.update(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteByGrpc(params: { id: number }, options?: { ctx?: Context }): Promise<boolean> {
        const inputs = new DeleteCustomerParams({
            id: params.id,
            deletedUserId: options?.ctx?.currentUser?.id,
        });

        const row = await this.gRPCrepo.delete(inputs);
        return row.success;
    }

    async createContactPersonRelationsByGrpc(
        params: CreateCustomerToContactPeopleInput,
        options?: { ctx?: Context }
    ): Promise<Customer> {
        const inputs = new CreateContactPersonRelationParams({
            id: params.id,
            contactPersonIds: params.contactPersonIds,
        });

        const res = await this.gRPCrepo.createContactPersonRelations(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }

    async deleteContactPersonRelationsByGrpc(
        params: DeleteCustomerToContactPeopleInput,
        options?: { ctx?: Context }
    ): Promise<Customer> {
        const inputs = new DeleteContactPersonRelationParams({
            id: params.id,
            contactPersonIds: params.contactPersonIds,
        });

        const res = await this.gRPCrepo.deleteContactPersonRelations(inputs);
        const updatedRow = await this.findOneOrError({ id: res.result?.data?.id ?? 0 });
        return updatedRow;
    }
}
