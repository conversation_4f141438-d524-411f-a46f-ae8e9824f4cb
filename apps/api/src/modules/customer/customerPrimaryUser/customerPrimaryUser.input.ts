import { Field, ID, InputType, Int } from 'type-graphql';

import { BaseFilterInput } from '~/utils/types/base.type';

@InputType('CustomersPrimaryUserFilterInput')
export class FilterInput extends BaseFilterInput {
    @Field(() => ID, { nullable: true })
    customerId?: number;

    @Field(() => ID, { nullable: true })
    salesTeamId?: number;

    @Field(() => ID, { nullable: true })
    userId?: number;
}
