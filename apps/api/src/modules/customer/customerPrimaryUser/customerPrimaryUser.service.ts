import { CustomersPrimaryUser } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customersPrimaryUser.model';
import { FilterQuery } from '@mikro-orm/core';
import { Service } from 'typedi';

import { Context } from '~/utils/interfaces/apolloContext.interface';
import { BaseService } from '~/utils/providers/base.service';
import { FilterBuilder } from '~/utils/providers/filterBuilder.provider';
import { PaginationInput } from '~/utils/types/base.type';

import { FilterInput } from './customerPrimaryUser.input';

@Service()
export class CustomerPrimaryUserService extends BaseService<CustomersPrimaryUser> {
    protected entity = CustomersPrimaryUser;

    async search(
        params?: FilterInput,
        options?: { ctx?: Context; pagination?: PaginationInput; count?: boolean }
    ): Promise<{ count: number | null; rows: CustomersPrimaryUser[] }> {
        const em = this.em.fork();

        const where: FilterQuery<CustomersPrimaryUser>[] = new FilterBuilder(
            { em, ent: this.entity, filters: params },
            { salesTeamGroup: options?.ctx?.currentSalesTeamGroup }
        ).build();

        if (params?.customerId) {
            where.push({ customer: { id: params.customerId } });
        }
        if (params?.salesTeamId) {
            where.push({ salesTeam: { id: params.salesTeamId } });
        }
        if (params?.userId) {
            where.push({ user: { id: params.userId } });
        }

        const repo = em.getRepository(this.entity);
        const count = options?.count ? await repo.count({ $and: where }) : null;
        const rows = await repo.find(
            { $and: where },
            {
                limit: options?.pagination?.limit,
                offset: options?.pagination?.offset,
                populate: ['customer', 'salesTeam', 'user'],
                orderBy: [{ id: 'DESC' }],
            }
        );

        return { rows, count };
    }
}
