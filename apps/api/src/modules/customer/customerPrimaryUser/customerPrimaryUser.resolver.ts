import type { PublicUser } from '@clinico/mikro-orm-persistence/models/public/user.model';
import type { Customer } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customer.model';
import type { CustomersPrimaryUser } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/customersPrimaryUser.model';
import type { SalesTeam } from '@clinico/mikro-orm-persistence/models/salesRepWorkstation/salesTeam.model';
import { FieldResolver, Resolver, Root } from 'type-graphql';
import { Inject, Service } from 'typedi';

import { SalesTeamService } from '~/modules/salesTeam/salesTeam/salesTeam.service';
import { TSalesTeam } from '~/modules/salesTeam/salesTeam/salesTeam.type';
import { UserService } from '~/modules/user/user/user.service';
import { TUser } from '~/modules/user/user/user.type';

import { CustomerService } from '../customer/customer.service';
import { TCustomer } from '../customer/customer.type';
import { TCustomersPrimaryUser } from './customerPrimaryUser.type';

@Service()
@Resolver(() => TCustomersPrimaryUser)
export class CustomersPrimaryUserResolver {
    @Inject()
    private customerService: CustomerService;
    @FieldResolver(() => TCustomer, { description: '客戶' })
    async customer(@Root() model: CustomersPrimaryUser): Promise<Customer | undefined> {
        return this.customerService.findOne({ id: model.customer.id });
    }

    @Inject()
    private salesTeamService: SalesTeamService;
    @FieldResolver(() => TSalesTeam, { description: '業務團隊', nullable: true })
    async salesTeam(@Root() model: CustomersPrimaryUser): Promise<SalesTeam | undefined> {
        return await this.salesTeamService.findOne({ id: model.salesTeam?.id });
    }

    @Inject()
    private userService: UserService;
    @FieldResolver(() => TUser, { description: '負責業務' })
    async user(@Root() model: CustomersPrimaryUser): Promise<PublicUser | undefined> {
        return await this.userService.findOne({ id: model.user.id });
    }
}
